<?php

namespace App\Exceptions\Search;

use Exception;

abstract class SearchException extends Exception
{
    protected string $searchQuery = '';
    protected string $provider = '';
    protected array $context = [];

    public function __construct(
        string $message = '',
        int $code = 0,
        ?Exception $previous = null,
        string $searchQuery = '',
        string $provider = '',
        array $context = []
    ) {
        parent::__construct($message, $code, $previous);
        
        $this->searchQuery = $searchQuery;
        $this->provider = $provider;
        $this->context = $context;
    }

    /**
     * Get the search query that caused the exception
     */
    public function getSearchQuery(): string
    {
        return $this->searchQuery;
    }

    /**
     * Get the provider that caused the exception
     */
    public function getProvider(): string
    {
        return $this->provider;
    }

    /**
     * Get additional context information
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Convert exception to array for logging/API responses
     */
    public function toArray(): array
    {
        return [
            'type' => static::class,
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'query' => $this->searchQuery,
            'provider' => $this->provider,
            'context' => $this->context,
            'file' => $this->getFile(),
            'line' => $this->getLine(),
        ];
    }
}