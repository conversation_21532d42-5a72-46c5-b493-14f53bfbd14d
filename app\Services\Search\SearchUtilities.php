<?php

namespace App\Services\Search;

use App\ValueObjects\Search\SearchResultItem;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use voku\helper\HTML;
use voku\helper\HtmlDomParser;

class SearchUtilities
{
    /**
     * Default options for HTTP requests
     */
    protected array $httpOptions = [
        'timeout' => 10,
        'connect_timeout' => 5,
        'headers' => [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language' => 'en-US,en;q=0.5',
        ],
    ];

    /**
     * Preprocess and validate a search query
     */
    public function preprocessQuery(string $query): string
    {
        // Trim whitespace and normalize spaces
        $query = trim(preg_replace('/\s+/', ' ', $query));
        
        // Validate query length
        if (mb_strlen($query) < 1) {
            throw new \InvalidArgumentException('Search query cannot be empty');
        }
        
        if (mb_strlen($query) > 500) {
            throw new \InvalidArgumentException('Search query is too long (max 500 characters)');
        }
        
        // Remove potentially malicious content
        $query = $this->sanitizeInput($query);
        
        return $query;
    }
    
    /**
     * Sanitize input to prevent XSS and other attacks
     */
    public function sanitizeInput(string $input): string
    {
        // Convert special characters to HTML entities
        $sanitized = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8', false);
        
        // Remove any remaining HTML tags
        $sanitized = strip_tags($sanitized);
        
        // Trim again after sanitization
        return trim($sanitized);
    }
    
    /**
     * Deduplicate search results based on URL and title similarity
     * 
     * @param SearchResultItem[] $results
     * @param float $similarityThreshold Threshold for considering titles similar (0.0 to 1.0)
     * @return SearchResultItem[]
     */
    public function deduplicateResults(array $results, float $similarityThreshold = 0.9): array
    {
        $uniqueResults = [];
        $seenUrls = [];
        $seenTitles = [];
        
        foreach ($results as $result) {
            $url = $this->normalizeUrl($result->url);
            $title = $this->normalizeText($result->title);
            
            // Skip if we've seen this exact URL before
            if (isset($seenUrls[$url])) {
                continue;
            }
            
            // Check for similar titles
            $isDuplicate = false;
            foreach ($seenTitles as $seenTitle) {
                similar_text($title, $seenTitle, $similarity);
                if ($similarity >= $similarityThreshold * 100) {
                    $isDuplicate = true;
                    break;
                }
            }
            
            if (!$isDuplicate) {
                $uniqueResults[] = $result;
                $seenUrls[$url] = true;
                $seenTitles[] = $title;
            }
        }
        
        return $uniqueResults;
    }
    
    /**
     * Extract content from a URL
     */
    public function extractUrlContent(string $url, array $options = []): ?array
    {
        try {
            // Merge options with defaults
            $options = array_merge($this->httpOptions, $options);
            
            // Make the HTTP request
            $response = Http::withOptions($options)
                ->timeout($options['timeout'])
                ->get($url);
                
            if (!$response->successful()) {
                Log::warning("Failed to fetch URL content: {$url}", [
                    'status' => $response->status(),
                ]);
                return null;
            }
            
            $content = $response->body();
            $contentType = $response->header('Content-Type');
            
            // Only process HTML content
            if (strpos($contentType, 'text/html') === false) {
                return [
                    'title' => basename(parse_url($url, PHP_URL_PATH)) ?: $url,
                    'content' => '',
                    'excerpt' => 'Non-HTML content',
                    'language' => $this->detectLanguage($content),
                ];
            }
            
            // Parse HTML content
            $dom = HtmlDomParser::str_get_html($content);
            
            if (!$dom) {
                return null;
            }
            
            // Extract title
            $title = $dom->find('title', 0);
            $titleText = $title ? $this->cleanText($title->text()) : 'Untitled';
            
            // Extract main content (try to get the main content element)
            $contentText = '';
            $contentElements = $dom->find('article, main, .content, .post, #content');
            
            if (!empty($contentElements)) {
                foreach ($contentElements as $element) {
                    $contentText .= ' ' . $this->cleanText($element->text());
                }
            } else {
                // Fallback to body text if no content elements found
                $body = $dom->find('body', 0);
                $contentText = $body ? $this->cleanText($body->text()) : '';
            }
            
            // Generate excerpt (first 200 chars of content)
            $excerpt = mb_substr($contentText, 0, 200);
            if (mb_strlen($contentText) > 200) {
                $excerpt .= '...';
            }
            
            return [
                'title' => $titleText,
                'content' => $contentText,
                'excerpt' => $excerpt,
                'language' => $this->detectLanguage($contentText),
            ];
            
        } catch (\Exception $e) {
            Log::error("Error extracting URL content: {$url}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return null;
        }
    }
    
    /**
     * Clean and normalize text
     */
    public function cleanText(string $text): string
    {
        // Convert to UTF-8 if not already
        if (!mb_check_encoding($text, 'UTF-8')) {
            $text = mb_convert_encoding($text, 'UTF-8', mb_detect_encoding($text));
        }
        
        // Remove control characters except newlines and tabs
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/u', '', $text);
        
        // Normalize whitespace
        $text = preg_replace('/[\s\p{Z}\p{C}\p{So}]+/u', ' ', $text);
        $text = preg_replace('/\s*[\r\n]+\s*/u', "\n", $text);
        
        return trim($text);
    }
    
    /**
     * Normalize URL for comparison
     */
    public function normalizeUrl(string $url): string
    {
        // Convert to lowercase
        $url = strtolower($url);
        
        // Remove common tracking parameters
        $url = preg_replace('/([?&])(?:utm_[^=]+=[^&]*|fbclid=[^&]*|gclid=[^&]*|msclkid=[^&]*|mc_[a-z]+=[^&]*)+&?/i', '$1', $url);
        
        // Remove trailing slashes and question marks
        $url = rtrim($url, '/?&');
        
        // Remove empty query parameters
        $url = preg_replace('/[?&]+$/', '', $url);
        
        // Normalize URL encoding
        $url = urldecode($url);
        
        return $url;
    }
    
    /**
     * Normalize text for comparison
     */
    public function normalizeText(string $text): string
    {
        // Convert to lowercase and remove extra whitespace
        $text = mb_strtolower(trim($text));
        
        // Remove punctuation and special characters
        $text = preg_replace('/[^\p{L}\p{N}\s]/u', '', $text);
        
        // Normalize whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        
        return $text;
    }
    
    /**
     * Detect language of text
     */
    public function detectLanguage(string $text): string
    {
        // Simple language detection based on common words
        $commonWords = [
            'en' => ['the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i'],
            'es' => ['el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'ser', 'se'],
            'fr' => ['le', 'la', 'de', 'et', 'à', 'les', 'des', 'en', 'un', 'une'],
            'de' => ['der', 'die', 'und', 'in', 'den', 'von', 'zu', 'das', 'mit', 'sich'],
        ];
        
        $text = ' ' . $this->normalizeText($text) . ' ';
        $scores = [];
        
        foreach ($commonWords as $lang => $words) {
            $score = 0;
            foreach ($words as $word) {
                if (strpos($text, " $word ") !== false) {
                    $score++;
                }
            }
            $scores[$lang] = $score;
        }
        
        arsort($scores);
        $detectedLang = key($scores);
        
        // Default to English if no strong match
        return $scores[$detectedLang] > 0 ? $detectedLang : 'en';
    }
    
    /**
     * Highlight query terms in text
     */
    public function highlightTerms(string $text, string $query, string $tag = 'strong'): string
    {
        $terms = $this->extractKeywords($query);
        
        if (empty($terms)) {
            return $text;
        }
        
        // Sort terms by length (longest first) to handle overlapping matches
        usort($terms, function($a, $b) {
            return mb_strlen($b) - mb_strlen($a);
        });
        
        // Escape special regex characters in terms
        $escapedTerms = array_map('preg_quote', $terms);
        $pattern = '/(' . implode('|', $escapedTerms) . ')/iu';
        
        // Highlight matches
        return preg_replace_callback($pattern, function($matches) use ($tag) {
            return "<$tag>" . $matches[1] . "</$tag>";
        }, $text);
    }
    
    /**
     * Extract keywords from query
     */
    public function extractKeywords(string $query): array
    {
        // Remove common stop words
        $stopWords = ['a', 'an', 'and', 'are', 'as', 'at', 'be', 'but', 'by', 'for', 'if', 'in', 'into', 'is', 'it',
            'no', 'not', 'of', 'on', 'or', 'such', 'that', 'the', 'their', 'then', 'there', 'these', 'they', 'this', 'to',
            'was', 'will', 'with', 'what', 'when', 'where', 'who', 'how', 'why', 'which', 'from'];
        
        // Split into words and filter out stop words and short words
        $words = preg_split('/\s+/', $this->normalizeText($query));
        $keywords = array_filter($words, function($word) use ($stopWords) {
            return mb_strlen($word) > 2 && !in_array($word, $stopWords);
        });
        
        // Remove duplicates and reindex
        return array_values(array_unique($keywords));
    }
}
