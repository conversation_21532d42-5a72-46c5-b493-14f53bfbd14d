<?php

namespace App\ValueObjects\Search;

class SearchResultItem
{
    public function __construct(
        public readonly string $title,
        public readonly string $url,
        public readonly string $snippet,
        public readonly string $displayUrl,
        public readonly string $provider,
        public readonly float $relevanceScore = 0.0,
        public readonly array $metadata = [],
        public readonly ?string $imageUrl = null,
        public readonly ?string $publishedDate = null,
        public readonly ?string $author = null
    ) {}

    /**
     * Convert to array for API responses
     */
    public function toArray(): array
    {
        return [
            'title' => $this->title,
            'url' => $this->url,
            'snippet' => $this->snippet,
            'displayUrl' => $this->displayUrl,
            'provider' => $this->provider,
            'relevanceScore' => $this->relevanceScore,
            'metadata' => $this->metadata,
            'imageUrl' => $this->imageUrl,
            'publishedDate' => $this->publishedDate,
            'author' => $this->author,
        ];
    }

    /**
     * Create from array
     */
    public static function fromArray(array $data): self
    {
        return new self(
            title: $data['title'] ?? '',
            url: $data['url'] ?? '',
            snippet: $data['snippet'] ?? '',
            displayUrl: $data['displayUrl'] ?? $data['url'] ?? '',
            provider: $data['provider'] ?? 'unknown',
            relevanceScore: (float) ($data['relevanceScore'] ?? 0.0),
            metadata: $data['metadata'] ?? [],
            imageUrl: $data['imageUrl'] ?? null,
            publishedDate: $data['publishedDate'] ?? null,
            author: $data['author'] ?? null
        );
    }

    /**
     * Check if this result item has valid required fields
     */
    public function isValid(): bool
    {
        return !empty($this->title) && !empty($this->url) && !empty($this->snippet);
    }
}