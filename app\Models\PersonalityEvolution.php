<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PersonalityEvolution extends Model
{
    protected $fillable = [
        'aspect',
        'current_state',
        'evolution_history',
        'stability_score',
        'last_updated_at',
    ];

    protected $casts = [
        'current_state' => 'array',
        'evolution_history' => 'array',
        'stability_score' => 'float',
        'last_updated_at' => 'datetime',
    ];

    /**
     * Update personality aspect
     */
    public function updateAspect(array $newState, string $reason = ''): void
    {
        $history = $this->evolution_history ?? [];
        
        // Add current state to history
        $history[] = [
            'state' => $this->current_state,
            'timestamp' => now()->toISOString(),
            'reason' => $reason,
        ];

        // Keep only last 50 history entries
        if (count($history) > 50) {
            $history = array_slice($history, -50);
        }

        $this->update([
            'current_state' => $newState,
            'evolution_history' => $history,
            'last_updated_at' => now(),
        ]);
    }

    /**
     * Calculate stability based on recent changes
     */
    public function calculateStability(): float
    {
        $history = $this->evolution_history ?? [];
        
        if (count($history) < 2) {
            return 1.0; // Very stable if no changes
        }

        // Look at recent changes (last 10)
        $recentChanges = array_slice($history, -10);
        $changeFrequency = count($recentChanges) / 10;
        
        // More changes = less stability
        return max(0.0, 1.0 - $changeFrequency);
    }

    /**
     * Scope for specific aspect
     */
    public function scopeAspect($query, string $aspect)
    {
        return $query->where('aspect', $aspect);
    }

    /**
     * Scope for stable aspects
     */
    public function scopeStable($query, float $threshold = 0.7)
    {
        return $query->where('stability_score', '>=', $threshold);
    }
}
