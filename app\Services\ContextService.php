<?php

namespace App\Services;

use App\Models\ChatSession;
use App\Models\Message;
use Illuminate\Support\Facades\Log;

class ContextService
{
    private const MAX_CONTEXT_MESSAGES = 20;
    private const CONTEXT_TOKEN_LIMIT = 4000;

    public function buildContextForSession(ChatSession $session, string $currentMessage): array
    {
        try {
            // Get recent conversation history
            $recentMessages = $this->getRecentMessages($session);
            
            // Extract user preferences and patterns
            $userPreferences = $this->extractUserPreferences($session);
            
            // Build context summary
            $contextSummary = $this->buildContextSummary($session, $recentMessages);
            
            // Prepare context for AI model
            $context = [
                'session_info' => [
                    'session_id' => $session->session_id,
                    'personality' => $session->personality_type,
                    'message_count' => $session->messages()->count(),
                    'session_age' => $session->created_at->diffInHours(now()),
                ],
                'user_preferences' => $userPreferences,
                'conversation_summary' => $contextSummary,
                'recent_messages' => $recentMessages,
            ];
            
            return $context;
            
        } catch (\Exception $e) {
            Log::error('Context Service Error', [
                'message' => $e->getMessage(),
                'session_id' => $session->session_id,
            ]);
            
            return [
                'session_info' => [
                    'session_id' => $session->session_id,
                    'personality' => $session->personality_type,
                ],
                'user_preferences' => [],
                'conversation_summary' => '',
                'recent_messages' => [],
            ];
        }
    }

    public function updateSessionContext(ChatSession $session, string $userMessage, string $widdxResponse): void
    {
        try {
            $currentContext = $session->context_data ?? [];
            
            // Update conversation patterns
            $patterns = $this->analyzeConversationPatterns($userMessage, $widdxResponse);
            
            // Update user preferences
            $preferences = $this->updateUserPreferences($currentContext, $userMessage);
            
            // Merge new context data
            $newContextData = array_merge($currentContext, [
                'last_updated' => now()->toISOString(),
                'patterns' => array_merge($currentContext['patterns'] ?? [], $patterns),
                'preferences' => $preferences,
                'interaction_count' => ($currentContext['interaction_count'] ?? 0) + 1,
            ]);
            
            // Keep context data size manageable
            $newContextData = $this->pruneContextData($newContextData);
            
            $session->update(['context_data' => $newContextData]);
            
        } catch (\Exception $e) {
            Log::error('Context Update Error', [
                'message' => $e->getMessage(),
                'session_id' => $session->session_id,
            ]);
        }
    }

    private function getRecentMessages(ChatSession $session): array
    {
        return $session->messages()
            ->orderBy('created_at', 'desc')
            ->limit(self::MAX_CONTEXT_MESSAGES)
            ->get()
            ->reverse()
            ->map(function ($message) {
                return [
                    'role' => $message->role,
                    'content' => $this->truncateContent($message->content, 500),
                    'timestamp' => $message->created_at->toISOString(),
                ];
            })
            ->toArray();
    }

    private function extractUserPreferences(ChatSession $session): array
    {
        $contextData = $session->context_data ?? [];
        
        return [
            'preferred_response_length' => $contextData['preferences']['response_length'] ?? 'medium',
            'communication_style' => $contextData['preferences']['communication_style'] ?? 'balanced',
            'topics_of_interest' => $contextData['preferences']['topics'] ?? [],
            'interaction_patterns' => $contextData['patterns'] ?? [],
        ];
    }

    private function buildContextSummary(ChatSession $session, array $recentMessages): string
    {
        if (empty($recentMessages)) {
            return 'New conversation started.';
        }
        
        $messageCount = count($recentMessages);
        $userMessages = array_filter($recentMessages, fn($msg) => $msg['role'] === 'user');
        $topics = $this->extractTopics($recentMessages);
        
        $summary = "Ongoing conversation with {$messageCount} recent messages. ";
        
        if (!empty($topics)) {
            $summary .= "Main topics discussed: " . implode(', ', array_slice($topics, 0, 3)) . ". ";
        }
        
        $summary .= "User seems engaged and responsive.";
        
        return $summary;
    }

    private function analyzeConversationPatterns(string $userMessage, string $widdxResponse): array
    {
        $patterns = [];
        
        // Analyze message length preferences
        $userLength = strlen($userMessage);
        if ($userLength < 50) {
            $patterns['prefers_short_messages'] = true;
        } elseif ($userLength > 200) {
            $patterns['prefers_detailed_messages'] = true;
        }
        
        // Analyze question patterns
        if (str_contains($userMessage, '?')) {
            $patterns['asks_questions'] = true;
        }
        
        // Analyze politeness
        $politeWords = ['please', 'thank', 'sorry', 'excuse'];
        foreach ($politeWords as $word) {
            if (stripos($userMessage, $word) !== false) {
                $patterns['polite_communication'] = true;
                break;
            }
        }
        
        return $patterns;
    }

    private function updateUserPreferences(array $currentContext, string $userMessage): array
    {
        $preferences = $currentContext['preferences'] ?? [];
        
        // Update response length preference based on user message length
        $messageLength = strlen($userMessage);
        if ($messageLength < 50) {
            $preferences['response_length'] = 'short';
        } elseif ($messageLength > 200) {
            $preferences['response_length'] = 'detailed';
        } else {
            $preferences['response_length'] = 'medium';
        }
        
        // Extract topics from user message
        $topics = $this->extractTopicsFromText($userMessage);
        if (!empty($topics)) {
            $existingTopics = $preferences['topics'] ?? [];
            $preferences['topics'] = array_unique(array_merge($existingTopics, $topics));
            
            // Keep only the most recent 10 topics
            $preferences['topics'] = array_slice($preferences['topics'], -10);
        }
        
        return $preferences;
    }

    private function extractTopics(array $messages): array
    {
        $allText = implode(' ', array_column($messages, 'content'));
        return $this->extractTopicsFromText($allText);
    }

    private function extractTopicsFromText(string $text): array
    {
        // Simple topic extraction - in a real implementation, you might use NLP
        $commonWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'shall', 'a', 'an', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'];
        
        $words = str_word_count(strtolower($text), 1);
        $words = array_filter($words, fn($word) => strlen($word) > 3 && !in_array($word, $commonWords));
        
        $wordCounts = array_count_values($words);
        arsort($wordCounts);
        
        return array_keys(array_slice($wordCounts, 0, 5));
    }

    private function truncateContent(string $content, int $maxLength): string
    {
        if (strlen($content) <= $maxLength) {
            return $content;
        }
        
        return substr($content, 0, $maxLength - 3) . '...';
    }

    private function pruneContextData(array $contextData): array
    {
        // Keep context data size manageable by removing old patterns and limiting arrays
        if (isset($contextData['patterns']) && count($contextData['patterns']) > 20) {
            $contextData['patterns'] = array_slice($contextData['patterns'], -20, 20, true);
        }
        
        if (isset($contextData['preferences']['topics']) && count($contextData['preferences']['topics']) > 10) {
            $contextData['preferences']['topics'] = array_slice($contextData['preferences']['topics'], -10);
        }
        
        return $contextData;
    }
}
