<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Free Search Services
    |--------------------------------------------------------------------------
    */
    'free_search' => [
        'duckduckgo' => [
            'base_url' => 'https://api.duckduckgo.com',
            'enabled' => true,
        ],
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'deepseek' => [
        'api_key' => env('DEEPSEEK_API_KEY'),
        'base_url' => env('DEEPSEEK_BASE_URL', 'https://api.deepseek.com'),
        'timeout' => env('DEEPSEEK_TIMEOUT', 10), // Reduced to 10s for faster response
        'max_retries' => env('DEEPSEEK_MAX_RETRIES', 2), // Reduced retries
        'initial_retry_delay' => env('DEEPSEEK_INITIAL_RETRY_DELAY', 500), // Faster retry
        'backoff_factor' => env('DEEPSEEK_BACKOFF_FACTOR', 1.5),
        'failure_threshold' => env('DEEPSEEK_FAILURE_THRESHOLD', 3), // Lower threshold
        'circuit_cooldown' => env('DEEPSEEK_CIRCUIT_COOLDOWN', 15000), // Shorter cooldown
        'enabled' => env('DEEPSEEK_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Search Services Configuration (Free alternatives only)
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | Image Generation Services Configuration (Gemini only)
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | Voice Services Configuration (Browser-based only)
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | Document Analysis Services Configuration (DeepSeek only)
    |--------------------------------------------------------------------------
    */

    'gemini' => [
        'api_key' => env('GEMINI_API_KEY'),
        'base_url' => env('GEMINI_BASE_URL', 'https://generativelanguage.googleapis.com'),
        'timeout' => env('GEMINI_TIMEOUT', 15), // Reduced to 15s
        'max_retries' => env('GEMINI_MAX_RETRIES', 2), // Reduced retries
        'initial_retry_delay' => env('GEMINI_INITIAL_RETRY_DELAY', 500), // Faster retry
        'backoff_factor' => env('GEMINI_BACKOFF_FACTOR', 1.5),
        'enabled' => env('GEMINI_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | LLM Client Configuration
    |--------------------------------------------------------------------------
    */
    'llm_fallback' => [
        'primary' => env('LLM_PRIMARY', 'deepseek'), // deepseek or gemini
        'fallback_enabled' => env('LLM_FALLBACK_ENABLED', true),
    ],

    'llm_client' => App\Services\ApiFallbackService::class,

];
