# Design Document

## Overview

This design document outlines the refactoring of the current search services architecture to eliminate code duplication, improve maintainability, and create a unified, extensible search system. The current implementation has four separate search services with significant code overlap and inconsistent patterns.

### Current State Analysis

**Existing Services:**
- `FreeSearchService` - Basic DuckDuckGo search with caching
- `LiveSearchService` - Wrapper around FreeSearchService
- `UnlimitedSearchService` - Multi-provider search with parallel processing
- `DeepSearchService` - Advanced search with AI analysis using DeepSeek

**Identified Issues:**
- Code duplication across services (DuckDuckGo implementation repeated)
- Inconsistent caching strategies
- Mixed responsibilities within services
- Difficult to maintain and extend
- No clear service selection logic

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Search Controller] --> B[SearchServiceFactory]
    B --> C{User Tier Check}
    C -->|Free| D[FreeSearchProvider]
    C -->|Premium| E[UnlimitedSearchProvider]
    C -->|Advanced| F[DeepSearchProvider]
    
    D --> G[BaseSearchService]
    E --> G
    F --> G
    
    G --> H[SearchProviderManager]
    G --> I[CacheManager]
    G --> J[SearchUtilities]
    
    H --> K[DuckDuckGoProvider]
    H --> L[SearxProvider]
    H --> M[StartPageProvider]
    
    F --> N[DeepSeekClient]
    F --> O[ContentAnalyzer]
```

### Service Layer Architecture

The new architecture follows a layered approach:

1. **Controller Layer** - API endpoints and request handling
2. **Factory Layer** - Service selection based on user tier
3. **Service Layer** - Business logic and orchestration
4. **Provider Layer** - External API integrations
5. **Utility Layer** - Shared functionality and helpers

## Components and Interfaces

### Core Interfaces

#### SearchServiceInterface
```php
interface SearchServiceInterface
{
    public function search(string $query, array $options = []): SearchResult;
    public function getCapabilities(): array;
    public function isAvailable(): bool;
}
```

#### SearchProviderInterface
```php
interface SearchProviderInterface
{
    public function search(string $query, SearchOptions $options): ProviderResult;
    public function getName(): string;
    public function isConfigured(): bool;
    public function getTimeout(): int;
}
```

#### SearchResult (Value Object)
```php
class SearchResult
{
    public function __construct(
        public readonly bool $success,
        public readonly string $query,
        public readonly array $results,
        public readonly string $provider,
        public readonly ?string $error = null,
        public readonly array $metadata = []
    ) {}
}
```

### Core Components

#### 1. SearchServiceFactory
**Responsibility:** Service selection based on user subscription and requirements

**Key Methods:**
- `createSearchService(User $user, string $searchType): SearchServiceInterface`
- `getAvailableServices(User $user): array`

**Logic:**
- Free users → FreeSearchService
- Premium users → UnlimitedSearchService  
- Advanced users → DeepSearchService
- Fallback to free service on errors

#### 2. BaseSearchService (Abstract)
**Responsibility:** Common functionality shared across all search services

**Key Features:**
- Unified caching strategy
- Error handling and logging
- Result formatting
- Configuration management

**Key Methods:**
- `abstract protected function performSearch(string $query, SearchOptions $options): SearchResult`
- `protected function getCacheKey(string $query, array $options): string`
- `protected function formatResults(array $rawResults): array`

#### 3. SearchProviderManager
**Responsibility:** Managing multiple search providers and their configurations

**Key Features:**
- Provider registration and discovery
- Health checking and failover
- Load balancing across providers
- Provider-specific configuration

**Key Methods:**
- `registerProvider(SearchProviderInterface $provider): void`
- `getAvailableProviders(): array`
- `executeSearch(string $query, SearchOptions $options): ProviderResult`

#### 4. CacheManager
**Responsibility:** Unified caching strategy for all search operations

**Key Features:**
- Consistent cache key generation
- TTL management based on search type
- Cache invalidation strategies
- Performance metrics

**Cache Key Strategy:**
```
search:{service_type}:{query_hash}:{options_hash}
```

#### 5. SearchUtilities
**Responsibility:** Shared utility functions for search operations

**Key Features:**
- Query preprocessing and validation
- Result deduplication
- URL content extraction
- Text processing utilities

### Service Implementations

#### 1. FreeSearchService
**Purpose:** Basic search functionality for free users

**Features:**
- DuckDuckGo integration only
- Basic caching (15 minutes)
- Limited results (10 max)
- Simple error handling

#### 2. UnlimitedSearchService  
**Purpose:** Enhanced search for premium users

**Features:**
- Multiple provider support
- Parallel search execution
- Extended caching (5 minutes for freshness)
- Result deduplication
- Higher result limits (20+ per provider)

#### 3. DeepSearchService
**Purpose:** AI-enhanced search for advanced users

**Features:**
- Query enhancement using DeepSeek
- Content analysis and insights
- Multi-step search process
- Extended caching (30 minutes)
- Comprehensive result analysis

### Provider Implementations

#### 1. DuckDuckGoProvider
**Features:**
- Instant Answer API integration
- Related topics extraction
- Free tier limitations handling

#### 2. SearxProvider
**Features:**
- Self-hosted search engine integration
- Multiple backend aggregation
- Privacy-focused results

#### 3. StartPageProvider
**Features:**
- Google results without tracking
- Enhanced privacy features
- Premium result quality

## Data Models

### SearchOptions (Value Object)
```php
class SearchOptions
{
    public function __construct(
        public readonly int $maxResults = 10,
        public readonly string $language = 'ar',
        public readonly string $region = 'SA',
        public readonly array $providers = ['duckduckgo'],
        public readonly bool $enableCache = true,
        public readonly int $timeout = 30,
        public readonly array $filters = []
    ) {}
}
```

### ProviderResult (Value Object)
```php
class ProviderResult
{
    public function __construct(
        public readonly string $provider,
        public readonly bool $success,
        public readonly array $results,
        public readonly float $responseTime,
        public readonly ?string $error = null
    ) {}
}
```

### SearchResultItem (Value Object)
```php
class SearchResultItem
{
    public function __construct(
        public readonly string $title,
        public readonly string $url,
        public readonly string $snippet,
        public readonly string $displayUrl,
        public readonly string $provider,
        public readonly float $relevanceScore = 0.0,
        public readonly array $metadata = []
    ) {}
}
```

## Error Handling

### Exception Hierarchy
```php
abstract class SearchException extends Exception {}

class SearchProviderException extends SearchException {}
class SearchTimeoutException extends SearchException {}
class SearchRateLimitException extends SearchException {}
class SearchConfigurationException extends SearchException {}
```

### Error Handling Strategy
1. **Provider Level:** Individual provider failures don't break the entire search
2. **Service Level:** Graceful degradation to fallback providers
3. **Factory Level:** Fallback to lower-tier services on failures
4. **Consistent Error Responses:** Standardized error format across all services

## Testing Strategy

### Unit Testing
- **Service Classes:** Mock dependencies, test business logic
- **Provider Classes:** Mock HTTP responses, test parsing logic
- **Utility Classes:** Test pure functions and transformations
- **Value Objects:** Test immutability and validation

### Integration Testing
- **Provider Integration:** Test with real API responses (cached)
- **Service Integration:** Test complete search flows
- **Cache Integration:** Test caching behavior and invalidation

### Performance Testing
- **Response Time:** Ensure search responses under 2 seconds
- **Concurrent Requests:** Test parallel search execution
- **Cache Performance:** Measure cache hit rates and response times
- **Memory Usage:** Monitor memory consumption during searches

## Migration Strategy

### Phase 1: Core Infrastructure
1. Create interfaces and base classes
2. Implement SearchServiceFactory
3. Create CacheManager and SearchUtilities
4. Set up testing framework

### Phase 2: Provider Refactoring
1. Extract DuckDuckGo logic into provider
2. Create SearchProviderManager
3. Implement additional providers
4. Add provider health checking

### Phase 3: Service Refactoring
1. Refactor FreeSearchService to use new architecture
2. Refactor UnlimitedSearchService
3. Refactor DeepSearchService
4. Update LiveSearchService to use factory

### Phase 4: Integration and Testing
1. Update controllers to use factory
2. Comprehensive testing
3. Performance optimization
4. Documentation updates

### Backward Compatibility
- Maintain existing API endpoints
- Preserve response formats
- Keep existing configuration options
- Gradual migration of dependent code

## Performance Considerations

### Caching Strategy
- **Multi-level caching:** Application cache + Redis for distributed caching
- **Cache warming:** Pre-populate cache for common queries
- **Smart invalidation:** Time-based + event-based cache invalidation

### Parallel Processing
- **Provider parallelization:** Execute multiple providers simultaneously
- **Connection pooling:** Reuse HTTP connections
- **Timeout management:** Prevent slow providers from blocking requests

### Resource Management
- **Memory optimization:** Stream large responses, limit result sizes
- **Connection limits:** Manage concurrent connections per provider
- **Rate limiting:** Respect provider rate limits and implement backoff

## Security Considerations

### Input Validation
- Query sanitization and length limits
- Option validation and type checking
- Injection prevention for search parameters

### API Security
- Rate limiting per user/IP
- Authentication for premium features
- Audit logging for search activities

### Data Privacy
- No storage of search queries (except caching)
- Anonymized logging
- Compliance with privacy regulations

## Monitoring and Observability

### Metrics
- Search response times by provider
- Cache hit rates by service type
- Error rates and types
- Provider availability and health

### Logging
- Structured logging with correlation IDs
- Performance metrics logging
- Error tracking with context
- User activity logging (anonymized)

### Alerting
- Provider downtime alerts
- Performance degradation alerts
- Error rate threshold alerts
- Cache performance alerts