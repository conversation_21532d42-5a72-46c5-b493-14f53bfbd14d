<?php

namespace App\Exceptions\Search;

class SearchRateLimitException extends SearchException
{
    private int $retryAfterSeconds;
    private int $currentLimit;
    private int $limitPeriod;

    public function __construct(
        string $message = '',
        int $code = 0,
        ?\Exception $previous = null,
        string $searchQuery = '',
        string $provider = '',
        array $context = [],
        int $retryAfterSeconds = 0,
        int $currentLimit = 0,
        int $limitPeriod = 0
    ) {
        parent::__construct($message, $code, $previous, $searchQuery, $provider, $context);
        $this->retryAfterSeconds = $retryAfterSeconds;
        $this->currentLimit = $currentLimit;
        $this->limitPeriod = $limitPeriod;
    }

    /**
     * Get the number of seconds to wait before retrying
     */
    public function getRetryAfterSeconds(): int
    {
        return $this->retryAfterSeconds;
    }

    /**
     * Get the current rate limit
     */
    public function getCurrentLimit(): int
    {
        return $this->currentLimit;
    }

    /**
     * Get the rate limit period in seconds
     */
    public function getLimitPeriod(): int
    {
        return $this->limitPeriod;
    }

    /**
     * Create rate limit exception for provider
     */
    public static function providerRateLimit(
        string $provider,
        string $query,
        int $retryAfterSeconds = 60,
        int $currentLimit = 0,
        int $limitPeriod = 3600,
        array $context = []
    ): self {
        return new self(
            message: "Rate limit exceeded for provider '{$provider}'. Retry after {$retryAfterSeconds} seconds.",
            code: 3001,
            searchQuery: $query,
            provider: $provider,
            context: $context,
            retryAfterSeconds: $retryAfterSeconds,
            currentLimit: $currentLimit,
            limitPeriod: $limitPeriod
        );
    }

    /**
     * Create rate limit exception for user/service
     */
    public static function serviceRateLimit(
        string $query,
        int $retryAfterSeconds = 60,
        int $currentLimit = 0,
        int $limitPeriod = 3600,
        array $context = []
    ): self {
        return new self(
            message: "Service rate limit exceeded. Retry after {$retryAfterSeconds} seconds.",
            code: 3002,
            searchQuery: $query,
            context: $context,
            retryAfterSeconds: $retryAfterSeconds,
            currentLimit: $currentLimit,
            limitPeriod: $limitPeriod
        );
    }

    /**
     * Convert exception to array with rate limit information
     */
    public function toArray(): array
    {
        $array = parent::toArray();
        $array['retryAfterSeconds'] = $this->retryAfterSeconds;
        $array['currentLimit'] = $this->currentLimit;
        $array['limitPeriod'] = $this->limitPeriod;
        return $array;
    }
}