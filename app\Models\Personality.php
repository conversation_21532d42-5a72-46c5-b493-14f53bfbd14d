<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Personality extends Model
{
    protected $fillable = [
        'name',
        'display_name',
        'description',
        'system_prompt',
        'response_modifiers',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'response_modifiers' => 'array',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
