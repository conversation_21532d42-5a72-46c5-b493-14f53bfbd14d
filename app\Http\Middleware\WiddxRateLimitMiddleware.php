<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class WiddxRateLimitMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!config('widdx.rate_limiting.enabled')) {
            return $next($request);
        }

        // Skip rate limiting for unlimited search endpoints
        if ($this->isUnlimitedSearchRequest($request)) {
            return $next($request);
        }

        // Skip rate limiting for search requests if unlimited search is enabled
        if ($this->isSearchRequest($request) && config('widdx.features.unlimited_search.enabled', true)) {
            if (!config('widdx.features.unlimited_search.rate_limiting_enabled', false)) {
                return $next($request);
            }
        }

        $key = $this->resolveRequestSignature($request);

        // Check per-minute rate limit
        $perMinuteLimit = config('widdx.rate_limiting.max_requests_per_minute', 60);
        if (RateLimiter::tooManyAttempts($key . ':minute', $perMinuteLimit)) {
            return response()->json([
                'success' => false,
                'error' => 'Too many requests. Please slow down.',
                'retry_after' => RateLimiter::availableIn($key . ':minute'),
            ], 429);
        }

        // Check per-hour rate limit
        $perHourLimit = config('widdx.rate_limiting.max_requests_per_hour', 1000);
        if (RateLimiter::tooManyAttempts($key . ':hour', $perHourLimit)) {
            return response()->json([
                'success' => false,
                'error' => 'Hourly rate limit exceeded. Please try again later.',
                'retry_after' => RateLimiter::availableIn($key . ':hour'),
            ], 429);
        }

        // Increment counters
        RateLimiter::hit($key . ':minute', 60); // 1 minute window
        RateLimiter::hit($key . ':hour', 3600); // 1 hour window

        return $next($request);
    }

    /**
     * Resolve the request signature for rate limiting.
     */
    protected function resolveRequestSignature(Request $request): string
    {
        // Use IP address as the primary identifier
        $ip = $request->ip();

        // If user is authenticated, use user ID instead
        if ($request->user()) {
            return 'user:' . $request->user()->id;
        }

        return 'ip:' . $ip;
    }

    /**
     * Check if the request is for unlimited search endpoints
     */
    protected function isUnlimitedSearchRequest(Request $request): bool
    {
        $path = $request->path();

        return str_starts_with($path, 'api/unlimited-search') ||
               str_contains($path, 'unlimited-search');
    }

    /**
     * Check if the request is a search request
     */
    protected function isSearchRequest(Request $request): bool
    {
        $path = $request->path();

        return str_contains($path, '/search') ||
               str_contains($path, '/deep-search') ||
               str_contains($path, '/search-suggestions');
    }
}
