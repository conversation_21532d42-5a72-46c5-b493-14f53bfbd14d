<?php

namespace App\Services;

use App\Exceptions\ImageGenerationException;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use RuntimeException;
use InvalidArgumentException;

class ImageGenerationService
{
    private array $providers;
    private int $timeout;
    private string $defaultProvider;
    private ImageStorageService $storageService;
    private array $fallbackOrder = [];

    public function __construct(ImageStorageService $storageService)
    {
        $this->storageService = $storageService;
        $this->timeout = (int) env('WIDDX_IMAGE_TIMEOUT', 120);
        $this->defaultProvider = env('WIDDX_IMAGE_DEFAULT_PROVIDER', 'freeDescription');
        $this->fallbackOrder = [
            'freeDescription',
            'freeSvg',
            'freePlaceholder'
        ];

        Log::debug('ImageGenerationService initialized', [
            'default_provider' => $this->defaultProvider,
        ]);
    }

    /**
     * Generate image with automatic fallback
     */
    public function generateImage(string $prompt, array $options = []): array
    {
        $provider = $options['provider'] ?? $this->defaultProvider;
        $providersToTry = [$provider];

        // Add fallback providers if not already included
        foreach ($this->fallbackOrder as $fallbackProvider) {
            if (!in_array($fallbackProvider, $providersToTry)) {
                $providersToTry[] = $fallbackProvider;
            }
        }

        $attemptedProviders = [];
        $lastError = null;

        // Try each provider in the fallback order
        foreach ($providersToTry as $currentProvider) {
            try {
                if (in_array($currentProvider, $attemptedProviders)) {
                    continue;
                }

                Log::info('Attempting image generation with provider', [
                    'provider' => $currentProvider,
                    'prompt' => $prompt,
                ]);

                // Route to the appropriate provider
                $method = 'generateWith' . ucfirst($currentProvider);
                if (method_exists($this, $method)) {
                    $result = $this->$method($prompt, $options);

                    Log::info('Image generation completed successfully', [
                        'provider' => $currentProvider,
                        'prompt' => $prompt,
                    ]);

                    return $result;
                } else {
                    throw new InvalidArgumentException("Unsupported image generation provider: {$currentProvider}");
                }

            } catch (Exception $e) {
                $attemptedProviders[] = $currentProvider;
                $lastError = $e;

                Log::warning('Image generation attempt failed', [
                    'provider' => $currentProvider,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // If we get here, all providers failed
        $errorMessage = "All image generation attempts failed. Tried providers: " . implode(', ', $attemptedProviders);
        if ($lastError) {
            $errorMessage .= ". Last error: " . $lastError->getMessage();
        }

        Log::error('All image generation attempts failed', [
            'prompt' => $prompt,
            'attempted_providers' => $attemptedProviders,
            'last_error' => $lastError ? $lastError->getMessage() : null
        ]);

        throw new ImageGenerationException($errorMessage);
    }

    /**
     * Generate free description alternative
     */
    private function generateWithFreeDescription(string $prompt, array $options = []): array
    {
        try {
            $description = $this->generateDetailedDescription($prompt, $options);

            return [
                'success' => true,
                'provider' => 'freeDescription',
                'type' => 'description',
                'images' => [
                    [
                        'url' => null,
                        'description' => $description,
                        'type' => 'text_description',
                        'format' => 'text'
                    ]
                ],
                'metadata' => [
                    'provider' => 'freeDescription',
                    'generated_at' => now()->toISOString(),
                    'is_free_alternative' => true,
                ]
            ];
        } catch (Exception $e) {
            throw new RuntimeException('Free description generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Generate free SVG alternative
     */
    private function generateWithFreeSvg(string $prompt, array $options = []): array
    {
        try {
            $svg = $this->generateSimpleSvg($prompt, $options);

            return [
                'success' => true,
                'provider' => 'freeSvg',
                'type' => 'svg',
                'images' => [
                    [
                        'url' => 'data:image/svg+xml;base64,' . base64_encode($svg),
                        'type' => 'svg_illustration',
                        'format' => 'svg'
                    ]
                ],
                'metadata' => [
                    'provider' => 'freeSvg',
                    'generated_at' => now()->toISOString(),
                    'is_free_alternative' => true,
                ]
            ];
        } catch (Exception $e) {
            throw new RuntimeException('Free SVG generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Generate free placeholder alternative
     */
    private function generateWithFreePlaceholder(string $prompt, array $options = []): array
    {
        try {
            $width = $options['width'] ?? 400;
            $height = $options['height'] ?? 400;

            $placeholderUrl = "https://via.placeholder.com/{$width}x{$height}/cccccc/333333?text=" . urlencode(substr($prompt, 0, 50));

            return [
                'success' => true,
                'provider' => 'freePlaceholder',
                'type' => 'placeholder',
                'images' => [
                    [
                        'url' => $placeholderUrl,
                        'type' => 'placeholder',
                        'format' => 'png',
                        'width' => $width,
                        'height' => $height
                    ]
                ],
                'metadata' => [
                    'provider' => 'freePlaceholder',
                    'generated_at' => now()->toISOString(),
                    'is_free_alternative' => true,
                ]
            ];
        } catch (Exception $e) {
            throw new RuntimeException('Free placeholder generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Generate image using HuggingFace API (currently disabled)
     */
    private function generateWithHuggingface(string $prompt, array $options = []): array
    {
        // HuggingFace is currently disabled in the system
        // This method exists for future implementation
        throw new RuntimeException('HuggingFace image generation is currently disabled. Please use free alternatives or Gemini.');

        /*
        // Future implementation when HuggingFace is enabled:

        $apiKey = env('HUGGINGFACE_API_KEY');
        if (empty($apiKey)) {
            throw new RuntimeException('HuggingFace API key is not configured');
        }

        $model = $options['model'] ?? 'stabilityai/stable-diffusion-xl-base-1.0';
        $url = "https://api-inference.huggingface.co/models/{$model}";

        try {
            $response = Http::withToken($apiKey)
                ->timeout(120)
                ->post($url, [
                    'inputs' => $prompt,
                    'parameters' => [
                        'width' => $options['width'] ?? 1024,
                        'height' => $options['height'] ?? 1024,
                        'num_inference_steps' => $options['steps'] ?? 50,
                        'guidance_scale' => $options['guidance'] ?? 7.5,
                    ]
                ]);

            if ($response->successful()) {
                $imageData = $response->body();
                $filename = 'generated_images/hf_' . uniqid() . '.png';
                $this->storageService->saveImage($imageData, $filename, 'png');

                return [
                    'success' => true,
                    'provider' => 'huggingface',
                    'type' => 'generated',
                    'images' => [
                        [
                            'url' => Storage::url($filename),
                            'type' => 'ai_generated',
                            'format' => 'png'
                        ]
                    ],
                    'metadata' => [
                        'provider' => 'huggingface',
                        'model' => $model,
                        'generated_at' => now()->toISOString(),
                    ]
                ];
            } else {
                throw new RuntimeException('HuggingFace API request failed: ' . $response->body());
            }
        } catch (Exception $e) {
            throw new RuntimeException('HuggingFace image generation failed: ' . $e->getMessage());
        }
        */
    }

    /**
     * Generate detailed description for free alternative
     */
    private function generateDetailedDescription(string $prompt, array $options = []): string
    {
        $language = $options['language'] ?? 'ar';

        if ($language === 'ar') {
            return "وصف تفصيلي للصورة المطلوبة: {$prompt}. هذا وصف نصي بديل لتوليد الصور المجاني يوفر تفاصيل دقيقة عن الصورة المطلوبة.";
        } else {
            return "Detailed description of the requested image: {$prompt}. This is a text-based alternative for free image generation providing detailed information about the requested image.";
        }
    }

    /**
     * Generate simple SVG illustration
     */
    private function generateSimpleSvg(string $prompt, array $options = []): string
    {
        $width = $options['width'] ?? 400;
        $height = $options['height'] ?? 400;

        $svg = <<<SVG
<svg width="{$width}" height="{$height}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="#f0f0f0"/>
    <circle cx="200" cy="200" r="50" fill="#4CAF50"/>
    <text x="200" y="300" text-anchor="middle" font-family="Arial" font-size="14" fill="#333">
        {$prompt}
    </text>
</svg>
SVG;

        return $svg;
    }

    /**
     * Get available image styles and options
     */
    public function getAvailableOptions(): array
    {
        return [
            'providers' => ['freeDescription', 'freeSvg', 'freePlaceholder'],
            'sizes' => [
                'free' => ['400x400', '600x400', '800x600'],
            ],
            'styles' => [
                'free' => ['description', 'svg', 'placeholder'],
            ],
            'quality' => ['standard'],
            'free_alternatives' => [
                'description' => 'Generate detailed text description',
                'svg' => 'Generate simple SVG illustration',
                'placeholder' => 'Generate placeholder image',
            ],
        ];
    }

    /**
     * Filter sensitive options for logging
     */
    private function filterSensitiveOptions(array $options): array
    {
        $sensitiveKeys = ['api_key', 'password', 'secret', 'token'];

        return array_map(function($value, $key) use ($sensitiveKeys) {
            return in_array($key, $sensitiveKeys) ? '[REDACTED]' : $value;
        }, $options, array_keys($options));
    }
}
