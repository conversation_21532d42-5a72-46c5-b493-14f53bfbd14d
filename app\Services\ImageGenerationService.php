<?php

namespace App\Services;

use App\Exceptions\ImageGenerationException;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use RuntimeException;
use InvalidArgumentException;

class ImageGenerationService
{
    private array $providers;
    private int $timeout;
    private string $defaultProvider;
    private ImageStorageService $storageService;
    private array $fallbackOrder = [];

    public function __construct(ImageStorageService $storageService)
    {
        $this->storageService = $storageService;
        
        // Get API keys from environment variables
        $huggingfaceApiKey = env('HUGGINGFACE_API_KEY');
        $geminiApiKey = env('GEMINI_API_KEY');
        $deepseekApiKey = env('DEEPSEEK_API_KEY');
        
        $this->providers = [
            'gemini' => [
                'api_key' => $geminiApiKey,
                'model' => env('GEMINI_IMAGE_MODEL', 'gemini-2.0-flash'),
                'timeout' => (int) env('GEMINI_TIMEOUT', 120),
                'base_url' => 'https://generativelanguage.googleapis.com',
                'supports_arabic' => true
            ],
            'huggingface' => [
                'api_key' => $huggingfaceApiKey,
                'model' => env('HUGGINGFACE_IMAGE_MODEL', 'stabilityai/stable-diffusion-xl-base-1.0'),
                'timeout' => (int) env('HUGGINGFACE_TIMEOUT', 120),
                'base_url' => 'https://api-inference.huggingface.co/models/',
                'supports_arabic' => true
            ],
            'deepseek' => [
                'api_key' => $deepseekApiKey,
                'model' => env('DEEPSEEK_IMAGE_MODEL', 'deepseek-vision'),
                'timeout' => (int) env('DEEPSEEK_TIMEOUT', 120),
                'base_url' => 'https://api.deepseek.com/v1/',
                'supports_arabic' => true
            ]
        ];
        
        $this->timeout = 120; // Default timeout in seconds
        $this->defaultProvider = env('WIDDX_IMAGE_DEFAULT_PROVIDER', 'huggingface');
        $this->fallbackOrder = array_filter([
            $this->defaultProvider,
            'deepseek',
            'huggingface',
            'gemini'
        ], function($provider) {
            return $provider !== $this->defaultProvider;
        });
        
        // Log provider configuration (without sensitive data)
        Log::debug('ImageGenerationService initialized', [
            'default_provider' => $this->defaultProvider,
            'providers_configured' => array_keys($this->providers)
        ]);
    }

    /**
     * Generate image using the configured provider
     */
    public function generateImage(string $prompt, array $options = [])
    {
        $provider = $options['provider'] ?? $this->defaultProvider;
        $attemptedProviders = [];
        $lastError = null;
        
        // Try the requested provider first, then fallback to others
        $providersToTry = array_unique(array_merge([$provider], $this->fallbackOrder));
        
        foreach ($providersToTry as $currentProvider) {
            try {
                // Skip if we've already tried this provider or it's not configured
                if (in_array($currentProvider, $attemptedProviders) || empty($this->providers[$currentProvider]['api_key'])) {
                    continue;
                }
                
                // Log the attempt
                Log::info('Attempting image generation with provider', [
                    'provider' => $currentProvider,
                    'prompt' => $prompt,
                    'options' => $this->filterSensitiveOptions($options),
                ]);
                
                // Validate input
                $this->validateImageRequest($prompt, $options);
                
                // Route to the appropriate provider
                $method = 'generateWith' . ucfirst($currentProvider);
                if (method_exists($this, $method)) {
                    $result = $this->$method($prompt, $options);
                    
                    // If we got here, the generation was successful
                    Log::info('Image generation completed successfully', [
                        'provider' => $currentProvider,
                        'prompt' => $prompt,
                        'result_keys' => array_keys($result),
                    ]);
                    
                    return $result;
                } else {
                    throw new InvalidArgumentException("Unsupported image generation provider: {$currentProvider}");
                }
                
            } catch (Exception $e) {
                $attemptedProviders[] = $currentProvider;
                $lastError = $e;
                
                // Log the error but continue to next provider
                Log::warning('Image generation attempt failed', [
                    'provider' => $currentProvider,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
                
                // Add a small delay between provider attempts
                if (next($providersToTry) !== false) {
                    usleep(500000); // 0.5 second delay
                }
            }
        }
        
        // If we get here, all providers failed
        $errorMessage = 'All image generation attempts failed. ';
        $errorMessage .= 'Tried providers: ' . implode(', ', $attemptedProviders) . '. ';
        $errorMessage .= 'Last error: ' . ($lastError ? $lastError->getMessage() : 'Unknown error');
        
        Log::error('All image generation attempts failed', [
            'prompt' => $prompt,
            'attempted_providers' => $attemptedProviders,
            'last_error' => $lastError ? $lastError->getMessage() : null,
        ]);
        
        throw new ImageGenerationException($errorMessage);
    }

    /**
     * Generate image using Gemini 2.0 Flash Image Generation
     */
    private function generateWithGemini(string $prompt, array $options = []): array
    {
        $config = $this->providers['gemini'];
        $apiKey = $config['api_key'] ?? null;
        $model = $options['model'] ?? $config['model'] ?? 'gemini-2.0-flash';
        $timeout = $options['timeout'] ?? $config['timeout'] ?? 120;

        if (empty($apiKey)) {
            throw new RuntimeException('Gemini API key is not configured');
        }

        // Enhance prompt for better results with non-English languages
        $enhancedPrompt = $this->enhancePromptForGemini($prompt, $options);
        
        $url = "{$config['base_url']}/v1beta/models/{$model}:generateContent";
        
        $payload = [
            'contents' => [
                'parts' => [
                    ['text' => $enhancedPrompt]
                ]
            ],
            'generationConfig' => [
                'temperature' => min(0.9, max(0.1, $options['temperature'] ?? 0.7)),
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 2048,
                'stopSequences' => [],
            ],
            'safetySettings' => [
                [
                    'category' => 'HARM_CATEGORY_HARASSMENT',
                    'threshold' => 'BLOCK_ONLY_HIGH'
                ],
                [
                    'category' => 'HARM_CATEGORY_HATE_SPEECH',
                    'threshold' => 'BLOCK_ONLY_HIGH'
                ],
                [
                    'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                    'threshold' => 'BLOCK_ONLY_HIGH'
                ],
                [
                    'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
                    'threshold' => 'BLOCK_ONLY_HIGH'
                ],
            ],
        ];

        Log::debug('Sending request to Gemini API', [
            'model' => $model,
            'prompt_length' => strlen($enhancedPrompt),
            'options' => $this->filterSensitiveOptions($options),
        ]);

        $startTime = microtime(true);
        $response = Http::timeout($timeout)
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post("$url?key={$apiKey}", $payload);

        $duration = round((microtime(true) - $startTime) * 1000); // in ms
        
        Log::debug('Received response from Gemini API', [
            'status' => $response->status(),
            'duration_ms' => $duration,
            'response_size' => strlen($response->body()),
        ]);

        if ($response->failed()) {
            $errorData = $response->json();
            Log::error('Gemini API request failed', [
                'status' => $response->status(),
                'error' => $errorData['error']['message'] ?? $response->body(),
                'response' => $errorData,
            ]);
            throw new RuntimeException('Gemini API request failed: ' . ($errorData['error']['message'] ?? $response->body()));
        }

        $data = $response->json();
        $images = [];

        if (!empty($data['candidates'][0]['content']['parts'])) {
            foreach ($data['candidates'][0]['content']['parts'] as $index => $part) {
                if (isset($part['inlineData'])) {
                    try {
                        $inlineData = $part['inlineData'];
                        $imageData = base64_decode($inlineData['data']);

                        if ($imageData === false) {
                            Log::warning('Failed to decode image data from Gemini response', [
                                'part_index' => $index,
                                'data_length' => strlen($inlineData['data']),
                            ]);
                            continue;
                        }

                        $mimeType = $inlineData['mimeType'] ?? 'image/png';
                        $extension = $this->getExtensionFromMimeType($mimeType);
                        $fileName = 'generated_images/' . uniqid('gemini_') . $extension;
                        
                        // Store the image
                        $stored = Storage::disk('public')->put($fileName, $imageData);
                        
                        if ($stored === false) {
                            Log::error('Failed to store generated image', [
                                'file_name' => $fileName,
                                'size' => strlen($imageData),
                            ]);
                            continue;
                        }
                        
                        $url = asset('storage/' . ltrim($fileName, '/'));
                        $fileSize = Storage::disk('public')->size($fileName);

                        $images[] = [
                            'url' => $url,
                            'path' => $fileName,
                            'mime_type' => $mimeType,
                            'file_size' => $fileSize,
                            'created_at' => now()->toISOString(),
                            'provider' => 'gemini',
                            'model' => $model,
                        ];
                        
                    } catch (\Exception $e) {
                        Log::error('Error processing Gemini image part', [
                            'part_index' => $index,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                        ]);
                        continue;
                    }
                }
            }
        }

        if (empty($images)) {
            // Log the full response for debugging when no images are generated
            Log::warning('No images were generated by Gemini', [
                'response_data' => $data,
                'prompt' => $prompt,
                'enhanced_prompt' => $enhancedPrompt,
            ]);
            throw new RuntimeException('No images were generated by Gemini. The response did not contain any valid image data.');
        }

        return [
            'success' => true,
            'provider' => 'gemini',
            'model' => $model,
            'prompt' => $prompt,
            'enhanced_prompt' => $enhancedPrompt,
            'images' => $images,
            'metadata' => [
                'temperature' => $options['temperature'] ?? 0.7,
                'model' => $model,
                'provider' => 'gemini',
                'generated_at' => now()->toISOString(),
            ]
        ];
    }

    /**
     * Generate an image using the DeepSeek API
     */
    private function generateWithDeepSeek(string $prompt, array $options = []): array
    {
        $config = $this->providers['deepseek'];
        $apiKey = $config['api_key'] ?? null;
        $model = $options['model'] ?? $config['model'] ?? 'deepseek-vision';
        $timeout = $options['timeout'] ?? $config['timeout'] ?? 120;

        if (empty($apiKey)) {
            throw new RuntimeException('DeepSeek API key is not configured');
        }

        // Enhance prompt for better results with non-English languages
        $enhancedPrompt = $this->enhancePromptForDeepSeek($prompt, $options);
        
        $url = "{$config['base_url']}generate";
        
        $payload = [
            'prompt' => $enhancedPrompt,
            'model' => $model,
            'temperature' => min(0.9, max(0.1, $options['temperature'] ?? 0.7)),
            'topK' => 40,
            'topP' => 0.95,
            'maxOutputTokens' => 2048,
            'stopSequences' => [],
        ];

        Log::debug('Sending request to DeepSeek API', [
            'model' => $model,
            'prompt_length' => strlen($enhancedPrompt),
            'options' => $this->filterSensitiveOptions($options),
        ]);

        $startTime = microtime(true);
        $response = Http::timeout($timeout)
            ->withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => "Bearer {$apiKey}",
            ])
            ->post($url, $payload);

        $duration = round((microtime(true) - $startTime) * 1000); // in ms
        
        Log::debug('Received response from DeepSeek API', [
            'status' => $response->status(),
            'duration_ms' => $duration,
            'response_size' => strlen($response->body()),
        ]);

        if ($response->failed()) {
            $errorData = $response->json();
            Log::error('DeepSeek API request failed', [
                'status' => $response->status(),
                'error' => $errorData['error']['message'] ?? $response->body(),
                'response' => $errorData,
            ]);
            throw new RuntimeException('DeepSeek API request failed: ' . ($errorData['error']['message'] ?? $response->body()));
        }

        $data = $response->json();
        $images = [];

        if (!empty($data['images'])) {
            foreach ($data['images'] as $index => $image) {
                try {
                    $imageData = base64_decode($image['data']);

                    if ($imageData === false) {
                        Log::warning('Failed to decode image data from DeepSeek response', [
                            'part_index' => $index,
                            'data_length' => strlen($image['data']),
                        ]);
                        continue;
                    }

                    $mimeType = $image['mimeType'] ?? 'image/png';
                    $extension = $this->getExtensionFromMimeType($mimeType);
                    $fileName = 'generated_images/' . uniqid('deepseek_') . $extension;
                    
                    // Store the image
                    $stored = Storage::disk('public')->put($fileName, $imageData);
                    
                    if ($stored === false) {
                        Log::error('Failed to store generated image', [
                            'file_name' => $fileName,
                            'size' => strlen($imageData),
                        ]);
                        continue;
                    }
                    
                    $url = asset('storage/' . ltrim($fileName, '/'));
                    $fileSize = Storage::disk('public')->size($fileName);

                    $images[] = [
                        'url' => $url,
                        'path' => $fileName,
                        'mime_type' => $mimeType,
                        'file_size' => $fileSize,
                        'created_at' => now()->toISOString(),
                        'provider' => 'deepseek',
                        'model' => $model,
                    ];
                    
                } catch (\Exception $e) {
                    Log::error('Error processing DeepSeek image part', [
                        'part_index' => $index,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ]);
                    continue;
                }
            }
        }

        if (empty($images)) {
            // Log the full response for debugging when no images are generated
            Log::warning('No images were generated by DeepSeek', [
                'response_data' => $data,
                'prompt' => $prompt,
                'enhanced_prompt' => $enhancedPrompt,
            ]);
            throw new RuntimeException('No images were generated by DeepSeek. The response did not contain any valid image data.');
        }

        return [
            'success' => true,
            'provider' => 'deepseek',
            'model' => $model,
            'prompt' => $prompt,
            'enhanced_prompt' => $enhancedPrompt,
            'images' => $images,
            'metadata' => [
                'temperature' => $options['temperature'] ?? 0.7,
                'model' => $model,
                'provider' => 'deepseek',
                'generated_at' => now()->toISOString(),
            ]
        ];
    }

    /**
     * Enhance prompt for DeepSeek image generation
     */
    private function enhancePromptForDeepSeek(string $prompt, array $options): string
    {
        // Add language-specific enhancements if needed
        $language = $options['target_language'] ?? 'en';
        $isArabic = in_array(strtolower($language), ['ar', 'arabic']);
        
        // Enhance prompt based on language
        if ($isArabic) {
            // For Arabic prompts, add more context and style guidance
            $enhancedPrompt = "$prompt\n\n";
            $enhancedPrompt .= "Please generate a high-quality, detailed image that accurately represents the above Arabic prompt. ";
            $enhancedPrompt .= "Pay attention to cultural context and ensure the image is appropriate and respectful. ";
            $enhancedPrompt .= "The image should be visually stunning with vibrant colors and clear details.";
            
            // Add style preferences if not specified
            if (!isset($options['style'])) {
                $enhancedPrompt .= " Use a photorealistic style with professional lighting and composition.";
            }
            
            return $enhancedPrompt;
        } elseif ($language !== 'en') {
            // For other non-English languages
            return "$prompt\n\n" . 
                "Please generate an image that accurately represents the above prompt while maintaining " .
                "the original language and cultural context. The image should be high quality and visually appealing.";
        }
        
        // For English, just return the original prompt with minimal enhancement
        return $prompt;
    }

    /**
     * Get extension from MIME type
     */
    private function getExtensionFromMimeType(string $mimeType): string
    {
        $mimeMap = [
            'image/jpeg' => '.jpg',
            'image/png' => '.png',
            'image/gif' => '.gif',
            'image/webp' => '.webp',
        ];

        return $mimeMap[strtolower($mimeType)] ?? '.png';
    }

    /**
     * Save image from data to local storage
     */
    private function saveImageFromData(string $imageData, string $provider): string
    {
        try {
            $metadata = [
                'provider' => $provider,
                'generated_at' => now()->toISOString()
            ];
            
            return $this->storageService->saveImage($imageData, $provider, $metadata);
            
        } catch (\Exception $e) {
            Log::error('Failed to save image', [
                'provider' => $provider,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            throw new RuntimeException('Failed to save image: ' . $e->getMessage());
        }
    }

    /**
     * Validate the image generation request
     */
    private function validateImageRequest(string $prompt, array $options): void
    {
        if (empty(trim($prompt))) {
            throw new InvalidArgumentException('Prompt cannot be empty');
        }

        if (mb_strlen($prompt) > 1000) {
            throw new InvalidArgumentException('Prompt is too long (max 1000 characters)');
        }

        // Check size
        $validSizes = ['1024x1024', '1792x1024', '1024x1792'];
        if (isset($options['size']) && !in_array($options['size'], $validSizes)) {
            throw new ImageGenerationException(
                'Invalid image size',
                'حجم الصورة غير صالح. الأحجام المدعومة: ' . implode(', ', $validSizes),
                1009
            );
        }

        // Check style
        $validStyles = ['natural', 'vivid', 'photographic', 'artistic', 'digital-art'];
        if (isset($options['style']) && !in_array($options['style'], $validStyles)) {
            throw new ImageGenerationException(
                'Invalid image style',
                'نمط الصورة غير صالح. الأنماط المدعومة: ' . implode(', ', $validStyles),
                1010
            );
        }

        // Check quality
        $validQualities = ['standard', 'hd'];
        if (isset($options['quality']) && !in_array($options['quality'], $validQualities)) {
            throw new ImageGenerationException(
                'Invalid image quality',
                'جودة الصورة غير صالحة. الجودات المدعومة: ' . implode(', ', $validQualities),
                1011
            );
        }

        // Check count
        if (isset($options['count']) && ($options['count'] < 1 || $options['count'] > 4)) {
            throw new ImageGenerationException(
                'Invalid image count',
                'عدد الصور غير صالح. يجب أن يكون بين 1 و 4.',
                1012
            );
        }
    }

    /**
            'hd' => 'high resolution, ultra detailed, 4K quality',
        ];

        // Add style and quality to the prompt
        $styleEnhancement = $styleEnhancements[$style] ?? '';
        $qualityEnhancement = $qualityEnhancements[$quality] ?? '';

        return "$prompt. $styleEnhancement. $qualityEnhancement";
    }

    /**
     * Filter sensitive options from logging
     */
    private function filterSensitiveOptions(array $options): array
    {
        $sensitiveKeys = ['api_key', 'password', 'secret', 'token'];
        
        return array_map(function($value, $key) use ($sensitiveKeys) {
            if (is_array($value)) {
                return $this->filterSensitiveOptions($value);
            }
            
            // Check if the key contains any sensitive terms
            foreach ($sensitiveKeys as $sensitive) {
                if (stripos($key, $sensitive) !== false) {
                    return '***REDACTED***';
                }
            }
            
            return $value;
        }, $options, array_keys($options));
    }

    /**
     * Enhance prompt for Gemini image generation
     */
    private function enhancePromptForGemini(string $prompt, array $options): string
    {
        // Add language-specific enhancements if needed
        $language = $options['target_language'] ?? 'en';
        $isArabic = in_array(strtolower($language), ['ar', 'arabic']);
        
        // Enhance prompt based on language
        if ($isArabic) {
            // For Arabic prompts, add more context and style guidance
            $enhancedPrompt = "$prompt\n\n";
            $enhancedPrompt .= "Please generate a high-quality, detailed image that accurately represents the above Arabic prompt. ";
            $enhancedPrompt .= "Pay attention to cultural context and ensure the image is appropriate and respectful. ";
            $enhancedPrompt .= "The image should be visually stunning with vibrant colors and clear details.";
            
            // Add style preferences if not specified
            if (!isset($options['style'])) {
                $enhancedPrompt .= " Use a photorealistic style with professional lighting and composition.";
            }
            
            return $enhancedPrompt;
        } elseif ($language !== 'en') {
            // For other non-English languages
            return "$prompt\n\n" . 
                "Please generate an image that accurately represents the above prompt while maintaining " .
                "the original language and cultural context. The image should be high quality and visually appealing.";
        }
        
        // For English, use style and quality enhancements
        $style = $options['style'] ?? 'natural';
        $quality = $options['quality'] ?? 'standard';
        
        $styleEnhancements = [
            'natural' => 'natural style',
            'vivid' => 'vibrant and colorful style',
            'photographic' => 'photorealistic style',
            'artistic' => 'artistic style',
            'digital-art' => 'digital art style',
        ];
        
        $qualityEnhancements = [
            'standard' => 'high quality',
            'hd' => 'ultra high definition, 4K quality',
        ];
        
        $styleText = $styleEnhancements[$style] ?? $styleEnhancements['natural'];
        $qualityText = $qualityEnhancements[$quality] ?? $qualityEnhancements['standard'];
        
        // Create enhanced prompt
        $enhancedPrompt = "$prompt. Style: $styleText. Quality: $qualityText";
        
        // Add additional enhancements if provided
        if (!empty($options['style_enhancements'])) {
            $enhancedPrompt .= ". " . $options['style_enhancements'];
        }
        
        return $enhancedPrompt;
    }

    /**
     * Get available image styles and options
     */
    public function getAvailableOptions(): array
    {
        return [
            'providers' => ['gemini'],
            'sizes' => [
                'gemini' => ['1024x1024', '1792x1024', '1024x1792'],
            ],
            'styles' => [
                'gemini' => ['natural', 'vivid', 'photographic', 'artistic', 'digital-art'],
            ],
            'quality' => ['standard', 'hd'],
        ];
    }

    /**
     * Test image generation functionality
     */
    public function testImageGeneration(): array
    {
        try {
            $testPrompt = 'A beautiful sunset over mountains';
            $result = $this->generateImage($testPrompt, [
                'style' => 'natural',
                'quality' => 'standard',
            ]);

            return [
                'success' => true,
                'test_prompt' => $testPrompt,
                'generation_success' => $result['success'],
                'images_count' => count($result['images'] ?? []),
                'provider' => 'gemini',
                'message' => 'اختبار توليد الصور تم بنجاح',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'فشل في اختبار توليد الصور',
            ];
        }
    }

    /**
     * Get storage statistics
     */
    public function getStorageStats(): array
    {
        try {
            $stats = $this->storageService->getStorageStats();
            
            return [
                'success' => true,
                'stats' => $stats,
                'message' => 'تم جلب إحصائيات التخزين بنجاح'
            ];
            
        } catch (\Exception $e) {
            Log::error('Failed to get storage stats', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'فشل في جلب إحصائيات التخزين'
            ];
        }
    }

    /**
     * Clean up old images
     */
    public function cleanupOldImages(int $daysToKeep = 30): array
    {
        try {
            $result = $this->storageService->cleanupOldImages($daysToKeep);
            
            return [
                'success' => true,
                'deleted_count' => $result['deleted_count'] ?? 0,
                'freed_space' => $result['freed_space'] ?? 0,
                'message' => 'تم تنظيف الصور القديمة بنجاح'
            ];
            
        } catch (\Exception $e) {
            Log::error('Failed to clean up old images', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'فشل في تنظيف الصور القديمة'
            ];
        }
    }
}
