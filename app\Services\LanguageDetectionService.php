<?php

namespace App\Services;

use LanguageDetector\LanguageDetector;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class LanguageDetectionService
{
    private LanguageDetector $detector;

    // Language override patterns that users can use
    private array $languageOverridePatterns = [
        'english' => ['respond in english', 'answer in english', 'reply in english', 'speak english'],
        'spanish' => ['responde en español', 'contesta en español', 'habla español', 'respond in spanish'],
        'french' => ['répondez en français', 'répondre en français', 'respond in french', 'parlez français'],
        'german' => ['antworte auf deutsch', 'respond in german', 'sprechen sie deutsch'],
        'italian' => ['rispondi in italiano', 'respond in italian', 'parla italiano'],
        'portuguese' => ['responda em português', 'respond in portuguese', 'fale português'],
        'russian' => ['отвечай по-русски', 'respond in russian', 'говори по-русски'],
        'chinese' => ['用中文回答', 'respond in chinese', '说中文'],
        'japanese' => ['日本語で答えて', 'respond in japanese', '日本語で話して'],
        'korean' => ['한국어로 대답해', 'respond in korean', '한국어로 말해'],
        'arabic' => ['أجب بالعربية', 'respond in arabic', 'تكلم بالعربية'],
        'hindi' => ['हिंदी में जवाब दें', 'respond in hindi', 'हिंदी में बोलें'],
        'dutch' => ['antwoord in het nederlands', 'respond in dutch', 'spreek nederlands'],
        'swedish' => ['svara på svenska', 'respond in swedish', 'tala svenska'],
        'norwegian' => ['svar på norsk', 'respond in norwegian', 'snakk norsk'],
        'danish' => ['svar på dansk', 'respond in danish', 'tal dansk'],
        'finnish' => ['vastaa suomeksi', 'respond in finnish', 'puhu suomea'],
        'polish' => ['odpowiedz po polsku', 'respond in polish', 'mów po polsku'],
        'czech' => ['odpověz česky', 'respond in czech', 'mluv česky'],
        'hungarian' => ['válaszolj magyarul', 'respond in hungarian', 'beszélj magyarul'],
        'romanian' => ['răspunde în română', 'respond in romanian', 'vorbește română'],
        'bulgarian' => ['отговори на български', 'respond in bulgarian', 'говори български'],
        'greek' => ['απάντησε στα ελληνικά', 'respond in greek', 'μίλα ελληνικά'],
        'turkish' => ['türkçe cevap ver', 'respond in turkish', 'türkçe konuş'],
        'hebrew' => ['ענה בעברית', 'respond in hebrew', 'דבר עברית'],
        'thai' => ['ตอบเป็นภาษาไทย', 'respond in thai', 'พูดภาษาไทย'],
        'vietnamese' => ['trả lời bằng tiếng việt', 'respond in vietnamese', 'nói tiếng việt'],
        'indonesian' => ['jawab dalam bahasa indonesia', 'respond in indonesian', 'bicara bahasa indonesia'],
        'malay' => ['jawab dalam bahasa melayu', 'respond in malay', 'cakap bahasa melayu'],
        'ukrainian' => ['відповідай українською', 'respond in ukrainian', 'говори українською'],
        'croatian' => ['odgovori na hrvatskom', 'respond in croatian', 'govori hrvatski'],
        'serbian' => ['одговори на српском', 'respond in serbian', 'говори српски'],
        'slovenian' => ['odgovori v slovenščini', 'respond in slovenian', 'govori slovensko'],
        'slovak' => ['odpovedz po slovensky', 'respond in slovak', 'hovor po slovensky'],
        'lithuanian' => ['atsakyk lietuviškai', 'respond in lithuanian', 'kalbėk lietuviškai'],
        'latvian' => ['atbildi latviski', 'respond in latvian', 'runā latviski'],
        'estonian' => ['vasta eesti keeles', 'respond in estonian', 'räägi eesti keelt'],
    ];

    // Language code mappings
    private array $languageCodeMap = [
        'en' => 'english',
        'es' => 'spanish',
        'fr' => 'french',
        'de' => 'german',
        'it' => 'italian',
        'pt' => 'portuguese',
        'ru' => 'russian',
        'zh' => 'chinese',
        'ja' => 'japanese',
        'ko' => 'korean',
        'ar' => 'arabic',
        'hi' => 'hindi',
        'nl' => 'dutch',
        'sv' => 'swedish',
        'no' => 'norwegian',
        'da' => 'danish',
        'fi' => 'finnish',
        'pl' => 'polish',
        'cs' => 'czech',
        'hu' => 'hungarian',
        'ro' => 'romanian',
        'bg' => 'bulgarian',
        'el' => 'greek',
        'tr' => 'turkish',
        'he' => 'hebrew',
        'th' => 'thai',
        'vi' => 'vietnamese',
        'id' => 'indonesian',
        'ms' => 'malay',
        'uk' => 'ukrainian',
        'hr' => 'croatian',
        'sr' => 'serbian',
        'sl' => 'slovenian',
        'sk' => 'slovak',
        'lt' => 'lithuanian',
        'lv' => 'latvian',
        'et' => 'estonian',
    ];

    public function __construct()
    {
        $this->detector = new LanguageDetector();
    }

    /**
     * Detect the language of a user message
     */
    public function detectLanguage(string $message): array
    {
        try {
            // First check for language override commands
            $override = $this->checkForLanguageOverride($message);
            if ($override) {
                return [
                    'language' => $override['language'],
                    'language_code' => $override['code'],
                    'confidence' => 1.0,
                    'method' => 'override',
                    'original_message' => $message,
                    'cleaned_message' => $override['cleaned_message'],
                ];
            }

            // Enhanced Arabic detection
            $arabicDetection = $this->detectArabic($message);
            if ($arabicDetection['is_arabic']) {
                return [
                    'language' => 'arabic',
                    'language_code' => 'ar',
                    'confidence' => $arabicDetection['confidence'],
                    'method' => 'arabic_enhanced',
                    'original_message' => $message,
                    'cleaned_message' => $message,
                ];
            }

            // Use automatic detection for other languages
            try {
                $result = $this->detector->evaluate($message);
                $scores = $result->getScores();

                // Find the language with highest score
                $maxScore = 0;
                $detectedCode = 'en';

                foreach ($scores as $langCode => $score) {
                    if ($score > $maxScore) {
                        $maxScore = $score;
                        $detectedCode = $langCode;
                    }
                }

                // Improved confidence thresholds
                if ($maxScore < 0.15) {
                    $detectedCode = 'en'; // Default to English for low confidence
                }

                // Log detection for debugging
                Log::info('Language detection', [
                    'message' => substr($message, 0, 100),
                    'detected_code' => $detectedCode,
                    'max_score' => $maxScore,
                    'top_scores' => array_slice($scores, 0, 3, true)
                ]);

            } catch (\Exception $e) {
                Log::warning('Language detection failed', [
                    'message' => substr($message, 0, 100),
                    'error' => $e->getMessage()
                ]);
                $detectedCode = 'en';
                $maxScore = 0;
            }

            $language = $this->languageCodeMap[$detectedCode] ?? 'english';

            // Use the max score as confidence
            $confidence = $this->calculateConfidence($message, $detectedCode, $maxScore ?? 0);

            return [
                'language' => $language,
                'language_code' => $detectedCode,
                'confidence' => $confidence,
                'method' => 'automatic',
                'original_message' => $message,
                'cleaned_message' => $message,
            ];

        } catch (\Exception $e) {
            Log::warning('Language detection failed', [
                'message' => substr($message, 0, 100),
                'error' => $e->getMessage(),
            ]);

            // Fallback to English
            return [
                'language' => 'english',
                'language_code' => 'en',
                'confidence' => 0.0,
                'method' => 'fallback',
                'original_message' => $message,
                'cleaned_message' => $message,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check if the message contains a language override command
     */
    private function checkForLanguageOverride(string $message): ?array
    {
        $lowerMessage = strtolower($message);

        foreach ($this->languageOverridePatterns as $language => $patterns) {
            foreach ($patterns as $pattern) {
                if (str_contains($lowerMessage, $pattern)) {
                    // Remove the override command from the message
                    $cleanedMessage = $this->removeOverrideCommand($message, $pattern);

                    return [
                        'language' => $language,
                        'code' => array_search($language, $this->languageCodeMap) ?: 'en',
                        'cleaned_message' => $cleanedMessage,
                    ];
                }
            }
        }

        return null;
    }

    /**
     * Remove language override command from message
     */
    private function removeOverrideCommand(string $message, string $pattern): string
    {
        // For Arabic and other non-Latin scripts, word boundaries don't work well
        // Use a more flexible approach
        $quotedPattern = preg_quote($pattern, '/');

        // Try with word boundaries first (for Latin scripts)
        $cleaned = preg_replace('/\b' . $quotedPattern . '\b\.?\s*/iu', '', $message);

        // If that didn't work (e.g., for Arabic), try without word boundaries
        if ($cleaned === $message) {
            $cleaned = preg_replace('/' . $quotedPattern . '\.?\s*/iu', '', $message);
        }

        return trim($cleaned) ?: $message; // Return original if cleaning results in empty string
    }

    /**
     * Enhanced Arabic detection method
     */
    private function detectArabic(string $message): array
    {
        // Check for Arabic Unicode range
        $arabicCharCount = preg_match_all('/[\x{0600}-\x{06FF}]/u', $message);
        $totalChars = mb_strlen($message);

        if ($totalChars === 0) {
            return ['is_arabic' => false, 'confidence' => 0.0];
        }

        $arabicRatio = $arabicCharCount / $totalChars;

        // Common Arabic words and phrases
        $arabicWords = [
            'مرحبا', 'أهلا', 'السلام', 'عليكم', 'كيف', 'حالك', 'شكرا', 'من', 'فضلك',
            'نعم', 'لا', 'هذا', 'هذه', 'ماذا', 'أين', 'متى', 'كيف', 'لماذا', 'من',
            'إلى', 'في', 'على', 'عن', 'مع', 'بدون', 'قبل', 'بعد', 'أثناء',
            'يمكن', 'يجب', 'أريد', 'أحب', 'أفضل', 'أعتقد', 'أعرف', 'أفهم'
        ];

        $arabicWordCount = 0;
        foreach ($arabicWords as $word) {
            if (mb_strpos($message, $word) !== false) {
                $arabicWordCount++;
            }
        }

        // Calculate confidence based on multiple factors
        $confidence = 0.0;

        // Factor 1: Arabic character ratio
        if ($arabicRatio > 0.7) {
            $confidence += 0.8;
        } elseif ($arabicRatio > 0.5) {
            $confidence += 0.6;
        } elseif ($arabicRatio > 0.3) {
            $confidence += 0.4;
        } elseif ($arabicRatio > 0.1) {
            $confidence += 0.2;
        }

        // Factor 2: Arabic words presence
        if ($arabicWordCount > 2) {
            $confidence += 0.3;
        } elseif ($arabicWordCount > 0) {
            $confidence += 0.2;
        }

        // Factor 3: Arabic punctuation and numbers
        if (preg_match('/[\x{061F}\x{060C}\x{06D4}]/u', $message)) { // Arabic question mark, comma, full stop
            $confidence += 0.1;
        }

        // Factor 4: Right-to-left markers
        if (preg_match('/[\x{200F}\x{202E}]/u', $message)) {
            $confidence += 0.1;
        }

        $confidence = min(1.0, $confidence);
        $isArabic = $confidence > 0.5 || $arabicRatio > 0.3;

        return [
            'is_arabic' => $isArabic,
            'confidence' => $confidence,
            'arabic_ratio' => $arabicRatio,
            'arabic_word_count' => $arabicWordCount,
        ];
    }

    /**
     * Calculate confidence score for language detection
     */
    private function calculateConfidence(string $message, string $detectedCode, float $actualScore = 0): float
    {
        // If we have an actual score from the detector, use it as base
        if ($actualScore > 0) {
            $baseConfidence = $actualScore;
        } else {
            // Fallback to simple confidence calculation based on message length
            $messageLength = strlen($message);

            if ($messageLength < 10) {
                $baseConfidence = 0.3; // Low confidence for very short messages
            } elseif ($messageLength < 50) {
                $baseConfidence = 0.6; // Medium confidence for short messages
            } else {
                $baseConfidence = 0.8; // High confidence for longer messages
            }
        }

        // Ensure confidence is between 0 and 1
        return min(1.0, max(0.0, $baseConfidence));
    }

    /**
     * Get language name from code
     */
    public function getLanguageName(string $code): string
    {
        return $this->languageCodeMap[$code] ?? 'english';
    }

    /**
     * Get language code from name
     */
    public function getLanguageCode(string $language): string
    {
        return array_search(strtolower($language), $this->languageCodeMap) ?: 'en';
    }

    /**
     * Check if a language is supported
     */
    public function isLanguageSupported(string $language): bool
    {
        $language = strtolower($language);
        return in_array($language, $this->languageCodeMap) ||
               array_key_exists($language, $this->languageCodeMap);
    }

    /**
     * Get all supported languages
     */
    public function getSupportedLanguages(): array
    {
        return array_unique(array_values($this->languageCodeMap));
    }

    /**
     * Get language display name for UI
     */
    public function getLanguageDisplayName(string $language): string
    {
        $displayNames = [
            'english' => 'English',
            'spanish' => 'Español',
            'french' => 'Français',
            'german' => 'Deutsch',
            'italian' => 'Italiano',
            'portuguese' => 'Português',
            'russian' => 'Русский',
            'chinese' => '中文',
            'japanese' => '日本語',
            'korean' => '한국어',
            'arabic' => 'العربية',
            'hindi' => 'हिंदी',
            'dutch' => 'Nederlands',
            'swedish' => 'Svenska',
            'norwegian' => 'Norsk',
            'danish' => 'Dansk',
            'finnish' => 'Suomi',
            'polish' => 'Polski',
            'czech' => 'Čeština',
            'hungarian' => 'Magyar',
            'romanian' => 'Română',
            'bulgarian' => 'Български',
            'greek' => 'Ελληνικά',
            'turkish' => 'Türkçe',
            'hebrew' => 'עברית',
            'thai' => 'ไทย',
            'vietnamese' => 'Tiếng Việt',
            'indonesian' => 'Bahasa Indonesia',
            'malay' => 'Bahasa Melayu',
            'ukrainian' => 'Українська',
            'croatian' => 'Hrvatski',
            'serbian' => 'Српски',
            'slovenian' => 'Slovenščina',
            'slovak' => 'Slovenčina',
            'lithuanian' => 'Lietuvių',
            'latvian' => 'Latviešu',
            'estonian' => 'Eesti',
        ];

        return $displayNames[strtolower($language)] ?? ucfirst($language);
    }
}
