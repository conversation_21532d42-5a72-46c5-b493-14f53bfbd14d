<?php

namespace App\Http\Controllers;

use App\Services\UnlimitedSearchService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class UnlimitedSearchController extends Controller
{
    private UnlimitedSearchService $unlimitedSearch;

    public function __construct(UnlimitedSearchService $unlimitedSearch)
    {
        $this->unlimitedSearch = $unlimitedSearch;
    }

    /**
     * Perform unlimited search without rate limiting
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'query' => 'required|string|max:1000',
                'providers' => 'sometimes|array',
                'providers.*' => 'string|in:duckduckgo,searx,startpage',
                'max_results' => 'sometimes|integer|min:1|max:100',
                'language' => 'sometimes|string|max:10',
                'region' => 'sometimes|string|max:10',
                'parallel' => 'sometimes|boolean',
                'cache' => 'sometimes|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid request parameters',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $options = [
                'providers' => $request->input('providers', ['duckduckgo']),
                'max_results' => $request->input('max_results', 20),
                'language' => $request->input('language', 'ar'),
                'region' => $request->input('region', 'SA'),
                'parallel' => $request->input('parallel', true),
                'cache' => $request->input('cache', true),
            ];

            $result = $this->unlimitedSearch->search($request->input('query'), $options);

            // Add response metadata
            $result['request_id'] = uniqid('search_', true);
            $result['timestamp'] = now()->toISOString();
            $result['unlimited_mode'] = true;
            $result['rate_limited'] = false;

            return response()->json($result)
                ->header('X-Search-Mode', 'unlimited')
                ->header('X-Rate-Limit-Enabled', 'false')
                ->header('X-Request-ID', $result['request_id']);

        } catch (\Exception $e) {
            Log::error('Unlimited search API error', [
                'query' => $request->input('query'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'فشل في البحث غير المحدود',
                'message' => $e->getMessage(),
                'unlimited_mode' => true,
                'timestamp' => now()->toISOString(),
            ], 500);
        }
    }

    /**
     * Perform bulk search requests
     */
    public function bulkSearch(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'queries' => 'required|array|min:1|max:50',
                'queries.*' => 'required|string|max:500',
                'providers' => 'sometimes|array',
                'providers.*' => 'string|in:duckduckgo,searx,startpage',
                'max_results_per_query' => 'sometimes|integer|min:1|max:20',
                'language' => 'sometimes|string|max:10',
                'region' => 'sometimes|string|max:10',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid bulk search parameters',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $queries = $request->input('queries');
            $options = [
                'providers' => $request->input('providers', ['duckduckgo']),
                'max_results' => $request->input('max_results_per_query', 10),
                'language' => $request->input('language', 'ar'),
                'region' => $request->input('region', 'SA'),
            ];

            $results = [];
            $totalResults = 0;

            foreach ($queries as $index => $query) {
                try {
                    $result = $this->unlimitedSearch->search($query, $options);
                    $results[] = [
                        'query_index' => $index,
                        'query' => $query,
                        'result' => $result,
                    ];
                    $totalResults += count($result['results'] ?? []);
                } catch (\Exception $e) {
                    $results[] = [
                        'query_index' => $index,
                        'query' => $query,
                        'result' => [
                            'success' => false,
                            'error' => $e->getMessage(),
                            'results' => [],
                        ],
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'bulk_search' => true,
                'unlimited_mode' => true,
                'total_queries' => count($queries),
                'total_results' => $totalResults,
                'results' => $results,
                'timestamp' => now()->toISOString(),
            ])->header('X-Search-Mode', 'unlimited-bulk');

        } catch (\Exception $e) {
            Log::error('Bulk unlimited search error', [
                'error' => $e->getMessage(),
                'queries_count' => count($request->input('queries', [])),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'فشل في البحث المجمع غير المحدود',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get search suggestions without rate limiting
     */
    public function suggestions(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'query' => 'required|string|max:200',
                'language' => 'sometimes|string|max:10',
                'max_suggestions' => 'sometimes|integer|min:1|max:20',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid suggestion parameters',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $query = $request->input('query');
            $language = $request->input('language', 'ar');
            $maxSuggestions = $request->input('max_suggestions', 10);

            // Generate suggestions based on query
            $suggestions = $this->generateSuggestions($query, $language, $maxSuggestions);

            return response()->json([
                'success' => true,
                'query' => $query,
                'suggestions' => $suggestions,
                'unlimited_mode' => true,
                'timestamp' => now()->toISOString(),
            ])->header('X-Search-Mode', 'unlimited-suggestions');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'فشل في الحصول على اقتراحات البحث',
                'suggestions' => [],
            ], 500);
        }
    }

    /**
     * Get unlimited search capabilities and status
     */
    public function capabilities(): JsonResponse
    {
        try {
            $capabilities = $this->unlimitedSearch->getCapabilities();
            
            $capabilities['api_endpoints'] = [
                'search' => '/api/unlimited-search',
                'bulk_search' => '/api/unlimited-search/bulk',
                'suggestions' => '/api/unlimited-search/suggestions',
                'capabilities' => '/api/unlimited-search/capabilities',
            ];

            $capabilities['system_status'] = [
                'unlimited_search_enabled' => config('widdx.features.unlimited_search.enabled', true),
                'rate_limiting_disabled' => !config('widdx.features.unlimited_search.rate_limiting_enabled', false),
                'cache_enabled' => config('widdx.features.unlimited_search.cache_enabled', true),
                'parallel_search_enabled' => config('widdx.features.unlimited_search.parallel_enabled', true),
            ];

            return response()->json($capabilities)
                ->header('X-Search-Mode', 'unlimited-capabilities');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'فشل في الحصول على معلومات النظام',
            ], 500);
        }
    }

    /**
     * Generate search suggestions
     */
    private function generateSuggestions(string $query, string $language, int $maxSuggestions): array
    {
        $suggestions = [];

        if ($language === 'ar') {
            $suggestions = [
                $query . ' معلومات',
                $query . ' شرح',
                $query . ' أخبار',
                $query . ' تعريف',
                $query . ' كيفية',
                'ما هو ' . $query,
                'كيف ' . $query,
                'أين ' . $query,
                'متى ' . $query,
                'لماذا ' . $query,
            ];
        } else {
            $suggestions = [
                $query . ' information',
                $query . ' explanation',
                $query . ' news',
                $query . ' definition',
                $query . ' how to',
                'what is ' . $query,
                'how to ' . $query,
                'where is ' . $query,
                'when is ' . $query,
                'why is ' . $query,
            ];
        }

        return array_slice($suggestions, 0, $maxSuggestions);
    }
}
