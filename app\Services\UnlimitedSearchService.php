<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;
use GuzzleHttp\Promise;
use GuzzleHttp\Client;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;

class UnlimitedSearchService
{
    private int $timeout;
    private int $cacheMinutes;
    private bool $parallelEnabled;
    private int $maxParallelRequests;
    private array $searchProviders;

    public function __construct()
    {
        $this->timeout = config('services.deepseek.timeout', 30);
        $this->cacheMinutes = config('widdx.features.unlimited_search.cache_duration', 5);
        $this->parallelEnabled = config('widdx.features.unlimited_search.parallel_enabled', true);
        $this->maxParallelRequests = config('widdx.features.unlimited_search.max_parallel_requests', 10);
        
        $this->searchProviders = [
            'duckduckgo' => 'https://api.duckduckgo.com/',
            'searx' => 'https://searx.be/search',
            'startpage' => 'https://www.startpage.com/sp/search',
        ];
    }

    /**
     * Perform unlimited search with multiple providers and parallel requests
     */
    public function search(string $query, array $options = []): array
    {
        try {
            $cacheKey = 'unlimited_search_' . md5($query . serialize($options));
            
            // Check cache first if enabled
            if (config('widdx.features.unlimited_search.cache_enabled', true)) {
                if ($cached = Cache::get($cacheKey)) {
                    Log::info('Unlimited search cache hit', ['query' => $query]);
                    return $this->addUnlimitedMetadata($cached);
                }
            }

            $maxResults = $options['max_results'] ?? 20;
            $language = $options['language'] ?? 'ar';
            $region = $options['region'] ?? 'SA';
            $providers = $options['providers'] ?? ['duckduckgo'];

            // Perform parallel searches if enabled
            if ($this->parallelEnabled && count($providers) > 1) {
                $results = $this->performParallelSearch($query, $providers, $maxResults, $language);
            } else {
                $results = $this->performSequentialSearch($query, $providers, $maxResults, $language);
            }

            // Merge and deduplicate results
            $mergedResults = $this->mergeAndDeduplicateResults($results, $maxResults);

            $finalResult = [
                'success' => true,
                'provider' => 'unlimited_multi_provider',
                'providers_used' => $providers,
                'query' => $query,
                'total_results' => count($mergedResults),
                'results' => $mergedResults,
                'unlimited_mode' => true,
                'cache_enabled' => config('widdx.features.unlimited_search.cache_enabled', true),
                'parallel_enabled' => $this->parallelEnabled,
            ];

            // Cache the results if enabled
            if (config('widdx.features.unlimited_search.cache_enabled', true)) {
                Cache::put($cacheKey, $finalResult, now()->addMinutes($this->cacheMinutes));
            }

            Log::info('Unlimited search completed', [
                'query' => $query,
                'providers_used' => count($providers),
                'total_results' => count($mergedResults),
                'parallel_mode' => $this->parallelEnabled,
            ]);

            return $this->addUnlimitedMetadata($finalResult);

        } catch (\Exception $e) {
            Log::error('Unlimited search failed', [
                'query' => $query,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'query' => $query,
                'results' => [],
                'unlimited_mode' => true,
                'fallback_used' => true,
            ];
        }
    }

    /**
     * Perform parallel searches across multiple providers
     */
    private function performParallelSearch(string $query, array $providers, int $maxResults, string $language): array
    {
        $client = new Client(['timeout' => $this->timeout]);
        $requests = [];
        $results = [];

        // Prepare requests for each provider
        foreach ($providers as $provider) {
            if (isset($this->searchProviders[$provider])) {
                $requests[$provider] = $this->createSearchRequest($provider, $query, $maxResults, $language);
            }
        }

        // Execute parallel requests
        $pool = new Pool($client, $requests, [
            'concurrency' => min($this->maxParallelRequests, count($requests)),
            'fulfilled' => function ($response, $provider) use (&$results, $query) {
                try {
                    $data = json_decode($response->getBody()->getContents(), true);
                    $results[$provider] = $this->parseProviderResponse($provider, $data, $query);
                } catch (\Exception $e) {
                    Log::warning("Failed to parse response from {$provider}", ['error' => $e->getMessage()]);
                    $results[$provider] = [];
                }
            },
            'rejected' => function ($reason, $provider) use (&$results) {
                Log::warning("Request to {$provider} failed", ['reason' => $reason->getMessage()]);
                $results[$provider] = [];
            },
        ]);

        $promise = $pool->promise();
        $promise->wait();

        return $results;
    }

    /**
     * Perform sequential searches (fallback method)
     */
    private function performSequentialSearch(string $query, array $providers, int $maxResults, string $language): array
    {
        $results = [];

        foreach ($providers as $provider) {
            try {
                if ($provider === 'duckduckgo') {
                    $results[$provider] = $this->searchDuckDuckGo($query, $maxResults, $language);
                } else {
                    $results[$provider] = $this->searchGenericProvider($provider, $query, $maxResults, $language);
                }
            } catch (\Exception $e) {
                Log::warning("Sequential search failed for {$provider}", ['error' => $e->getMessage()]);
                $results[$provider] = [];
            }
        }

        return $results;
    }

    /**
     * Search using DuckDuckGo API (enhanced version)
     */
    private function searchDuckDuckGo(string $query, int $maxResults, string $language): array
    {
        $response = Http::timeout($this->timeout)
            ->get('https://api.duckduckgo.com/', [
                'q' => $query,
                'format' => 'json',
                'no_html' => '1',
                'skip_disambig' => '1',
                'safe_search' => 'moderate',
            ]);

        if (!$response->successful()) {
            throw new \Exception('DuckDuckGo API request failed');
        }

        $data = $response->json();
        return $this->parseProviderResponse('duckduckgo', $data, $query);
    }

    /**
     * Create search request for a specific provider
     */
    private function createSearchRequest(string $provider, string $query, int $maxResults, string $language): Request
    {
        $url = $this->searchProviders[$provider];
        
        switch ($provider) {
            case 'duckduckgo':
                $params = [
                    'q' => $query,
                    'format' => 'json',
                    'no_html' => '1',
                    'skip_disambig' => '1',
                ];
                break;
            default:
                $params = ['q' => $query];
        }

        $fullUrl = $url . '?' . http_build_query($params);
        return new Request('GET', $fullUrl);
    }

    /**
     * Parse response from different providers
     */
    private function parseProviderResponse(string $provider, array $data, string $query): array
    {
        $results = [];

        switch ($provider) {
            case 'duckduckgo':
                // Parse instant answer
                if (!empty($data['Abstract'])) {
                    $results[] = [
                        'title' => $data['Heading'] ?: 'معلومات عن ' . $query,
                        'url' => $data['AbstractURL'] ?: '#',
                        'snippet' => $data['Abstract'],
                        'display_url' => parse_url($data['AbstractURL'] ?: '#', PHP_URL_HOST) ?: 'duckduckgo.com',
                        'formatted_url' => $data['AbstractURL'] ?: '#',
                        'provider' => 'duckduckgo',
                    ];
                }

                // Parse related topics
                if (!empty($data['RelatedTopics'])) {
                    foreach ($data['RelatedTopics'] as $topic) {
                        if (isset($topic['Text']) && isset($topic['FirstURL'])) {
                            $results[] = [
                                'title' => $this->extractTitle($topic['Text']),
                                'url' => $topic['FirstURL'],
                                'snippet' => $topic['Text'],
                                'display_url' => parse_url($topic['FirstURL'], PHP_URL_HOST),
                                'formatted_url' => $topic['FirstURL'],
                                'provider' => 'duckduckgo',
                            ];
                        }
                    }
                }
                break;
        }

        return $results;
    }

    /**
     * Merge and deduplicate results from multiple providers
     */
    private function mergeAndDeduplicateResults(array $providerResults, int $maxResults): array
    {
        $allResults = [];
        $seenUrls = [];

        foreach ($providerResults as $provider => $results) {
            foreach ($results as $result) {
                $url = $result['url'] ?? '';
                
                // Skip duplicates based on URL
                if (!empty($url) && !in_array($url, $seenUrls)) {
                    $seenUrls[] = $url;
                    $allResults[] = $result;
                }
            }
        }

        // Sort by relevance (you can implement custom scoring here)
        usort($allResults, function ($a, $b) {
            // Simple scoring based on title and snippet length
            $scoreA = strlen($a['title'] ?? '') + strlen($a['snippet'] ?? '');
            $scoreB = strlen($b['title'] ?? '') + strlen($b['snippet'] ?? '');
            return $scoreB <=> $scoreA;
        });

        return array_slice($allResults, 0, $maxResults);
    }

    /**
     * Add unlimited search metadata to results
     */
    private function addUnlimitedMetadata(array $result): array
    {
        $result['unlimited_search'] = true;
        $result['rate_limited'] = false;
        $result['timestamp'] = now()->toISOString();
        $result['search_mode'] = 'unlimited';
        
        return $result;
    }

    /**
     * Extract title from text
     */
    private function extractTitle(string $text): string
    {
        $sentences = explode('.', $text);
        return trim($sentences[0]) ?: 'نتيجة البحث';
    }

    /**
     * Get unlimited search capabilities
     */
    public function getCapabilities(): array
    {
        return [
            'unlimited_requests' => true,
            'rate_limiting' => false,
            'parallel_search' => $this->parallelEnabled,
            'max_parallel_requests' => $this->maxParallelRequests,
            'cache_enabled' => config('widdx.features.unlimited_search.cache_enabled', true),
            'cache_duration_minutes' => $this->cacheMinutes,
            'supported_providers' => array_keys($this->searchProviders),
            'features' => [
                'web_search' => 'البحث في الويب بدون حدود',
                'parallel_search' => 'البحث المتوازي عبر عدة مصادر',
                'result_deduplication' => 'إزالة النتائج المكررة',
                'intelligent_caching' => 'التخزين المؤقت الذكي',
            ],
            'limitations' => [
                'يعتمد على مصادر البحث المجانية',
                'قد تختلف جودة النتائج حسب المصدر',
            ],
        ];
    }
}
