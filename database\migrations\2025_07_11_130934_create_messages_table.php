<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chat_session_id')->constrained()->onDelete('cascade');
            $table->enum('role', ['user', 'widdx']); // Only user and WIDDX, never mention other models
            $table->longText('content');
            $table->string('detected_language')->default('english');
            $table->string('detected_language_code')->default('en');
            $table->float('language_confidence')->default(0.0);
            $table->string('language_detection_method')->default('automatic');
            $table->boolean('language_override_used')->default(false);
            $table->json('metadata')->nullable(); // Store processing info, model responses, etc.
            $table->string('personality_applied')->nullable();
            $table->float('processing_time')->nullable(); // Time taken to generate response
            $table->timestamps();

            $table->index(['chat_session_id', 'created_at']);
            $table->index('role');
            $table->index(['detected_language_code', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
