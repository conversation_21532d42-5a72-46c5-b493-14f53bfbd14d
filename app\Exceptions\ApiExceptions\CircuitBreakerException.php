<?php

namespace App\Exceptions\ApiExceptions;

use Exception;

class CircuitBreakerException extends Exception
{
    protected $code = 503;
    protected $message = 'API circuit breaker is open';

    public function __construct(?string $message = null, ?int $retryAfter = null)
    {
        if ($message !== null) {
            $this->message = $message;
        }

        if ($retryAfter !== null) {
            $this->message .= " - Retry after: {$retryAfter}ms";
        }

        parent::__construct($this->message, $this->code);
    }
}
