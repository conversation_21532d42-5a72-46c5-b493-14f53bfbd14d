<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class UnlimitedSearchMiddleware
{
    /**
     * Handle an incoming request for unlimited search.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if unlimited search is enabled
        if (!config('widdx.features.unlimited_search.enabled', true)) {
            return response()->json([
                'success' => false,
                'error' => 'Unlimited search is currently disabled.',
                'message' => 'البحث غير المحدود معطل حالياً.',
            ], 503);
        }

        // Log the search request for monitoring
        Log::info('Unlimited search request', [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'query' => $request->input('query'),
            'timestamp' => now()->toISOString(),
        ]);

        // Add headers to indicate unlimited search
        $response = $next($request);
        
        if ($response instanceof \Illuminate\Http\JsonResponse) {
            $response->header('X-Search-Mode', 'unlimited');
            $response->header('X-Rate-Limit-Enabled', 'false');
            $response->header('X-Search-Provider', config('widdx.features.live_search.default_provider', 'duckduckgo'));
        }

        return $response;
    }

    /**
     * Check if the request should bypass rate limiting
     */
    protected function shouldBypassRateLimit(Request $request): bool
    {
        // Always bypass rate limiting for search requests when unlimited search is enabled
        return config('widdx.features.unlimited_search.enabled', true) && 
               !config('widdx.features.unlimited_search.rate_limiting_enabled', false);
    }

    /**
     * Log search analytics for monitoring
     */
    protected function logSearchAnalytics(Request $request): void
    {
        try {
            $analytics = [
                'timestamp' => now()->toISOString(),
                'ip' => $request->ip(),
                'query' => $request->input('query'),
                'provider' => $request->input('provider', 'duckduckgo'),
                'max_results' => $request->input('max_results', 10),
                'language' => $request->input('language', 'ar'),
                'region' => $request->input('region', 'SA'),
                'user_agent' => $request->userAgent(),
                'referer' => $request->header('referer'),
            ];

            Log::channel('search')->info('Search analytics', $analytics);
        } catch (\Exception $e) {
            Log::warning('Failed to log search analytics', ['error' => $e->getMessage()]);
        }
    }
}
