<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ChatRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'message' => 'required|string|max:10000',
            'session_id' => 'nullable|string|max:255',
            'personality' => 'nullable|string|in:neutral,witty,sarcastic,formal,casual',
            'think_mode' => 'nullable|boolean',
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|max:10240|mimes:jpg,jpeg,png,pdf,txt,doc,docx',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'message.required' => 'الرسالة مطلوبة',
            'message.max' => 'يجب ألا تتجاوز الرسالة 10000 حرف',
            'attachments.*.max' => 'يجب ألا يتجاوز حجم الملف 10 ميجابايت',
            'attachments.*.mimes' => 'نوع الملف غير مدعوم. الأنواع المدعومة: jpg, jpeg, png, pdf, txt, doc, docx',
        ];
    }
}
