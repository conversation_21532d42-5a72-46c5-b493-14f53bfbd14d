<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\WiddxIdentityService;
use App\Services\WiddxKnowledgeService;
use App\Services\WiddxLearningService;
use App\Services\DeepSeekClient;
use App\Services\GeminiClient;

class WiddxServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register WIDDX core services
        $this->app->singleton(WiddxKnowledgeService::class, function ($app) {
            return new WiddxKnowledgeService();
        });

        $this->app->singleton(WiddxLearningService::class, function ($app) {
            return new WiddxLearningService(
                $app->make(DeepSeekClient::class),
                $app->make(GeminiClient::class),
                $app->make(WiddxKnowledgeService::class)
            );
        });

        $this->app->singleton(WiddxIdentityService::class, function ($app) {
            return new WiddxIdentityService(
                $app->make(WiddxKnowledgeService::class),
                $app->make(WiddxLearningService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register any boot-time logic here
        $this->registerCommands();
        $this->scheduleCleanupTasks();
    }

    /**
     * Register WIDDX-specific commands
     */
    private function registerCommands(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                // Add custom commands here if needed
            ]);
        }
    }

    /**
     * Schedule cleanup tasks
     */
    private function scheduleCleanupTasks(): void
    {
        // This would typically be done in the Console/Kernel.php schedule method
        // but we can set up the logic here for reference
    }
}
