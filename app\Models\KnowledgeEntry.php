<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class KnowledgeEntry extends Model
{
    protected $fillable = [
        'topic',
        'category',
        'content',
        'metadata',
        'language',
        'confidence_score',
        'usage_count',
        'last_used_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'confidence_score' => 'float',
        'usage_count' => 'integer',
        'last_used_at' => 'datetime',
    ];

    /**
     * Increment usage count and update last used timestamp
     */
    public function recordUsage(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Update confidence score based on reinforcement or contradiction
     */
    public function updateConfidence(float $adjustment): void
    {
        $newScore = max(0.0, min(1.0, $this->confidence_score + $adjustment));
        $this->update(['confidence_score' => $newScore]);
    }

    /**
     * Scope for high confidence knowledge
     */
    public function scopeHighConfidence($query, float $threshold = 0.7)
    {
        return $query->where('confidence_score', '>=', $threshold);
    }

    /**
     * Scope for frequently used knowledge
     */
    public function scopeFrequentlyUsed($query, int $minUsage = 5)
    {
        return $query->where('usage_count', '>=', $minUsage);
    }

    /**
     * Scope for specific category
     */
    public function scopeCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for specific language
     */
    public function scopeLanguage($query, string $language)
    {
        return $query->where('language', $language);
    }

    /**
     * Search knowledge by topic or content
     */
    public function scopeSearch($query, string $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            $q->where('topic', 'like', "%{$searchTerm}%")
              ->orWhere('content', 'like', "%{$searchTerm}%");
        });
    }

    /**
     * Get related knowledge entries
     */
    public function getRelatedKnowledge(int $limit = 5)
    {
        return static::where('id', '!=', $this->id)
            ->where(function ($query) {
                $query->where('topic', 'like', "%{$this->topic}%")
                      ->orWhere('category', $this->category);
            })
            ->orderByDesc('confidence_score')
            ->orderByDesc('usage_count')
            ->limit($limit)
            ->get();
    }
}
