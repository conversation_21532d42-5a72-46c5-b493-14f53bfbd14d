<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Knowledge entries - WIDDX's learned information
        Schema::create('knowledge_entries', function (Blueprint $table) {
            $table->id();
            $table->string('topic')->index(); // Main topic/subject
            $table->string('category')->index(); // Category like 'user_preference', 'fact', 'pattern'
            $table->text('content'); // The actual knowledge content
            $table->json('metadata')->nullable(); // Additional context, sources, confidence
            $table->string('language')->default('en')->index(); // Language of the knowledge
            $table->float('confidence_score')->default(0.5); // How confident WIDDX is about this knowledge
            $table->integer('usage_count')->default(0); // How often this knowledge is referenced
            $table->timestamp('last_used_at')->nullable();
            $table->timestamps();

            $table->index(['topic', 'category']);
            $table->index(['confidence_score', 'usage_count']);
        });

        // Conversation insights - What WIDDX learns from conversations
        Schema::create('conversation_insights', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chat_session_id')->constrained()->onDelete('cascade');
            $table->string('insight_type')->index(); // 'user_pattern', 'topic_interest', 'communication_style'
            $table->text('insight_content');
            $table->json('supporting_data')->nullable(); // Evidence supporting this insight
            $table->float('strength')->default(0.5); // How strong this insight is (0-1)
            $table->string('language')->default('en');
            $table->timestamps();

            $table->index(['insight_type', 'strength']);
        });

        // User patterns - How users typically interact with WIDDX
        Schema::create('user_patterns', function (Blueprint $table) {
            $table->id();
            $table->string('user_identifier')->index(); // IP or user ID
            $table->string('pattern_type')->index(); // 'communication_style', 'topic_preference', 'language_preference'
            $table->json('pattern_data'); // The actual pattern data
            $table->float('confidence')->default(0.5);
            $table->integer('occurrence_count')->default(1);
            $table->timestamp('last_observed_at');
            $table->timestamps();

            $table->index(['user_identifier', 'pattern_type']);
        });

        // Topic relationships - How different topics relate to each other
        Schema::create('topic_relationships', function (Blueprint $table) {
            $table->id();
            $table->string('topic_a')->index();
            $table->string('topic_b')->index();
            $table->string('relationship_type')->index(); // 'related', 'opposite', 'prerequisite'
            $table->float('strength')->default(0.5); // How strong the relationship is
            $table->json('context')->nullable(); // Context where this relationship was observed
            $table->timestamps();

            $table->unique(['topic_a', 'topic_b', 'relationship_type']);
        });

        // WIDDX personality evolution - How WIDDX's personality develops over time
        Schema::create('personality_evolutions', function (Blueprint $table) {
            $table->id();
            $table->string('aspect')->index(); // 'humor_style', 'formality_level', 'expertise_area'
            $table->json('current_state'); // Current personality state for this aspect
            $table->json('evolution_history')->nullable(); // How it has changed over time
            $table->float('stability_score')->default(0.5); // How stable this aspect is
            $table->timestamp('last_updated_at');
            $table->timestamps();
        });

        // Learning sessions - Track what WIDDX learns in each interaction
        Schema::create('learning_sessions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chat_session_id')->constrained()->onDelete('cascade');
            $table->json('learned_items'); // What was learned in this session
            $table->json('reinforced_knowledge')->nullable(); // Existing knowledge that was reinforced
            $table->json('contradicted_knowledge')->nullable(); // Knowledge that was contradicted
            $table->float('learning_score')->default(0.0); // How much was learned (0-1)
            $table->string('primary_language')->default('en');
            $table->timestamps();
        });

        // Context memories - Important context that WIDDX should remember
        Schema::create('context_memories', function (Blueprint $table) {
            $table->id();
            $table->string('memory_type')->index(); // 'user_context', 'conversation_context', 'global_context'
            $table->string('key')->index(); // Memory key/identifier
            $table->json('value'); // Memory content
            $table->string('scope')->default('global'); // 'global', 'user', 'session'
            $table->string('scope_identifier')->nullable(); // User ID or session ID if scoped
            $table->float('importance')->default(0.5); // How important this memory is
            $table->timestamp('expires_at')->nullable(); // When this memory should expire
            $table->timestamps();

            $table->index(['memory_type', 'scope']);
            $table->index(['key', 'scope_identifier']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('context_memories');
        Schema::dropIfExists('learning_sessions');
        Schema::dropIfExists('personality_evolutions');
        Schema::dropIfExists('topic_relationships');
        Schema::dropIfExists('user_patterns');
        Schema::dropIfExists('conversation_insights');
        Schema::dropIfExists('knowledge_entries');
    }
};
