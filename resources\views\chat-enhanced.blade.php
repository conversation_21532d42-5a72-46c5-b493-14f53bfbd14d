<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>WIDDX AI - Autonomous Intelligent Assistant</title>

    {{-- Fonts --}}
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

    {{-- Icons --}}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    {{-- Tailwind CSS CDN --}}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'widdx': {
                            'primary': '#3b82f6',
                            'primary-hover': '#2563eb',
                            'secondary': '#8b5cf6',
                            'accent': '#06b6d4',
                            'success': '#10b981',
                            'warning': '#f59e0b',
                            'error': '#ef4444',
                        },
                        'widdx-bg': {
                            'primary': '#0f0f0f',
                            'secondary': '#1a1a1a',
                            'tertiary': '#262626',
                            'elevated': '#2d2d2d',
                            'hover': '#3a3a3a',
                            'glass': 'rgba(26, 26, 26, 0.8)',
                        },
                        'widdx-text': {
                            'primary': '#ffffff',
                            'secondary': '#d4d4d8',
                            'tertiary': '#a1a1aa',
                            'muted': '#71717a',
                            'accent': '#3b82f6',
                        },
                        'widdx-border': {
                            'primary': '#404040',
                            'secondary': '#525252',
                            'accent': '#3b82f6',
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'mono': ['JetBrains Mono', 'monospace'],
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-subtle': 'bounce 2s infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'slide-down': 'slideDown 0.3s ease-out',
                        'fade-in': 'fadeIn 0.5s ease-out',
                        'scale-in': 'scaleIn 0.2s ease-out',
                    },
                    keyframes: {
                        glow: {
                            '0%': { boxShadow: '0 0 5px #3b82f6, 0 0 10px #3b82f6, 0 0 15px #3b82f6' },
                            '100%': { boxShadow: '0 0 10px #3b82f6, 0 0 20px #3b82f6, 0 0 30px #3b82f6' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        slideDown: {
                            '0%': { opacity: '0', transform: 'translateY(-20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        scaleIn: {
                            '0%': { opacity: '0', transform: 'scale(0.9)' },
                            '100%': { opacity: '1', transform: 'scale(1)' }
                        }
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>

    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
            color: #ffffff;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .widdx-app {
            display: flex;
            height: 100vh;
            width: 100vw;
            position: relative;
        }

        /* Glass morphism effect */
        .glass {
            background: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(64, 64, 64, 0.3);
        }

        /* Enhanced scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #1a1a1a;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #2563eb, #7c3aed);
        }

        /* Gradient text */
        .gradient-text {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Animated background */
        .animated-bg {
            background: linear-gradient(-45deg, #0f0f0f, #1a1a1a, #262626, #1a1a1a);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Enhanced message bubbles */
        .message-bubble {
            position: relative;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(64, 64, 64, 0.3);
        }

        .message-bubble::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
            border-radius: inherit;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .message-bubble:hover::before {
            opacity: 1;
        }

        /* Status indicators */
        .status-indicator {
            position: relative;
            display: inline-block;
        }

        .status-indicator::after {
            content: '';
            position: absolute;
            top: -2px;
            right: -2px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            border: 2px solid #0f0f0f;
        }

        .status-indicator.online::after {
            background: #10b981;
            animation: pulse 2s infinite;
        }

        .status-indicator.learning::after {
            background: #f59e0b;
            animation: pulse 2s infinite;
        }

        .status-indicator.thinking::after {
            background: #8b5cf6;
            animation: pulse 1s infinite;
        }

        /* Floating action button */
        .fab {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(59, 130, 246, 0.6);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .widdx-sidebar {
                position: fixed;
                top: 0;
                left: 0;
                height: 100%;
                z-index: 50;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .widdx-sidebar.open {
                transform: translateX(0);
            }

            .fab {
                bottom: 1rem;
                right: 1rem;
                width: 48px;
                height: 48px;
            }
        }

        /* Loading animations */
        .loading-dots {
            display: inline-flex;
            gap: 4px;
        }

        .loading-dots span {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #3b82f6;
            animation: loadingDots 1.4s infinite ease-in-out;
        }

        .loading-dots span:nth-child(1) { animation-delay: 0s; }
        .loading-dots span:nth-child(2) { animation-delay: 0.2s; }
        .loading-dots span:nth-child(3) { animation-delay: 0.4s; }

        @keyframes loadingDots {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        /* Notification styles */
        .notification {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1000;
            max-width: 400px;
            padding: 1rem;
            border-radius: 0.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(64, 64, 64, 0.3);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: rgba(16, 185, 129, 0.2);
            border-color: rgba(16, 185, 129, 0.3);
            color: #10b981;
        }

        .notification.error {
            background: rgba(239, 68, 68, 0.2);
            border-color: rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }

        .notification.info {
            background: rgba(59, 130, 246, 0.2);
            border-color: rgba(59, 130, 246, 0.3);
            color: #3b82f6;
        }

        .notification.warning {
            background: rgba(245, 158, 11, 0.2);
            border-color: rgba(245, 158, 11, 0.3);
            color: #f59e0b;
        }
    </style>
</head>
<body class="animated-bg">
    <div class="widdx-app">
        {{-- Enhanced Sidebar --}}
        <aside id="widdx-sidebar" class="widdx-sidebar w-80 glass flex-shrink-0 transition-transform duration-300">
            <div class="p-6 h-full flex flex-col">
                {{-- Header --}}
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-full bg-gradient-to-br from-widdx-primary to-widdx-secondary flex items-center justify-center status-indicator online">
                            <i class="fas fa-brain text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold gradient-text">WIDDX AI</h1>
                            <p class="text-xs text-widdx-text-muted">Autonomous Assistant</p>
                        </div>
                    </div>
                    <button id="sidebar-close" class="text-widdx-text-tertiary hover:text-widdx-text-primary lg:hidden">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>

                {{-- WIDDX Status Panel --}}
                <div class="glass rounded-lg p-4 mb-6 border border-widdx-border-primary">
                    <h3 class="text-sm font-semibold text-widdx-text-secondary mb-3 flex items-center">
                        <i class="fas fa-chart-line text-widdx-accent mr-2"></i>
                        WIDDX Status
                    </h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-widdx-text-tertiary">Learning Level</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-16 h-1 bg-widdx-bg-tertiary rounded-full overflow-hidden">
                                    <div class="h-full bg-gradient-to-r from-widdx-primary to-widdx-secondary rounded-full" style="width: 75%"></div>
                                </div>
                                <span class="text-xs text-widdx-text-secondary">75%</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-widdx-text-tertiary">Knowledge Base</span>
                            <span class="text-xs text-widdx-success" id="knowledge-count">1,247 entries</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-widdx-text-tertiary">Personality</span>
                            <span class="text-xs text-widdx-accent" id="personality-status">Evolving</span>
                        </div>
                    </div>
                </div>

                {{-- Quick Actions --}}
                <div class="mb-6">
                    <button id="new-chat-btn" class="w-full bg-gradient-to-r from-widdx-primary to-widdx-secondary hover:from-widdx-primary-hover hover:to-widdx-secondary text-white py-3 px-4 rounded-lg mb-3 transition-all duration-200 flex items-center justify-center space-x-2">
                        <i class="fas fa-plus"></i>
                        <span>New Chat</span>
                    </button>
                    
                    <div class="grid grid-cols-2 gap-2">
                        <button id="image-gen-btn" class="glass p-3 rounded-lg hover:bg-widdx-bg-hover transition-colors text-center">
                            <i class="fas fa-image text-widdx-accent mb-1"></i>
                            <p class="text-xs text-widdx-text-tertiary">Generate</p>
                        </button>
                        <button id="search-btn" class="glass p-3 rounded-lg hover:bg-widdx-bg-hover transition-colors text-center">
                            <i class="fas fa-search text-widdx-warning mb-1"></i>
                            <p class="text-xs text-widdx-text-tertiary">Search</p>
                        </button>
                        <button id="analytics-btn" class="glass p-3 rounded-lg hover:bg-widdx-bg-hover transition-colors text-center">
                            <i class="fas fa-chart-bar text-widdx-success mb-1"></i>
                            <p class="text-xs text-widdx-text-tertiary">Analytics</p>
                        </button>
                        <button id="settings-btn" class="glass p-3 rounded-lg hover:bg-widdx-bg-hover transition-colors text-center">
                            <i class="fas fa-cog text-widdx-text-tertiary mb-1"></i>
                            <p class="text-xs text-widdx-text-tertiary">Settings</p>
                        </button>
                    </div>
                </div>

                {{-- Recent Chats --}}
                <div class="flex-1 overflow-hidden">
                    <h3 class="text-sm font-semibold text-widdx-text-secondary mb-3 flex items-center">
                        <i class="fas fa-history text-widdx-text-muted mr-2"></i>
                        Recent Chats
                    </h3>
                    <div class="space-y-2 overflow-y-auto" id="recent-chats">
                        <div class="glass rounded-lg p-3 hover:bg-widdx-bg-hover transition-colors cursor-pointer">
                            <div class="flex items-center space-x-2 mb-1">
                                <div class="w-2 h-2 bg-widdx-success rounded-full"></div>
                                <span class="text-sm text-widdx-text-primary">Current Chat</span>
                            </div>
                            <p class="text-xs text-widdx-text-muted">Just now</p>
                        </div>
                    </div>
                </div>

                {{-- Language & API Status --}}
                <div class="mt-4 pt-4 border-t border-widdx-border-primary">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-xs text-widdx-text-tertiary">Language</span>
                        <span class="text-xs text-widdx-text-secondary" id="current-language">Auto-detect</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-xs text-widdx-text-tertiary">API Status</span>
                        <div class="flex space-x-1">
                            <div class="w-2 h-2 bg-widdx-success rounded-full" title="Gemini API"></div>
                            <div class="w-2 h-2 bg-widdx-success rounded-full" title="DeepSeek API"></div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>
