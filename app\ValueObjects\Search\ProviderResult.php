<?php

namespace App\ValueObjects\Search;

class ProviderResult
{
    public function __construct(
        public readonly string $provider,
        public readonly bool $success,
        public readonly array $results,
        public readonly float $responseTime,
        public readonly ?string $error = null,
        public readonly array $metadata = [],
        public readonly int $totalResults = 0,
        public readonly ?string $nextPageToken = null
    ) {}

    /**
     * Convert to array for serialization
     */
    public function toArray(): array
    {
        return [
            'provider' => $this->provider,
            'success' => $this->success,
            'results' => array_map(
                fn($result) => $result instanceof SearchResultItem ? $result->toArray() : $result,
                $this->results
            ),
            'responseTime' => $this->responseTime,
            'error' => $this->error,
            'metadata' => $this->metadata,
            'totalResults' => $this->totalResults,
            'nextPageToken' => $this->nextPageToken,
        ];
    }

    /**
     * Create a successful result
     */
    public static function success(
        string $provider,
        array $results,
        float $responseTime,
        array $metadata = [],
        int $totalResults = 0,
        ?string $nextPageToken = null
    ): self {
        return new self(
            provider: $provider,
            success: true,
            results: $results,
            responseTime: $responseTime,
            error: null,
            metadata: $metadata,
            totalResults: $totalResults,
            nextPageToken: $nextPageToken
        );
    }

    /**
     * Create a failed result
     */
    public static function failure(
        string $provider,
        string $error,
        float $responseTime = 0.0,
        array $metadata = []
    ): self {
        return new self(
            provider: $provider,
            success: false,
            results: [],
            responseTime: $responseTime,
            error: $error,
            metadata: $metadata
        );
    }

    /**
     * Get the search result items as SearchResultItem objects
     */
    public function getResultItems(): array
    {
        return array_map(
            fn($result) => $result instanceof SearchResultItem 
                ? $result 
                : SearchResultItem::fromArray($result),
            $this->results
        );
    }

    /**
     * Check if the result has any valid results
     */
    public function hasResults(): bool
    {
        return $this->success && !empty($this->results);
    }
}