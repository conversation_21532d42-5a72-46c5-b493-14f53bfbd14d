<?php

use App\Http\Controllers\ChatController;
use App\Http\Controllers\AdvancedFeaturesController;
use App\Http\Controllers\UnlimitedSearchController;
use App\Http\Controllers\DeepSeekSearchController;
use App\Http\Controllers\ImageGenerationController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// WIDDX AI Chat Routes

// Chat Routes
Route::prefix('chat')->middleware('widdx.rate_limit')->group(function () {
    Route::post('/', [ChatController::class, 'chat']);
    Route::get('/personalities', [ChatController::class, 'getPersonalities']);
    Route::get('/history', [ChatController::class, 'getSessionHistory']);
    Route::put('/personality', [ChatController::class, 'updatePersonality']);
});

// Advanced Features Routes
Route::prefix('features')->middleware('widdx.rate_limit')->group(function () {
    // Test image generation endpoint
    Route::get('/test-image', function (\App\Services\ImageGenerationService $imageService) {
        try {
            // Get available providers from the service
            $availableOptions = $imageService->getAvailableOptions();
            $availableProviders = $availableOptions['providers'] ?? [];
            
            if (empty($availableProviders)) {
                throw new \Exception('No image generation providers are available');
            }
            
            // Use the first available provider for testing
            $provider = $availableProviders[0];
            
            $prompt = 'A beautiful sunset over mountains';
            $options = [
                'provider' => $provider,
                'style' => 'vivid',
                'quality' => 'hd',
                'target_language' => 'en',
                'debug' => true // Enable debug mode for more detailed logging
            ];
            
            // Log the test attempt
            \Illuminate\Support\Facades\Log::info('Starting image generation test', [
                'provider' => $provider,
                'options' => $options
            ]);
            
            $result = $imageService->generateImage($prompt, $options);
            
            // Log successful generation
            \Illuminate\Support\Facades\Log::info('Image generation successful', [
                'provider' => $provider,
                'result_keys' => array_keys($result)
            ]);
            
            return response()->json([
                'success' => true,
                'provider' => $provider,
                'result' => $result
            ]);
        } catch (\Exception $e) {
            // Log the full error
            \Illuminate\Support\Facades\Log::error('Image generation test failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'available_providers' => $availableProviders ?? null
            ], 500);
        }
    });

    // Live Search (Free & Paid)
    Route::post('/search', [AdvancedFeaturesController::class, 'search']);
    Route::post('/search-suggestions', [AdvancedFeaturesController::class, 'searchSuggestions']);
    Route::post('/deep-search', [AdvancedFeaturesController::class, 'deepSearch']);

    // Image Generation Routes
    Route::post('/generate-image', [ImageGenerationController::class, 'generate']);

    // Test route for image generation
    Route::get('/test-image-generation', function () {
        try {
            // Log start of the request
            \Illuminate\Support\Facades\Log::info('Starting test image generation');
            
            // Initialize services with error handling
            try {
                $storageService = new \App\Services\ImageStorageService();
                $imageService = new \App\Services\ImageGenerationService($storageService);
                
                \Illuminate\Support\Facades\Log::info('Services initialized', [
                    'storage_service' => get_class($storageService),
                    'image_service' => get_class($imageService)
                ]);
                
                // Test with a simple prompt
                $prompt = 'A beautiful sunset over mountains';
                $options = [
                    'provider' => 'huggingface',
                    'model' => 'stabilityai/stable-diffusion-2-1',
                    'width' => 512,
                    'height' => 512,
                    'num_images' => 1
                ];
                
                \Illuminate\Support\Facades\Log::info('Calling generateImage', [
                    'prompt' => $prompt,
                    'options' => $options
                ]);
                
                $result = $imageService->generateImage($prompt, $options);
                
                if (empty($result['images'])) {
                    throw new \Exception('No images were generated');
                }
                
                // Log success
                \Illuminate\Support\Facades\Log::info('Image generation successful', [
                    'images_count' => count($result['images']),
                    'first_image' => $result['images'][0]['url'] ?? null
                ]);
                
                return response()->json([
                    'success' => true,
                    'result' => $result,
                    'message' => 'تم توليد الصور بنجاح'
                ]);
                
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('Image generation service error', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                return response()->json([
                    'success' => false,
                    'error' => $e->getMessage(),
                    'trace' => config('app.debug') ? $e->getTraceAsString() : null,
                    'message' => 'خطأ في خدمة توليد الصور',
                    'service_error' => true
                ], 500);
            }
            
        } catch (\Throwable $e) {
            // Catch any unhandled exceptions
            \Illuminate\Support\Facades\Log::error('Unexpected error in test image generation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'type' => get_class($e)
            ]);
            
            return response()->json([
                'success' => false,
                'error' => 'An unexpected error occurred',
                'message' => 'حدث خطأ غير متوقع',
                'exception' => config('app.debug') ? [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'type' => get_class($e)
                ] : null
            ], 500);
        }
    });

    Route::post('/generate-placeholder', [AdvancedFeaturesController::class, 'generatePlaceholder']);

    // Voice Services (Free & Paid)
    Route::post('/text-to-speech', [AdvancedFeaturesController::class, 'textToSpeech']);
    Route::post('/speech-to-text', [AdvancedFeaturesController::class, 'speechToText']);
    Route::get('/speech-to-text-instructions', [AdvancedFeaturesController::class, 'speechToTextInstructions']);
    Route::get('/voice-widget', [AdvancedFeaturesController::class, 'voiceWidget']);

    // Think Mode
    Route::post('/think-mode', [AdvancedFeaturesController::class, 'thinkMode']);

    // Document Analysis
    Route::post('/analyze-document', [AdvancedFeaturesController::class, 'analyzeDocument']);

    // Vision/Image Analysis
    Route::post('/analyze-image', [AdvancedFeaturesController::class, 'analyzeImage']);

    // Capabilities
    Route::get('/capabilities', [AdvancedFeaturesController::class, 'getCapabilities']);
});

// Unlimited Search Routes (No Rate Limiting)
Route::prefix('unlimited-search')->middleware('unlimited.search')->group(function () {
    Route::post('/', [UnlimitedSearchController::class, 'search']);
    Route::post('/bulk', [UnlimitedSearchController::class, 'bulkSearch']);
    Route::post('/suggestions', [UnlimitedSearchController::class, 'suggestions']);
    Route::get('/capabilities', [UnlimitedSearchController::class, 'capabilities']);
});

// DeepSeek Intelligent Search Routes (AI-Powered)
Route::prefix('deepseek-search')->middleware('unlimited.search')->group(function () {
    Route::post('/intelligent', [DeepSeekSearchController::class, 'intelligentSearch']);
    Route::post('/comparative', [DeepSeekSearchController::class, 'comparativeSearch']);
    Route::post('/interactive', [DeepSeekSearchController::class, 'interactiveSearch']);
    Route::get('/capabilities', [DeepSeekSearchController::class, 'capabilities']);
});

// Health check endpoint
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'service' => 'WIDDX AI',
        'timestamp' => now()->toISOString(),
        'features' => [
            'live_search' => true,
            'image_generation' => true,
            'voice_services' => true,
            'deep_search' => true,
            'think_mode' => true,
            'document_analysis' => true,
            'vision' => true,
        ],
    ]);
});
