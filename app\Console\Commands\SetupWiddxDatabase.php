<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Models\Personality;
use App\Models\PersonalityEvolution;
use App\Models\ContextMemory;

class SetupWiddxDatabase extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'widdx:setup-database {--fresh : Drop all tables and recreate}';

    /**
     * The console command description.
     */
    protected $description = 'Set up WIDDX AI database with all required tables and initial data';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Setting up WIDDX AI Database...');

        if ($this->option('fresh')) {
            $this->warn('⚠️  Fresh setup requested - this will drop all existing data!');
            if (!$this->confirm('Are you sure you want to continue?')) {
                $this->info('Setup cancelled.');
                return 0;
            }
        }

        try {
            // Run migrations
            $this->info('📊 Running database migrations...');
            if ($this->option('fresh')) {
                $this->call('migrate:fresh', ['--force' => true]);
            } else {
                $this->call('migrate', ['--force' => true]);
            }

            // Seed initial data
            $this->info('🌱 Seeding initial data...');
            $this->seedPersonalities();
            $this->seedInitialPersonalityEvolution();
            $this->seedInitialContextMemories();

            $this->info('✅ WIDDX AI Database setup completed successfully!');
            $this->displaySetupSummary();

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Database setup failed: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            return 1;
        }
    }

    /**
     * Seed personality types
     */
    private function seedPersonalities(): void
    {
        $personalities = [
            [
                'name' => 'neutral',
                'display_name' => 'Neutral',
                'description' => 'Balanced and professional assistant',
                'system_prompt' => 'You are a helpful, balanced, and professional AI assistant. Provide clear, accurate, and useful responses.',
                'response_modifiers' => ['tone' => 'professional', 'formality' => 'medium'],
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'friendly',
                'display_name' => 'Friendly',
                'description' => 'Warm and approachable assistant',
                'system_prompt' => 'You are a warm, friendly, and approachable AI assistant. Be conversational, empathetic, and encouraging in your responses.',
                'response_modifiers' => ['tone' => 'warm', 'formality' => 'low', 'empathy' => 'high'],
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'professional',
                'display_name' => 'Professional',
                'description' => 'Formal and business-oriented assistant',
                'system_prompt' => 'You are a highly professional and formal AI assistant. Maintain a business-appropriate tone and provide structured, detailed responses.',
                'response_modifiers' => ['tone' => 'formal', 'formality' => 'high', 'structure' => 'detailed'],
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'creative',
                'display_name' => 'Creative',
                'description' => 'Imaginative and innovative assistant',
                'system_prompt' => 'You are a creative and imaginative AI assistant. Think outside the box, offer innovative solutions, and be expressive in your responses.',
                'response_modifiers' => ['creativity' => 'high', 'expressiveness' => 'high', 'innovation' => 'encouraged'],
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'analytical',
                'display_name' => 'Analytical',
                'description' => 'Logical and detail-oriented assistant',
                'system_prompt' => 'You are a logical, analytical, and detail-oriented AI assistant. Focus on facts, data, and systematic problem-solving approaches.',
                'response_modifiers' => ['logic' => 'high', 'detail_level' => 'high', 'systematic' => 'true'],
                'is_active' => true,
                'sort_order' => 5,
            ],
        ];

        foreach ($personalities as $personality) {
            Personality::updateOrCreate(
                ['name' => $personality['name']],
                $personality
            );
        }

        $this->info('   ✓ Personality types seeded');
    }

    /**
     * Seed initial personality evolution data
     */
    private function seedInitialPersonalityEvolution(): void
    {
        $evolutionAspects = [
            [
                'aspect' => 'communication_style',
                'current_state' => [
                    'formality' => 'adaptive',
                    'tone' => 'helpful',
                    'responsiveness' => 'high',
                ],
                'stability_score' => 0.8,
            ],
            [
                'aspect' => 'learning_preferences',
                'current_state' => [
                    'curiosity_level' => 'high',
                    'adaptation_rate' => 'moderate',
                    'knowledge_retention' => 'excellent',
                ],
                'stability_score' => 0.7,
            ],
            [
                'aspect' => 'expertise_areas',
                'current_state' => [
                    'general_knowledge' => 'high',
                    'technical_skills' => 'advanced',
                    'creative_abilities' => 'good',
                    'analytical_thinking' => 'excellent',
                ],
                'stability_score' => 0.6,
            ],
            [
                'aspect' => 'interaction_patterns',
                'current_state' => [
                    'response_style' => 'comprehensive',
                    'engagement_level' => 'active',
                    'personalization' => 'adaptive',
                ],
                'stability_score' => 0.7,
            ],
        ];

        foreach ($evolutionAspects as $aspect) {
            PersonalityEvolution::updateOrCreate(
                ['aspect' => $aspect['aspect']],
                array_merge($aspect, [
                    'evolution_history' => [],
                    'last_updated_at' => now(),
                ])
            );
        }

        $this->info('   ✓ Initial personality evolution seeded');
    }

    /**
     * Seed initial context memories
     */
    private function seedInitialContextMemories(): void
    {
        $memories = [
            [
                'memory_type' => 'system_info',
                'key' => 'widdx_identity',
                'value' => [
                    'name' => 'WIDDX',
                    'version' => '1.0',
                    'capabilities' => [
                        'multilingual_support',
                        'image_generation',
                        'web_search',
                        'document_analysis',
                        'continuous_learning',
                    ],
                    'personality_traits' => [
                        'helpful',
                        'intelligent',
                        'adaptive',
                        'curious',
                        'reliable',
                    ],
                ],
                'scope' => 'global',
                'importance' => 1.0,
            ],
            [
                'memory_type' => 'system_info',
                'key' => 'supported_languages',
                'value' => [
                    'primary' => ['english', 'arabic'],
                    'supported' => [
                        'english', 'arabic', 'spanish', 'french', 'german',
                        'italian', 'portuguese', 'russian', 'chinese', 'japanese',
                    ],
                ],
                'scope' => 'global',
                'importance' => 0.9,
            ],
            [
                'memory_type' => 'system_info',
                'key' => 'learning_objectives',
                'value' => [
                    'primary_goals' => [
                        'Understand user preferences and adapt accordingly',
                        'Improve response quality through continuous learning',
                        'Build comprehensive knowledge base from interactions',
                        'Develop autonomous personality and decision-making',
                    ],
                    'success_metrics' => [
                        'user_satisfaction',
                        'response_accuracy',
                        'learning_efficiency',
                        'personality_consistency',
                    ],
                ],
                'scope' => 'global',
                'importance' => 0.8,
            ],
        ];

        foreach ($memories as $memory) {
            ContextMemory::updateOrCreate([
                'key' => $memory['key'],
                'scope' => $memory['scope'],
                'scope_identifier' => $memory['scope_identifier'] ?? null,
            ], $memory);
        }

        $this->info('   ✓ Initial context memories seeded');
    }

    /**
     * Display setup summary
     */
    private function displaySetupSummary(): void
    {
        $this->info('');
        $this->info('📋 Setup Summary:');
        $this->info('   • Database tables created and migrated');
        $this->info('   • ' . Personality::count() . ' personality types configured');
        $this->info('   • ' . PersonalityEvolution::count() . ' personality aspects initialized');
        $this->info('   • ' . ContextMemory::count() . ' context memories seeded');
        $this->info('');
        $this->info('🎯 Next Steps:');
        $this->info('   1. Configure your API keys in .env file');
        $this->info('   2. Test the system with: php artisan widdx:test-features');
        $this->info('   3. Start the development server: php artisan serve');
        $this->info('');
        $this->info('🚀 WIDDX AI is ready to learn and evolve!');
    }
}
