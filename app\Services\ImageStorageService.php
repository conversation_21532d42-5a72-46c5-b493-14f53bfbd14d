<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ImageStorageService
{
    private const MAX_STORAGE_SIZE = 1024 * 1024 * 1024; // 1GB
    private const CLEANUP_DAYS = 30; // Delete images older than 30 days
    private const IMAGES_DIRECTORY = 'generated_images';

    /**
     * Save image with optimization
     */
    public function saveImage(string $imageData, string $provider, array $metadata = []): string
    {
        // Validate image data
        if (empty($imageData)) {
            throw new \Exception('Empty image data provided');
        }

        // Ensure directory exists
        $this->ensureDirectoryExists();

        // Check storage space
        $this->checkStorageSpace();

        // Generate optimized filename
        $filename = $this->generateFilename($provider, $metadata);
        $path = self::IMAGES_DIRECTORY . '/' . $filename;

        // Optimize image data if needed
        $optimizedData = $this->optimizeImageData($imageData, $metadata);

        // Save the image
        $saved = Storage::disk('public')->put($path, $optimizedData);

        if (!$saved) {
            throw new \Exception('Failed to save image to storage');
        }

        // Log storage info
        $this->logStorageInfo($path, $metadata);

        return $path;
    }

    /**
     * Clean up old images
     * 
     * @param int $daysToKeep Number of days to keep images (default: 30)
     * @return array Result of the cleanup operation
     */
    public function cleanupOldImages(int $daysToKeep = 30): array
    {
        $cutoffDate = now()->subDays($daysToKeep);
        $deletedCount = 0;
        $freedSpace = 0;

        try {
            if (!Storage::disk('public')->exists(self::IMAGES_DIRECTORY)) {
                return [
                    'deleted_count' => 0,
                    'freed_space' => 0,
                    'freed_space_formatted' => $this->formatFileSize(0),
                    'message' => 'No images directory found'
                ];
            }

            $files = Storage::disk('public')->files(self::IMAGES_DIRECTORY);
            
            foreach ($files as $file) {
                try {
                    $lastModified = Storage::disk('public')->lastModified($file);
                    $fileDate = Carbon::createFromTimestamp($lastModified);
                    
                    if ($fileDate->lt($cutoffDate)) {
                        $size = Storage::disk('public')->size($file);
                        if (Storage::disk('public')->delete($file)) {
                            $deletedCount++;
                            $freedSpace += $size;
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('Error processing file during cleanup', [
                        'file' => $file,
                        'error' => $e->getMessage()
                    ]);
                    continue;
                }
            }

            $result = [
                'deleted_count' => $deletedCount,
                'freed_space' => $freedSpace,
                'freed_space_formatted' => $this->formatFileSize($freedSpace),
                'message' => 'تم تنظيف الصور القديمة بنجاح',
                'cutoff_date' => $cutoffDate->toDateTimeString()
            ];

            Log::info('Image cleanup completed', $result);
            return $result;

        } catch (\Exception $e) {
            Log::error('Failed to clean up old images', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return [
                'deleted_count' => $deletedCount,
                'freed_space' => $freedSpace,
                'freed_space_formatted' => $this->formatFileSize($freedSpace),
                'error' => $e->getMessage(),
                'message' => 'فشل في تنظيف الصور القديمة',
                'cutoff_date' => $cutoffDate->toDateTimeString()
            ];
        }
    }

    /**
     * Get storage statistics
     * 
     * @return array Storage statistics including file count, sizes, and usage
     */
    public function getStorageStats(): array
    {
        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'total_size_formatted' => $this->formatFileSize(0),
            'storage_limit' => self::MAX_STORAGE_SIZE,
            'storage_limit_formatted' => $this->formatFileSize(self::MAX_STORAGE_SIZE),
            'usage_percentage' => 0,
            'directory' => self::IMAGES_DIRECTORY,
            'status' => 'healthy',
            'last_cleanup' => null,
            'files_by_extension' => [],
            'oldest_file' => null,
            'newest_file' => null
        ];

        try {
            if (!Storage::disk('public')->exists(self::IMAGES_DIRECTORY)) {
                $stats['message'] = 'Images directory does not exist';
                return $stats;
            }

            $files = Storage::disk('public')->files(self::IMAGES_DIRECTORY);
            $stats['total_files'] = count($files);
            
            $oldestTimestamp = PHP_INT_MAX;
            $newestTimestamp = 0;
            $oldestFile = null;
            $newestFile = null;
            $filesByExtension = [];
            
            foreach ($files as $file) {
                try {
                    $size = Storage::disk('public')->size($file);
                    $lastModified = Storage::disk('public')->lastModified($file);
                    
                    $stats['total_size'] += $size;
                    
                    // Track oldest and newest files
                    if ($lastModified < $oldestTimestamp) {
                        $oldestTimestamp = $lastModified;
                        $oldestFile = [
                            'path' => $file,
                            'last_modified' => date('Y-m-d H:i:s', $lastModified),
                            'size' => $size,
                            'size_formatted' => $this->formatFileSize($size)
                        ];
                    }
                    
                    if ($lastModified > $newestTimestamp) {
                        $newestTimestamp = $lastModified;
                        $newestFile = [
                            'path' => $file,
                            'last_modified' => date('Y-m-d H:i:s', $lastModified),
                            'size' => $size,
                            'size_formatted' => $this->formatFileSize($size)
                        ];
                    }
                    
                    // Track files by extension
                    $extension = pathinfo($file, PATHINFO_EXTENSION) ?: 'unknown';
                    if (!isset($filesByExtension[$extension])) {
                        $filesByExtension[$extension] = [
                            'count' => 0,
                            'total_size' => 0,
                            'total_size_formatted' => '0 B'
                        ];
                    }
                    $filesByExtension[$extension]['count']++;
                    $filesByExtension[$extension]['total_size'] += $size;
                    $filesByExtension[$extension]['total_size_formatted'] = $this->formatFileSize(
                        $filesByExtension[$extension]['total_size']
                    );
                    
                } catch (\Exception $e) {
                    Log::error('Error processing file for stats', [
                        'file' => $file,
                        'error' => $e->getMessage()
                    ]);
                    continue;
                }
            }
            
            // Calculate usage percentage
            $stats['total_size_formatted'] = $this->formatFileSize($stats['total_size']);
            $stats['usage_percentage'] = min(100, round(($stats['total_size'] / self::MAX_STORAGE_SIZE) * 100, 2));
            
            // Set status based on usage
            if ($stats['usage_percentage'] > 90) {
                $stats['status'] = 'critical';
            } elseif ($stats['usage_percentage'] > 75) {
                $stats['status'] = 'warning';
            }
            
            // Add file extension stats
            $stats['files_by_extension'] = $filesByExtension;
            
            // Add oldest and newest file info
            $stats['oldest_file'] = $oldestFile;
            $stats['newest_file'] = $newestFile;
            
            return $stats;
            
        } catch (\Exception $e) {
            Log::error('Failed to get storage stats', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return [
                'error' => $e->getMessage(),
                'message' => 'فشل في جلب إحصائيات التخزين',
                'status' => 'error'
            ];
        }
    }

    /**
     * Ensure images directory exists
     */
    private function ensureDirectoryExists(): void
    {
        if (!Storage::disk('public')->exists(self::IMAGES_DIRECTORY)) {
            Storage::disk('public')->makeDirectory(self::IMAGES_DIRECTORY);
        }
    }

    /**
     * Check available storage space
     */
    private function checkStorageSpace(): void
    {
        $stats = $this->getStorageStats();

        if ($stats['total_size'] > self::MAX_STORAGE_SIZE * 0.9) {
            // If storage is 90% full, trigger cleanup
            $this->cleanupOldImages();
        }

        if ($stats['total_size'] > self::MAX_STORAGE_SIZE) {
            throw new \Exception('Storage limit exceeded. Please try again later.');
        }
    }

    /**
     * Generate optimized filename
     */
    private function generateFilename(string $provider, array $metadata): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $hash = substr(md5(uniqid()), 0, 8);

        $extension = 'png'; // Default
        if (isset($metadata['mime_type'])) {
            $extension = str_replace('image/', '', $metadata['mime_type']);
        }

        return "{$provider}_{$timestamp}_{$hash}.{$extension}";
    }

    /**
     * Optimize image data (placeholder for future image compression)
     */
    private function optimizeImageData(string $imageData, array $metadata): string
    {
        // For now, return as-is
        // Future: Add image compression logic here
        return $imageData;
    }

    /**
     * Log storage information
     */
    private function logStorageInfo(string $path, array $metadata): void
    {
        $size = Storage::disk('public')->size($path);

        Log::info('Image saved to storage', [
            'path' => $path,
            'size' => $size,
            'size_formatted' => $this->formatFileSize($size),
            'metadata' => $metadata,
        ]);
    }

    /**
     * Format file size in human readable format
     */
    private function formatFileSize(int $bytes): string
    {
        if ($bytes === 0) return '0 Bytes';
        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }
}
