<?php

namespace App\Services\Search;

use App\Contracts\Search\SearchProviderInterface;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class SearchProviderManager
{
    /**
     * Registered search providers
     *
     * @var array<string, SearchProviderInterface>
     */
    protected array $providers = [];

    /**
     * Provider weights for load balancing
     *
     * @var array<string, int>
     */
    protected array $weights = [];

    /**
     * Provider health status
     *
     * @var array<string, bool>
     */
    protected array $healthStatus = [];

    /**
     * Provider configuration
     *
     * @var array<string, array>
     */
    protected array $providerConfigs = [];

    /**
     * Health check cache TTL in seconds
     */
    protected const HEALTH_CHECK_TTL = 300; // 5 minutes

    /**
     * Register a search provider
     *
     * @param string $name
     * @param SearchProviderInterface $provider
     * @param int $weight
     * @param array $config
     * @return self
     */
    public function registerProvider(
        string $name,
        SearchProviderInterface $provider,
        int $weight = 1,
        array $config = []
    ): self {
        $this->providers[$name] = $provider;
        $this->weights[$name] = max(1, $weight);
        $this->providerConfigs[$name] = $config;
        $this->healthStatus[$name] = true; // Assume healthy by default

        return $this;
    }

    /**
     * Get a provider by name
     *
     * @param string $name
     * @return SearchProviderInterface|null
     */
    public function getProvider(string $name): ?SearchProviderInterface
    {
        return $this->providers[$name] ?? null;
    }

    /**
     * Get all registered providers
     *
     * @return array<string, SearchProviderInterface>
     */
    public function getAllProviders(): array
    {
        return $this->providers;
    }

    /**
     * Get available providers (configured and healthy)
     *
     * @return array<string, SearchProviderInterface>
     */
    public function getAvailableProviders(): array
    {
        return array_filter($this->providers, function (SearchProviderInterface $provider) {
            return $this->isProviderAvailable($provider);
        });
    }

    /**
     * Check if a provider is available
     *
     * @param SearchProviderInterface|string $provider
     * @return bool
     */
    public function isProviderAvailable($provider): bool
    {
        $providerName = $provider instanceof SearchProviderInterface ? $provider->getName() : $provider;
        
        // Check cache first
        $cacheKey = $this->getHealthCheckCacheKey($providerName);
        if (Cache::has($cacheKey)) {
            return (bool) Cache::get($cacheKey, false);
        }

        // Perform health check if not in cache
        if ($provider instanceof SearchProviderInterface) {
            $isAvailable = $provider->isConfigured() && $provider->isAvailable();
        } else {
            $provider = $this->getProvider($provider);
            $isAvailable = $provider && $provider->isConfigured() && $provider->isAvailable();
        }

        // Cache the result
        Cache::put($cacheKey, $isAvailable, self::HEALTH_CHECK_TTL);
        
        // Update health status
        if (isset($this->healthStatus[$providerName])) {
            $this->healthStatus[$providerName] = $isAvailable;
        }

        return $isAvailable;
    }

    /**
     * Get the next available provider using weighted round-robin
     *
     * @return SearchProviderInterface|null
     * @throws \RuntimeException If no providers are available
     */
    public function getNextProvider(): ?SearchProviderInterface
    {
        $availableProviders = $this->getAvailableProviders();
        
        if (empty($availableProviders)) {
            return null;
        }

        // Filter weights for available providers
        $availableWeights = array_intersect_key($this->weights, $availableProviders);
        
        // Get total weight
        $totalWeight = array_sum($availableWeights);
        
        if ($totalWeight <= 0) {
            // If all weights are zero, reset to 1 for all
            $availableWeights = array_fill_keys(array_keys($availableWeights), 1);
            $totalWeight = count($availableWeights);
        }
        
        // Get a random position in the total weight
        $position = mt_rand(1, $totalWeight);
        $currentWeight = 0;
        
        foreach ($availableWeights as $name => $weight) {
            $currentWeight += $weight;
            if ($position <= $currentWeight) {
                return $availableProviders[$name];
            }
        }
        
        // Fallback to first available provider
        return reset($availableProviders);
    }

    /**
     * Update provider weight
     *
     * @param string $name
     * @param int $weight
     * @return self
     */
    public function setProviderWeight(string $name, int $weight): self
    {
        if (isset($this->weights[$name])) {
            $this->weights[$name] = max(0, $weight);
        }
        
        return $this;
    }

    /**
     * Get provider configuration
     *
     * @param string $name
     * @return array
     */
    public function getProviderConfig(string $name): array
    {
        return $this->providerConfigs[$name] ?? [];
    }

    /**
     * Update provider configuration
     *
     * @param string $name
     * @param array $config
     * @return self
     */
    public function updateProviderConfig(string $name, array $config): self
    {
        if (isset($this->providerConfigs[$name])) {
            $this->providerConfigs[$name] = array_merge($this->providerConfigs[$name], $config);
            
            // Invalidate health check cache when config changes
            Cache::forget($this->getHealthCheckCacheKey($name));
        }
        
        return $this;
    }

    /**
     * Check health of all providers
     *
     * @return array<string, bool>
     */
    public function checkAllProvidersHealth(): array
    {
        $results = [];
        
        foreach ($this->providers as $name => $provider) {
            $results[$name] = $this->isProviderAvailable($provider);
        }
        
        return $results;
    }

    /**
     * Get health check cache key for a provider
     *
     * @param string $providerName
     * @return string
     */
    protected function getHealthCheckCacheKey(string $providerName): string
    {
        return 'search:provider:health:' . Str::slug($providerName);
    }

    /**
     * Get provider statistics
     *
     * @return array
     */
    public function getStatistics(): array
    {
        $stats = [
            'total_providers' => count($this->providers),
            'available_providers' => 0,
            'providers' => [],
        ];

        foreach ($this->providers as $name => $provider) {
            $isAvailable = $this->isProviderAvailable($provider);
            if ($isAvailable) {
                $stats['available_providers']++;
            }

            $stats['providers'][$name] = [
                'available' => $isAvailable,
                'weight' => $this->weights[$name] ?? 0,
                'configured' => $provider->isConfigured(),
                'timeout' => $provider->getTimeout(),
                'config' => $this->getProviderConfig($name),
            ];
        }

        return $stats;
    }
}
