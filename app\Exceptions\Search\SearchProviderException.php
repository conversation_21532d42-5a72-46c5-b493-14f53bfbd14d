<?php

namespace App\Exceptions\Search;

class SearchProviderException extends SearchException
{
    /**
     * Create exception for provider configuration issues
     */
    public static function configurationError(
        string $provider,
        string $message,
        array $context = []
    ): self {
        return new self(
            message: "Provider configuration error: {$message}",
            code: 1001,
            searchQuery: '',
            provider: $provider,
            context: $context
        );
    }

    /**
     * Create exception for provider API errors
     */
    public static function apiError(
        string $provider,
        string $message,
        string $query = '',
        array $context = []
    ): self {
        return new self(
            message: "Provider API error: {$message}",
            code: 1002,
            searchQuery: $query,
            provider: $provider,
            context: $context
        );
    }

    /**
     * Create exception for provider unavailability
     */
    public static function unavailable(
        string $provider,
        string $reason = '',
        array $context = []
    ): self {
        $message = "Provider '{$provider}' is unavailable";
        if ($reason) {
            $message .= ": {$reason}";
        }

        return new self(
            message: $message,
            code: 1003,
            provider: $provider,
            context: $context
        );
    }

    /**
     * Create exception for invalid provider responses
     */
    public static function invalidResponse(
        string $provider,
        string $query,
        string $details = '',
        array $context = []
    ): self {
        $message = "Invalid response from provider '{$provider}'";
        if ($details) {
            $message .= ": {$details}";
        }

        return new self(
            message: $message,
            code: 1004,
            searchQuery: $query,
            provider: $provider,
            context: $context
        );
    }
}