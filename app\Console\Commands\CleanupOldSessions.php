<?php

namespace App\Console\Commands;

use App\Services\SessionService;
use Illuminate\Console\Command;

class CleanupOldSessions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'widdx:cleanup-sessions {--days=30 : Number of days to keep sessions}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old chat sessions and their associated messages';

    private SessionService $sessionService;

    public function __construct(SessionService $sessionService)
    {
        parent::__construct();
        $this->sessionService = $sessionService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');

        $this->info("Cleaning up sessions older than {$days} days...");

        $deletedCount = $this->sessionService->cleanupOldSessions($days);

        if ($deletedCount > 0) {
            $this->info("Successfully deleted {$deletedCount} old sessions.");
        } else {
            $this->info("No old sessions found to delete.");
        }

        return self::SUCCESS;
    }
}
