<?php

namespace App\Services\Search;

use App\ValueObjects\Search\SearchOptions;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Carbon\Carbon;

class CacheManager
{
    protected array $ttlMapping;
    protected string $keyPrefix;
    protected bool $enabled;
    protected array $metrics;
    protected string $defaultStore;

    public function __construct()
    {
        $this->ttlMapping = $this->loadTTLMapping();
        $this->keyPrefix = Config::get('services.search.cache.prefix', 'search');
        $this->enabled = Config::get('services.search.cache.enabled', true);
        $this->defaultStore = Config::get('services.search.cache.store', 'default');
        $this->metrics = [
            'hits' => 0,
            'misses' => 0,
            'writes' => 0,
            'deletes' => 0,
            'errors' => 0,
        ];
    }

    /**
     * Generate consistent cache key for search operations
     */
    public function generateCacheKey(string $serviceType, string $query, SearchOptions $options): string
    {
        $keyData = [
            'service' => $serviceType,
            'query' => trim(strtolower($query)),
            'options' => $this->normalizeOptionsForCaching($options),
        ];

        $hash = md5(serialize($keyData));
        return "{$this->keyPrefix}:{$serviceType}:{$hash}";
    }

    /**
     * Get cached search result
     */
    public function get(string $cacheKey): ?array
    {
        if (!$this->enabled) {
            return null;
        }

        try {
            $cached = Cache::store($this->defaultStore)->get($cacheKey);
            
            if ($cached !== null) {
                $this->metrics['hits']++;
                
                Log::debug('Cache hit', [
                    'key' => $cacheKey,
                    'cached_at' => $cached['cached_at'] ?? 'unknown',
                ]);

                // Add cache metadata
                $cached['from_cache'] = true;
                $cached['cache_key'] = $cacheKey;
                
                return $cached;
            }

            $this->metrics['misses']++;
            return null;

        } catch (\Exception $e) {
            $this->metrics['errors']++;
            Log::warning('Cache get failed', [
                'key' => $cacheKey,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Store search result in cache
     */
    public function put(string $cacheKey, array $data, string $serviceType, SearchOptions $options = null): bool
    {
        if (!$this->enabled) {
            return false;
        }

        try {
            $ttl = $this->getTTL($serviceType, $options);
            $expiresAt = now()->addMinutes($ttl);

            // Add cache metadata
            $cacheData = array_merge($data, [
                'cached_at' => now()->toISOString(),
                'expires_at' => $expiresAt->toISOString(),
                'cache_key' => $cacheKey,
                'service_type' => $serviceType,
                'ttl_minutes' => $ttl,
            ]);

            $success = Cache::store($this->defaultStore)->put($cacheKey, $cacheData, $expiresAt);
            
            if ($success) {
                $this->metrics['writes']++;
                
                Log::debug('Cache stored', [
                    'key' => $cacheKey,
                    'service_type' => $serviceType,
                    'ttl_minutes' => $ttl,
                    'expires_at' => $expiresAt->toISOString(),
                ]);
            }

            return $success;

        } catch (\Exception $e) {
            $this->metrics['errors']++;
            Log::warning('Cache put failed', [
                'key' => $cacheKey,
                'service_type' => $serviceType,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Remove specific cache entry
     */
    public function forget(string $cacheKey): bool
    {
        if (!$this->enabled) {
            return false;
        }

        try {
            $success = Cache::store($this->defaultStore)->forget($cacheKey);
            
            if ($success) {
                $this->metrics['deletes']++;
                Log::debug('Cache entry deleted', ['key' => $cacheKey]);
            }

            return $success;

        } catch (\Exception $e) {
            $this->metrics['errors']++;
            Log::warning('Cache forget failed', [
                'key' => $cacheKey,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Clear cache for specific service type
     */
    public function clearServiceCache(string $serviceType): int
    {
        if (!$this->enabled) {
            return 0;
        }

        try {
            $pattern = "{$this->keyPrefix}:{$serviceType}:*";
            $cleared = $this->clearByPattern($pattern);
            
            Log::info('Service cache cleared', [
                'service_type' => $serviceType,
                'cleared_count' => $cleared,
            ]);

            return $cleared;

        } catch (\Exception $e) {
            $this->metrics['errors']++;
            Log::error('Service cache clear failed', [
                'service_type' => $serviceType,
                'error' => $e->getMessage(),
            ]);
            return 0;
        }
    }

    /**
     * Clear all search cache
     */
    public function clearAllCache(): int
    {
        if (!$this->enabled) {
            return 0;
        }

        try {
            $pattern = "{$this->keyPrefix}:*";
            $cleared = $this->clearByPattern($pattern);
            
            Log::info('All search cache cleared', ['cleared_count' => $cleared]);
            
            return $cleared;

        } catch (\Exception $e) {
            $this->metrics['errors']++;
            Log::error('All cache clear failed', ['error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * Clear expired cache entries
     */
    public function clearExpiredCache(): int
    {
        if (!$this->enabled) {
            return 0;
        }

        try {
            // This is a simplified implementation
            // In production, you might want to use Redis SCAN or similar
            $pattern = "{$this->keyPrefix}:*";
            $keys = $this->getKeysByPattern($pattern);
            $cleared = 0;

            foreach ($keys as $key) {
                $cached = Cache::store($this->defaultStore)->get($key);
                if ($cached && isset($cached['expires_at'])) {
                    $expiresAt = Carbon::parse($cached['expires_at']);
                    if ($expiresAt->isPast()) {
                        if (Cache::store($this->defaultStore)->forget($key)) {
                            $cleared++;
                        }
                    }
                }
            }

            Log::info('Expired cache cleared', ['cleared_count' => $cleared]);
            
            return $cleared;

        } catch (\Exception $e) {
            $this->metrics['errors']++;
            Log::error('Expired cache clear failed', ['error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * Get cache statistics
     */
    public function getStatistics(): array
    {
        $totalRequests = $this->metrics['hits'] + $this->metrics['misses'];
        $hitRate = $totalRequests > 0 ? ($this->metrics['hits'] / $totalRequests) * 100 : 0;

        return [
            'enabled' => $this->enabled,
            'store' => $this->defaultStore,
            'prefix' => $this->keyPrefix,
            'metrics' => $this->metrics,
            'hit_rate_percentage' => round($hitRate, 2),
            'total_requests' => $totalRequests,
            'ttl_mapping' => $this->ttlMapping,
        ];
    }

    /**
     * Get cache info for specific key
     */
    public function getCacheInfo(string $cacheKey): ?array
    {
        if (!$this->enabled) {
            return null;
        }

        try {
            $cached = Cache::store($this->defaultStore)->get($cacheKey);
            
            if ($cached === null) {
                return null;
            }

            return [
                'exists' => true,
                'cached_at' => $cached['cached_at'] ?? null,
                'expires_at' => $cached['expires_at'] ?? null,
                'service_type' => $cached['service_type'] ?? null,
                'ttl_minutes' => $cached['ttl_minutes'] ?? null,
                'size_estimate' => strlen(serialize($cached)),
            ];

        } catch (\Exception $e) {
            Log::warning('Cache info retrieval failed', [
                'key' => $cacheKey,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Warm cache with common queries
     */
    public function warmCache(array $commonQueries, string $serviceType = 'free'): int
    {
        if (!$this->enabled) {
            return 0;
        }

        $warmed = 0;
        
        foreach ($commonQueries as $query) {
            try {
                $options = new SearchOptions();
                $cacheKey = $this->generateCacheKey($serviceType, $query, $options);
                
                // Only warm if not already cached
                if ($this->get($cacheKey) === null) {
                    // This would typically involve actually performing the search
                    // For now, we'll just log the intent
                    Log::debug('Cache warming needed', [
                        'query' => $query,
                        'service_type' => $serviceType,
                        'cache_key' => $cacheKey,
                    ]);
                    $warmed++;
                }

            } catch (\Exception $e) {
                Log::warning('Cache warming failed for query', [
                    'query' => $query,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $warmed;
    }

    /**
     * Get TTL for service type and options
     */
    protected function getTTL(string $serviceType, ?SearchOptions $options = null): int
    {
        // Check if options specify a custom TTL
        if ($options && isset($options->metadata['cache_ttl'])) {
            return (int) $options->metadata['cache_ttl'];
        }

        // Use service-specific TTL
        return $this->ttlMapping[$serviceType] ?? $this->ttlMapping['default'];
    }

    /**
     * Normalize search options for consistent caching
     */
    protected function normalizeOptionsForCaching(SearchOptions $options): array
    {
        $normalized = $options->toArray();
        
        // Remove cache-specific options that shouldn't affect cache key
        unset($normalized['enableCache']);
        
        // Sort arrays for consistent hashing
        if (isset($normalized['providers'])) {
            sort($normalized['providers']);
        }
        
        if (isset($normalized['filters'])) {
            ksort($normalized['filters']);
        }

        return $normalized;
    }

    /**
     * Load TTL mapping from configuration
     */
    protected function loadTTLMapping(): array
    {
        return [
            'free' => Config::get('widdx.features.live_search.cache_minutes', 15),
            'live' => Config::get('widdx.features.live_search.cache_minutes', 15),
            'unlimited' => Config::get('widdx.features.unlimited_search.cache_duration', 5),
            'deep' => Config::get('widdx.features.deep_search.cache_minutes', 30),
            'default' => 15,
        ];
    }

    /**
     * Clear cache entries by pattern (simplified implementation)
     */
    protected function clearByPattern(string $pattern): int
    {
        try {
            $keys = $this->getKeysByPattern($pattern);
            $cleared = 0;

            foreach ($keys as $key) {
                if (Cache::store($this->defaultStore)->forget($key)) {
                    $cleared++;
                }
            }

            return $cleared;

        } catch (\Exception $e) {
            Log::error('Pattern-based cache clear failed', [
                'pattern' => $pattern,
                'error' => $e->getMessage(),
            ]);
            return 0;
        }
    }

    /**
     * Get cache keys by pattern (simplified implementation)
     * Note: This is a basic implementation. In production with Redis,
     * you would use SCAN command for better performance
     */
    protected function getKeysByPattern(string $pattern): array
    {
        // This is a simplified implementation
        // In a real application with Redis, you would use the SCAN command
        // For file-based cache, you might need to scan the cache directory
        
        try {
            // For demonstration purposes, return empty array
            // In production, implement proper pattern matching based on your cache store
            return [];
        } catch (\Exception $e) {
            Log::warning('Key pattern matching failed', [
                'pattern' => $pattern,
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * Reset metrics
     */
    public function resetMetrics(): void
    {
        $this->metrics = [
            'hits' => 0,
            'misses' => 0,
            'writes' => 0,
            'deletes' => 0,
            'errors' => 0,
        ];
        
        Log::info('Cache metrics reset');
    }

    /**
     * Enable/disable caching
     */
    public function setEnabled(bool $enabled): void
    {
        $this->enabled = $enabled;
        
        Log::info('Cache manager status changed', ['enabled' => $enabled]);
    }

    /**
     * Check if caching is enabled
     */
    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    /**
     * Get current cache store
     */
    public function getStore(): string
    {
        return $this->defaultStore;
    }

    /**
     * Set cache store
     */
    public function setStore(string $store): void
    {
        $this->defaultStore = $store;
        
        Log::info('Cache store changed', ['store' => $store]);
    }
}