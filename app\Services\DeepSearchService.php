<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class DeepSearchService
{
    private LiveSearchService $liveSearch;
    private DeepSeekClient $deepSeek;
    private int $timeout;
    private int $maxSources;

    public function __construct(LiveSearchService $liveSearch, DeepSeekClient $deepSeek)
    {
        $this->liveSearch = $liveSearch;
        $this->deepSeek = $deepSeek;
        $this->timeout = 60;
        $this->maxSources = 10;
    }

    /**
     * Perform deep search with analysis and insights
     */
    public function deepSearch(string $query, array $options = []): array
    {
        try {
            $cacheKey = 'deep_search_' . md5($query . serialize($options));

            // Check cache first
            if ($cached = Cache::get($cacheKey)) {
                Log::info('Deep search cache hit', ['query' => $query]);
                return $cached;
            }

            Log::info('Deep search started', [
                'query' => $query,
                'options' => $options,
            ]);

            // Step 1: Generate search variations and related queries
            $searchQueries = $this->generateSearchQueries($query);

            // Step 2: Perform multiple searches
            $searchResults = $this->performMultipleSearches($searchQueries, $options);

            // Step 3: Extract and analyze content from top results
            $analyzedContent = $this->analyzeSearchResults($searchResults);

            // Step 4: Generate insights and connections
            $insights = $this->generateInsights($query, $analyzedContent);

            // Step 5: Compile final result
            $result = [
                'success' => true,
                'query' => $query,
                'search_queries_used' => $searchQueries,
                'sources_analyzed' => count($analyzedContent['sources']),
                'insights' => $insights,
                'sources' => $analyzedContent['sources'],
                'related_topics' => $analyzedContent['related_topics'],
                'key_findings' => $analyzedContent['key_findings'],
                'search_time' => now()->toISOString(),
            ];

            // Cache the results for 30 minutes
            Cache::put($cacheKey, $result, now()->addMinutes(30));

            Log::info('Deep search completed', [
                'query' => $query,
                'sources_count' => count($analyzedContent['sources']),
                'insights_count' => count($insights),
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Deep search error', [
                'query' => $query,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'query' => $query,
                'insights' => [],
                'sources' => [],
            ];
        }
    }

    /**
     * Generate multiple search queries for comprehensive coverage
     */
    private function generateSearchQueries(string $originalQuery): array
    {
        try {
            $prompt = "Given the search query: '{$originalQuery}', generate 5 related search queries that would help gather comprehensive information about this topic. Include:
1. The original query
2. More specific variations
3. Related concepts
4. Different perspectives
5. Recent developments

Respond with a simple list of search queries, one per line, without any JSON formatting.";

            $response = $this->deepSeek->chat([
                ['role' => 'system', 'content' => 'You are a search query expert. Generate diverse, relevant search queries.'],
                ['role' => 'user', 'content' => $prompt],
            ], ['max_tokens' => 500]);

            if ($response['success']) {
                $content = $response['content'];
                // Split by lines and clean up
                $lines = explode("\n", $content);
                $queries = [];

                foreach ($lines as $line) {
                    $line = trim($line);
                    // Remove numbering and clean up
                    $line = preg_replace('/^\d+\.\s*/', '', $line);
                    $line = preg_replace('/^-\s*/', '', $line);

                    if (!empty($line) && strlen($line) > 3) {
                        $queries[] = $line;
                    }
                }

                // Ensure we have the original query
                if (!in_array($originalQuery, $queries)) {
                    array_unshift($queries, $originalQuery);
                }

                return array_slice($queries, 0, 5); // Limit to 5 queries
            }

            // Fallback: generate basic variations
            return [
                $originalQuery,
                "{$originalQuery} 2024",
                "{$originalQuery} آخر الأخبار",
                "{$originalQuery} تحليل",
                "{$originalQuery} معلومات",
            ];

        } catch (\Exception $e) {
            Log::warning('Query generation failed', ['error' => $e->getMessage()]);
            return [$originalQuery];
        }
    }

    /**
     * Perform searches using multiple queries
     */
    private function performMultipleSearches(array $queries, array $options): array
    {
        $allResults = [];

        foreach ($queries as $query) {
            try {
                // Ensure query is a string
                if (!is_string($query)) {
                    Log::warning('Invalid query type', ['query' => $query, 'type' => gettype($query)]);
                    continue;
                }

                $searchResult = $this->liveSearch->search($query, [
                    'max_results' => 5,
                    'language' => $options['language'] ?? 'ar',
                    'region' => $options['region'] ?? 'SA',
                ]);

                if ($searchResult['success']) {
                    $allResults[] = [
                        'query' => $query,
                        'results' => $searchResult['results'],
                    ];
                }

                // Small delay to avoid rate limiting
                usleep(500000); // 0.5 seconds

            } catch (\Exception $e) {
                Log::warning('Search failed for query', [
                    'query' => $query,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $allResults;
    }

    /**
     * Analyze search results and extract content
     */
    private function analyzeSearchResults(array $searchResults): array
    {
        $sources = [];
        $allContent = '';

        foreach ($searchResults as $searchResult) {
            foreach ($searchResult['results'] as $result) {
                if (count($sources) >= $this->maxSources) {
                    break 2;
                }

                $content = $this->extractContentFromUrl($result['url']);

                if ($content) {
                    $sources[] = [
                        'title' => $result['title'],
                        'url' => $result['url'],
                        'snippet' => $result['snippet'],
                        'content_preview' => substr($content, 0, 500),
                        'relevance_score' => $this->calculateRelevanceScore($result, $searchResult['query']),
                    ];

                    $allContent .= $content . "\n\n";
                }
            }
        }

        // Sort sources by relevance
        usort($sources, function($a, $b) {
            return $b['relevance_score'] <=> $a['relevance_score'];
        });

        // Extract key findings and related topics
        $analysis = $this->analyzeContent($allContent);

        return [
            'sources' => $sources,
            'related_topics' => $analysis['related_topics'],
            'key_findings' => $analysis['key_findings'],
        ];
    }

    /**
     * Extract content from URL (simplified version)
     */
    private function extractContentFromUrl(string $url): ?string
    {
        try {
            $response = Http::timeout(10)->get($url);

            if ($response->successful()) {
                $html = $response->body();

                // Simple content extraction (in production, use a proper HTML parser)
                $text = strip_tags($html);
                $text = preg_replace('/\s+/', ' ', $text);

                return substr(trim($text), 0, 2000); // Limit content length
            }

        } catch (\Exception $e) {
            Log::debug('Content extraction failed', [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
        }

        return null;
    }

    /**
     * Calculate relevance score for a search result
     */
    private function calculateRelevanceScore(array $result, string $query): float
    {
        $score = 0;

        // Title relevance
        $titleWords = explode(' ', strtolower($result['title']));
        $queryWords = explode(' ', strtolower($query));

        foreach ($queryWords as $word) {
            if (in_array($word, $titleWords)) {
                $score += 2;
            }
        }

        // Snippet relevance
        $snippetWords = explode(' ', strtolower($result['snippet']));
        foreach ($queryWords as $word) {
            if (in_array($word, $snippetWords)) {
                $score += 1;
            }
        }

        // URL quality (simple heuristic)
        if (strpos($result['url'], 'wikipedia') !== false) {
            $score += 3;
        }
        if (strpos($result['url'], '.gov') !== false) {
            $score += 2;
        }
        if (strpos($result['url'], '.edu') !== false) {
            $score += 2;
        }

        return $score;
    }

    /**
     * Analyze content to extract key findings and related topics
     */
    private function analyzeContent(string $content): array
    {
        try {
            $prompt = "Analyze the following content and extract:
1. Key findings (3-5 main points)
2. Related topics (5-7 related subjects)

Content:
" . substr($content, 0, 3000) . "

Respond in JSON format with 'key_findings' and 'related_topics' arrays.";

            $response = $this->deepSeek->chat([
                ['role' => 'system', 'content' => 'You are a content analyst. Extract key insights and related topics from text.'],
                ['role' => 'user', 'content' => $prompt],
            ], ['max_tokens' => 800]);

            if ($response['success']) {
                $content = $response['content'];
                if (preg_match('/\{.*\}/s', $content, $matches)) {
                    $analysis = json_decode($matches[0], true);
                    if (is_array($analysis)) {
                        return [
                            'key_findings' => $analysis['key_findings'] ?? [],
                            'related_topics' => $analysis['related_topics'] ?? [],
                        ];
                    }
                }
            }

        } catch (\Exception $e) {
            Log::warning('Content analysis failed', ['error' => $e->getMessage()]);
        }

        return [
            'key_findings' => [],
            'related_topics' => [],
        ];
    }

    /**
     * Generate insights and connections
     */
    private function generateInsights(string $originalQuery, array $analyzedContent): array
    {
        try {
            $sourcesText = '';
            foreach ($analyzedContent['sources'] as $source) {
                $sourcesText .= "- {$source['title']}: {$source['snippet']}\n";
            }

            $prompt = "Based on the search query '{$originalQuery}' and the following sources, generate 3-5 key insights that connect different pieces of information and reveal deeper understanding:

Sources:
{$sourcesText}

Key Findings:
" . implode("\n", $analyzedContent['key_findings']) . "

Generate insights that:
1. Connect different sources
2. Identify patterns or trends
3. Provide unique perspectives
4. Highlight important implications

Respond with an array of insight objects, each with 'title' and 'description' fields.";

            $response = $this->deepSeek->chat([
                ['role' => 'system', 'content' => 'You are an expert analyst who generates deep insights by connecting information from multiple sources.'],
                ['role' => 'user', 'content' => $prompt],
            ], ['max_tokens' => 1000]);

            if ($response['success']) {
                $content = $response['content'];
                if (preg_match('/\[.*\]/s', $content, $matches)) {
                    $insights = json_decode($matches[0], true);
                    if (is_array($insights)) {
                        return $insights;
                    }
                }
            }

        } catch (\Exception $e) {
            Log::warning('Insight generation failed', ['error' => $e->getMessage()]);
        }

        return [
            [
                'title' => 'تحليل شامل',
                'description' => 'تم جمع معلومات من مصادر متعددة حول الموضوع المطلوب.',
            ],
        ];
    }

    /**
     * Get search statistics
     */
    public function getSearchStats(): array
    {
        return [
            'total_searches' => Cache::get('deep_search_count', 0),
            'cache_hit_rate' => Cache::get('deep_search_cache_hits', 0) / max(Cache::get('deep_search_count', 1), 1),
            'average_sources_per_search' => Cache::get('deep_search_avg_sources', 0),
        ];
    }
}
