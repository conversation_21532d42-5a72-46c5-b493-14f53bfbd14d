<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class LiveSearchService
{
    private FreeSearchService $freeSearch;

    public function __construct(FreeSearchService $freeSearch)
    {
        $this->freeSearch = $freeSearch;
    }

    /**
     * Perform live search using free alternatives only
     */
    public function search(string $query, array $options = []): array
    {
        try {
            Log::info('Live search started (Free)', [
                'query' => $query,
                'options' => $options,
            ]);

            // Use free search service (DuckDuckGo)
            $result = $this->freeSearch->search($query, $options);

            Log::info('Live search completed', [
                'query' => $query,
                'success' => $result['success'],
                'results_count' => count($result['results'] ?? []),
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Live search failed', [
                'query' => $query,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'query' => $query,
                'results' => [],
            ];
        }
    }

    /**
     * Search for trending topics
     */
    public function getTrendingTopics(string $language = 'ar'): array
    {
        try {
            return $this->freeSearch->getTrendingTopics($language);
        } catch (\Exception $e) {
            Log::error('Failed to get trending topics', ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'topics' => [],
            ];
        }
    }

    /**
     * Search for news
     */
    public function searchNews(string $query = '', string $language = 'ar'): array
    {
        try {
            return $this->freeSearch->searchNews($query, $language);
        } catch (\Exception $e) {
            Log::error('News search failed', ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'results' => [],
            ];
        }
    }

    /**
     * Get search capabilities
     */
    public function getCapabilities(): array
    {
        return [
            'providers' => ['duckduckgo'],
            'features' => [
                'web_search' => 'البحث في الويب باستخدام DuckDuckGo',
                'news_search' => 'البحث في الأخبار',
                'trending_topics' => 'المواضيع الرائجة',
            ],
            'supported_languages' => ['ar', 'en'],
            'max_results' => 20,
            'free_alternative' => true,
            'limitations' => [
                'يعتمد على DuckDuckGo API المجاني',
                'قد تكون النتائج محدودة مقارنة بالخدمات المدفوعة',
            ],
        ];
    }

    /**
     * Test search functionality
     */
    public function testSearch(): array
    {
        try {
            $testQuery = 'test search';
            $result = $this->search($testQuery, ['max_results' => 3]);

            return [
                'success' => true,
                'test_query' => $testQuery,
                'search_success' => $result['success'],
                'results_count' => count($result['results'] ?? []),
                'provider' => 'duckduckgo',
                'message' => 'اختبار البحث تم بنجاح',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'فشل في اختبار البحث',
            ];
        }
    }
}
