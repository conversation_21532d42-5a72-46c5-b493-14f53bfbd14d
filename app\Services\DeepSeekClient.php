<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Exceptions\ApiExceptions\{
    RateLimitException,
    AuthenticationException,
    ServerException,
    ClientException,
    CircuitBreakerException
};

class DeepSeekClient
{
    private string $apiKey;
    private string $baseUrl;
    private int $timeout;

    private int $maxRetries;
    private int $initialRetryDelay;
    private float $backoffFactor;
    private int $failureCount = 0;
    private ?int $lastFailureTime = null;
    private string $circuitState = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    private int $failureThreshold;
    private int $circuitCooldown;

    public function __construct()
    {
        $this->validateConfig([
            'api_key' => 'required|string',
            'base_url' => 'required|url',
            'timeout' => 'required|integer|min:1',
            'max_retries' => 'required|integer|min:0',
            'initial_retry_delay' => 'required|integer|min:100',
            'backoff_factor' => 'required|numeric|min:1',
            'failure_threshold' => 'required|integer|min:1',
            'circuit_cooldown' => 'required|integer|min:1000',
        ]);

        $this->apiKey = config('services.deepseek.api_key');
        $this->baseUrl = config('services.deepseek.base_url', 'https://api.deepseek.com');
        $this->timeout = config('services.deepseek.timeout', 30);
        $this->maxRetries = config('services.deepseek.max_retries', 3);
        $this->initialRetryDelay = config('services.deepseek.initial_retry_delay', 1000); // ms
        $this->backoffFactor = config('services.deepseek.backoff_factor', 2.0);
        $this->failureThreshold = config('services.deepseek.failure_threshold', 5);
        $this->circuitCooldown = config('services.deepseek.circuit_cooldown', 30000); // ms
    }

    private function validateConfig(array $rules): void
    {
        foreach ($rules as $key => $rule) {
            $value = config("services.deepseek.{$key}");

            if (str_contains($rule, 'required') && empty($value)) {
                throw new \InvalidArgumentException("Missing required configuration: services.deepseek.{$key}");
            }

            if ($rule === 'url' && !filter_var($value, FILTER_VALIDATE_URL)) {
                throw new \InvalidArgumentException("Invalid URL in configuration: services.deepseek.{$key}");
            }

            if ($rule === 'integer' && !is_int($value)) {
                throw new \InvalidArgumentException("Invalid integer in configuration: services.deepseek.{$key}");
            }

            if ($rule === 'numeric' && !is_numeric($value)) {
                throw new \InvalidArgumentException("Invalid number in configuration: services.deepseek.{$key}");
            }

            if (str_contains($rule, 'min:') && $value < (int)explode(':', $rule)[1]) {
                throw new \InvalidArgumentException(
                    "Value too small for configuration: services.deepseek.{$key} (min: " . explode(':', $rule)[1] . ")"
                );
            }
        }
    }

    public function chat(array $messages, array $options = []): array
    {
        try {
            $payload = [
                'model' => $options['model'] ?? 'deepseek-chat',
                'messages' => $messages,
                'max_tokens' => $options['max_tokens'] ?? 2000,
                'temperature' => $options['temperature'] ?? 0.7,
                'stream' => false,
            ];

            $response = $this->retryRequest(function() use ($payload) {
                return Http::withHeaders([
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ])
                ->timeout($this->timeout)
                ->post($this->baseUrl . '/v1/chat/completions', $payload);
            });

            if (!$response->successful()) {
                $status = $response->status();
                $body = $response->body();

                Log::error('DeepSeek API Error', [
                    'status' => $status,
                    'body' => $body,
                ]);

                $retryAfter = $response->header('Retry-After');

                switch ($status) {
                    case 401:
                    case 403:
                        throw new AuthenticationException('DeepSeek API authentication failed', $body);
                    case 429:
                        throw new RateLimitException('DeepSeek API rate limit exceeded', $retryAfter);
                    case 400:
                    case 404:
                    case 422:
                        throw new ClientException("DeepSeek API client error (HTTP $status)", $body);
                    default:
                        if ($status >= 500) {
                            throw new ServerException("DeepSeek API server error (HTTP $status)", $body);
                        }
                        throw new \Exception("DeepSeek API request failed (HTTP $status): $body");
                }
            }

            $data = $response->json();

            return [
                'success' => true,
                'content' => $data['choices'][0]['message']['content'] ?? '',
                'usage' => $data['usage'] ?? [],
                'model' => $data['model'] ?? 'deepseek-chat',
                'raw_response' => $data,
            ];

        } catch (\Exception $e) {
            Log::error('DeepSeek Client Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'content' => '',
            ];
        }
    }

    public function buildMessages(string $systemPrompt, string $userMessage, array $conversationHistory = []): array
    {
        $messages = [];

        // Add system message
        if (!empty($systemPrompt)) {
            $messages[] = [
                'role' => 'system',
                'content' => $systemPrompt,
            ];
        }

        // Add conversation history
        foreach ($conversationHistory as $message) {
            $messages[] = [
                'role' => $message['role'] === 'widdx' ? 'assistant' : $message['role'],
                'content' => $message['content'],
            ];
        }

        // Add current user message
        $messages[] = [
            'role' => 'user',
            'content' => $userMessage,
        ];

        return $messages;
    }

    public function isConfigured(): bool
    {
        return !empty($this->apiKey);
    }

    private function retryRequest(callable $request, int $attempt = 1)
    {
        $startTime = microtime(true);
        try {
            if ($this->circuitState === 'OPEN') {
                $timeSinceLastFailure = (int)(microtime(true) * 1000) - $this->lastFailureTime;
                $remainingCooldown = $this->circuitCooldown - $timeSinceLastFailure;

                if ($timeSinceLastFailure < $this->circuitCooldown) {
                    throw new CircuitBreakerException(
                        'DeepSeek API circuit breaker is open - requests blocked',
                        $remainingCooldown
                    );
                }
                $this->circuitState = 'HALF_OPEN';
            }

            $response = $request();

            if ($response->successful()) {
                $duration = round((microtime(true) - $startTime) * 1000, 2);
                Log::info('DeepSeek API Request', [
                    'status' => $response->status(),
                    'duration_ms' => $duration,
                    'attempt' => $attempt,
                    'circuit_state' => $this->circuitState,
                    'success' => true
                ]);

                if ($this->circuitState === 'HALF_OPEN') {
                    $this->resetCircuit();
                }
                return $response;
            }

            if ($attempt >= $this->maxRetries) {
                $this->recordFailure();
                return $response;
            }

            $status = $response->status();
            $retryAfter = $response->header('Retry-After');
            $delay = $retryAfter
                ? (int)$retryAfter * 1000
                : $this->calculateRetryDelay($attempt);

            $duration = round((microtime(true) - $startTime) * 1000, 2);
            Log::warning("DeepSeek API request failed (attempt $attempt/$this->maxRetries)", [
                'status' => $status,
                'duration_ms' => $duration,
                'retry_in_ms' => $delay,
                'circuit_state' => $this->circuitState,
                'success' => false
            ]);

            usleep($delay * 1000);
            return $this->retryRequest($request, $attempt + 1);

        } catch (\Exception $e) {
            if ($attempt >= $this->maxRetries) {
                $this->recordFailure();
                throw $e;
            }

            $delay = $this->calculateRetryDelay($attempt);
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            Log::warning("DeepSeek API request failed (attempt $attempt/$this->maxRetries)", [
                'error' => $e->getMessage(),
                'duration_ms' => $duration,
                'retry_in_ms' => $delay,
                'circuit_state' => $this->circuitState,
                'success' => false
            ]);

            usleep($delay * 1000);
            return $this->retryRequest($request, $attempt + 1);
        }
    }

    private function recordFailure(): void
    {
        $this->failureCount++;
        $this->lastFailureTime = (int)(microtime(true) * 1000);

        if ($this->failureCount >= $this->failureThreshold) {
            $this->circuitState = 'OPEN';
            Log::critical('DeepSeek API circuit breaker tripped', [
                'failure_count' => $this->failureCount,
                'last_failure_time' => $this->lastFailureTime,
            ]);
        }
    }

    private function resetCircuit(): void
    {
        $this->failureCount = 0;
        $this->lastFailureTime = null;
        $this->circuitState = 'CLOSED';
        Log::info('DeepSeek API circuit breaker reset');
    }

    private function calculateRetryDelay(int $attempt): int
    {
        // Exponential backoff with jitter
        $exponentialDelay = $this->initialRetryDelay * pow($this->backoffFactor, $attempt - 1);
        $jitter = rand(0, $this->initialRetryDelay / 2);
        return (int)min($exponentialDelay + $jitter, 10000); // Max 10 seconds
    }
}
