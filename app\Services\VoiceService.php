<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class VoiceService
{
    private GeminiClient $gemini;
    private FreeVoiceService $freeVoice;

    public function __construct(GeminiClient $gemini, FreeVoiceService $freeVoice)
    {
        $this->gemini = $gemini;
        $this->freeVoice = $freeVoice;
    }

    /**
     * Convert text to speech using Gemini (Browser-based)
     */
    public function textToSpeech(string $text, array $options = []): array
    {
        try {
            $language = $options['language'] ?? 'ar';
            $speed = $options['speed'] ?? 1.0;
            $pitch = $options['pitch'] ?? 1.0;

            Log::info('Text-to-speech started (Gemini Browser)', [
                'text_length' => strlen($text),
                'language' => $language,
            ]);

            // Use browser-based TTS since Gemini doesn't have direct TTS API
            $result = $this->freeVoice->generateBrowserTTS($text, [
                'language' => $language,
                'speed' => $speed,
                'pitch' => $pitch,
            ]);

            Log::info('Text-to-speech completed', [
                'success' => $result['success'],
                'method' => 'browser_tts',
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Text-to-speech failed', [
                'error' => $e->getMessage(),
                'text_length' => strlen($text),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'provider' => 'gemini_browser',
            ];
        }
    }

    /**
     * Convert speech to text using Gemini (Browser-based)
     */
    public function speechToText(string $audioFilePath, array $options = []): array
    {
        try {
            $language = $options['language'] ?? 'ar';

            Log::info('Speech-to-text started (Gemini Browser)', [
                'audio_file' => $audioFilePath,
                'language' => $language,
            ]);

            // Use browser-based STT since Gemini doesn't have direct STT API
            $result = $this->freeVoice->generateBrowserSTT([
                'language' => $language,
            ]);

            Log::info('Speech-to-text completed', [
                'success' => $result['success'],
                'method' => 'browser_stt',
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Speech-to-text failed', [
                'error' => $e->getMessage(),
                'audio_file' => $audioFilePath,
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'provider' => 'gemini_browser',
                'text' => '',
            ];
        }
    }

    /**
     * Get available voices for Gemini (Browser-based)
     */
    public function getAvailableVoices(): array
    {
        return [
            'gemini_browser' => [
                'ar' => [
                    'ar-SA' => 'Arabic (Saudi Arabia)',
                    'ar-EG' => 'Arabic (Egypt)',
                ],
                'en' => [
                    'en-US' => 'English (US)',
                    'en-GB' => 'English (UK)',
                ],
            ],
        ];
    }

    /**
     * Get voice service capabilities
     */
    public function getCapabilities(): array
    {
        return [
            'providers' => ['gemini_browser'],
            'features' => [
                'text_to_speech' => 'تحويل النص إلى كلام باستخدام متصفح الويب',
                'speech_to_text' => 'التعرف على الكلام باستخدام متصفح الويب',
            ],
            'supported_languages' => ['ar', 'en', 'fr', 'es', 'de'],
            'supported_formats' => ['browser_based'],
            'limitations' => [
                'يتطلب متصفح ويب حديث',
                'يحتاج إذن المستخدم للوصول للميكروفون',
                'جودة الصوت تعتمد على المتصفح والنظام',
            ],
            'free_alternative' => true,
        ];
    }

    /**
     * Test voice service functionality
     */
    public function testVoiceService(): array
    {
        try {
            $testText = 'مرحبا، هذا اختبار للخدمة الصوتية';
            
            $ttsResult = $this->textToSpeech($testText, [
                'language' => 'ar',
                'speed' => 1.0,
            ]);

            return [
                'success' => true,
                'tts_test' => $ttsResult['success'],
                'provider' => 'gemini_browser',
                'message' => 'اختبار الخدمة الصوتية تم بنجاح',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'فشل في اختبار الخدمة الصوتية',
            ];
        }
    }
}
