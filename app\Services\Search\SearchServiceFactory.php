<?php

namespace App\Services\Search;

use App\Contracts\Search\SearchServiceInterface;
use App\Models\User;
use App\Exceptions\Search\SearchException;
use App\Exceptions\Search\SearchConfigurationException;
use App\Services\FreeSearchService;
use App\Services\LiveSearchService;
use App\Services\UnlimitedSearchService;
use App\Services\DeepSearchService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Illuminate\Container\Container;

class SearchServiceFactory
{
    protected Container $container;
    protected array $serviceRegistry;
    protected array $userTierMapping;
    protected string $fallbackService;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->serviceRegistry = [];
        $this->userTierMapping = $this->loadUserTierMapping();
        $this->fallbackService = 'free';
        
        $this->registerDefaultServices();
    }

    /**
     * Create appropriate search service based on user and requirements
     */
    public function createSearchService(?User $user = null, string $searchType = 'default'): SearchServiceInterface
    {
        try {
            $userTier = $this->determineUserTier($user);
            $serviceName = $this->selectServiceForTier($userTier, $searchType);
            
            Log::info('Creating search service', [
                'user_id' => $user?->id,
                'user_tier' => $userTier,
                'search_type' => $searchType,
                'selected_service' => $serviceName,
            ]);

            return $this->createService($serviceName);

        } catch (\Exception $e) {
            Log::warning('Failed to create requested search service, falling back', [
                'user_id' => $user?->id,
                'search_type' => $searchType,
                'error' => $e->getMessage(),
                'fallback_service' => $this->fallbackService,
            ]);

            return $this->createFallbackService();
        }
    }

    /**
     * Register a search service
     */
    public function registerService(string $name, string $serviceClass, array $config = []): void
    {
        if (!class_exists($serviceClass)) {
            throw new SearchConfigurationException("Service class {$serviceClass} does not exist");
        }

        if (!is_subclass_of($serviceClass, SearchServiceInterface::class)) {
            throw new SearchConfigurationException("Service class {$serviceClass} must implement SearchServiceInterface");
        }

        $this->serviceRegistry[$name] = [
            'class' => $serviceClass,
            'config' => $config,
            'enabled' => $config['enabled'] ?? true,
        ];

        Log::debug('Search service registered', [
            'name' => $name,
            'class' => $serviceClass,
            'enabled' => $config['enabled'] ?? true,
        ]);
    }

    /**
     * Get available services for a user
     */
    public function getAvailableServices(?User $user = null): array
    {
        $userTier = $this->determineUserTier($user);
        $availableServices = [];

        foreach ($this->serviceRegistry as $name => $config) {
            if (!$config['enabled']) {
                continue;
            }

            $tierRequirement = $config['config']['required_tier'] ?? 'free';
            
            if ($this->canUserAccessTier($userTier, $tierRequirement)) {
                $service = $this->createService($name);
                
                if ($service->isAvailable()) {
                    $availableServices[$name] = [
                        'name' => $name,
                        'capabilities' => $service->getCapabilities(),
                        'tier_requirement' => $tierRequirement,
                        'available' => true,
                    ];
                }
            }
        }

        return $availableServices;
    }

    /**
     * Check service availability
     */
    public function isServiceAvailable(string $serviceName): bool
    {
        try {
            if (!isset($this->serviceRegistry[$serviceName])) {
                return false;
            }

            if (!$this->serviceRegistry[$serviceName]['enabled']) {
                return false;
            }

            $service = $this->createService($serviceName);
            return $service->isAvailable();

        } catch (\Exception $e) {
            Log::warning('Service availability check failed', [
                'service' => $serviceName,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get service health status
     */
    public function getServiceHealth(): array
    {
        $health = [];

        foreach ($this->serviceRegistry as $name => $config) {
            try {
                $service = $this->createService($name);
                $health[$name] = [
                    'name' => $name,
                    'enabled' => $config['enabled'],
                    'available' => $service->isAvailable(),
                    'capabilities' => $service->getCapabilities(),
                    'status' => 'healthy',
                ];
            } catch (\Exception $e) {
                $health[$name] = [
                    'name' => $name,
                    'enabled' => $config['enabled'],
                    'available' => false,
                    'status' => 'unhealthy',
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $health;
    }

    /**
     * Determine user tier based on user model and configuration
     */
    protected function determineUserTier(?User $user): string
    {
        if (!$user) {
            return 'guest';
        }

        // Check if user has subscription information
        // This is a simplified implementation - in production you might have
        // a subscription model or user tier field
        if (method_exists($user, 'getSubscriptionTier')) {
            return $user->getSubscriptionTier();
        }

        // Check user attributes for tier determination
        if (isset($user->tier)) {
            return $user->tier;
        }

        if (isset($user->subscription_type)) {
            return $user->subscription_type;
        }

        // Default to free for authenticated users
        return 'free';
    }

    /**
     * Select appropriate service for user tier and search type
     */
    protected function selectServiceForTier(string $userTier, string $searchType): string
    {
        // Handle specific search type requests
        if ($searchType !== 'default') {
            $specificService = $this->getServiceForSearchType($searchType, $userTier);
            if ($specificService) {
                return $specificService;
            }
        }

        // Use tier mapping for default service selection
        $tierServices = $this->userTierMapping[$userTier] ?? [];
        
        foreach ($tierServices as $serviceName) {
            if ($this->isServiceAvailable($serviceName)) {
                return $serviceName;
            }
        }

        // Fallback to lower tier services
        return $this->getFallbackServiceForTier($userTier);
    }

    /**
     * Get service for specific search type
     */
    protected function getServiceForSearchType(string $searchType, string $userTier): ?string
    {
        $searchTypeMapping = [
            'deep' => ['deep', 'unlimited', 'free'],
            'unlimited' => ['unlimited', 'free'],
            'live' => ['live', 'free'],
            'free' => ['free'],
        ];

        $possibleServices = $searchTypeMapping[$searchType] ?? [];
        
        foreach ($possibleServices as $serviceName) {
            $serviceConfig = $this->serviceRegistry[$serviceName] ?? null;
            if (!$serviceConfig) {
                continue;
            }

            $requiredTier = $serviceConfig['config']['required_tier'] ?? 'free';
            
            if ($this->canUserAccessTier($userTier, $requiredTier) && 
                $this->isServiceAvailable($serviceName)) {
                return $serviceName;
            }
        }

        return null;
    }

    /**
     * Get fallback service for tier
     */
    protected function getFallbackServiceForTier(string $userTier): string
    {
        $fallbackChain = [
            'premium' => ['unlimited', 'free'],
            'unlimited' => ['unlimited', 'free'],
            'free' => ['free'],
            'guest' => ['free'],
        ];

        $fallbacks = $fallbackChain[$userTier] ?? ['free'];
        
        foreach ($fallbacks as $serviceName) {
            if ($this->isServiceAvailable($serviceName)) {
                return $serviceName;
            }
        }

        return $this->fallbackService;
    }

    /**
     * Check if user can access a tier
     */
    protected function canUserAccessTier(string $userTier, string $requiredTier): bool
    {
        $tierHierarchy = [
            'guest' => 0,
            'free' => 1,
            'unlimited' => 2,
            'premium' => 3,
        ];

        $userLevel = $tierHierarchy[$userTier] ?? 0;
        $requiredLevel = $tierHierarchy[$requiredTier] ?? 0;

        return $userLevel >= $requiredLevel;
    }

    /**
     * Create service instance
     */
    protected function createService(string $serviceName): SearchServiceInterface
    {
        if (!isset($this->serviceRegistry[$serviceName])) {
            throw new SearchException("Service '{$serviceName}' is not registered");
        }

        $serviceConfig = $this->serviceRegistry[$serviceName];
        
        if (!$serviceConfig['enabled']) {
            throw new SearchException("Service '{$serviceName}' is disabled");
        }

        try {
            return $this->container->make($serviceConfig['class']);
        } catch (\Exception $e) {
            throw new SearchException("Failed to create service '{$serviceName}': " . $e->getMessage());
        }
    }

    /**
     * Create fallback service
     */
    protected function createFallbackService(): SearchServiceInterface
    {
        try {
            return $this->createService($this->fallbackService);
        } catch (\Exception $e) {
            throw new SearchException("Failed to create fallback service: " . $e->getMessage());
        }
    }

    /**
     * Load user tier mapping from configuration
     */
    protected function loadUserTierMapping(): array
    {
        return [
            'guest' => ['free'],
            'free' => ['free'],
            'unlimited' => ['unlimited', 'free'],
            'premium' => ['deep', 'unlimited', 'free'],
        ];
    }

    /**
     * Register default services based on configuration
     */
    protected function registerDefaultServices(): void
    {
        // Register Free Search Service
        if (Config::get('widdx.features.live_search.enabled', true)) {
            $this->registerService('free', FreeSearchService::class, [
                'enabled' => true,
                'required_tier' => 'free',
                'cache_minutes' => Config::get('widdx.features.live_search.cache_minutes', 15),
                'max_results' => Config::get('widdx.features.live_search.max_results', 10),
            ]);
        }

        // Register Live Search Service (alias for free)
        if (Config::get('widdx.features.live_search.enabled', true)) {
            $this->registerService('live', LiveSearchService::class, [
                'enabled' => true,
                'required_tier' => 'free',
                'cache_minutes' => Config::get('widdx.features.live_search.cache_minutes', 15),
                'max_results' => Config::get('widdx.features.live_search.max_results', 10),
            ]);
        }

        // Register Unlimited Search Service
        if (Config::get('widdx.features.unlimited_search.enabled', true)) {
            $this->registerService('unlimited', UnlimitedSearchService::class, [
                'enabled' => true,
                'required_tier' => 'unlimited',
                'cache_minutes' => Config::get('widdx.features.unlimited_search.cache_duration', 5),
                'parallel_enabled' => Config::get('widdx.features.unlimited_search.parallel_enabled', true),
                'max_parallel_requests' => Config::get('widdx.features.unlimited_search.max_parallel_requests', 10),
            ]);
        }

        // Register Deep Search Service
        if (Config::get('widdx.features.deep_search.enabled', true)) {
            $this->registerService('deep', DeepSearchService::class, [
                'enabled' => true,
                'required_tier' => 'premium',
                'cache_minutes' => Config::get('widdx.features.deep_search.cache_minutes', 30),
                'max_sources' => Config::get('widdx.features.deep_search.max_sources', 5),
            ]);
        }
    }

    /**
     * Get registered services
     */
    public function getRegisteredServices(): array
    {
        return array_keys($this->serviceRegistry);
    }

    /**
     * Get service configuration
     */
    public function getServiceConfiguration(string $serviceName): ?array
    {
        return $this->serviceRegistry[$serviceName] ?? null;
    }

    /**
     * Enable/disable a service
     */
    public function setServiceEnabled(string $serviceName, bool $enabled): void
    {
        if (isset($this->serviceRegistry[$serviceName])) {
            $this->serviceRegistry[$serviceName]['enabled'] = $enabled;
            
            Log::info('Search service status changed', [
                'service' => $serviceName,
                'enabled' => $enabled,
            ]);
        }
    }

    /**
     * Get factory statistics
     */
    public function getStatistics(): array
    {
        $stats = [
            'total_services' => count($this->serviceRegistry),
            'enabled_services' => 0,
            'available_services' => 0,
            'services' => [],
        ];

        foreach ($this->serviceRegistry as $name => $config) {
            if ($config['enabled']) {
                $stats['enabled_services']++;
                
                if ($this->isServiceAvailable($name)) {
                    $stats['available_services']++;
                }
            }

            $stats['services'][$name] = [
                'enabled' => $config['enabled'],
                'available' => $this->isServiceAvailable($name),
                'required_tier' => $config['config']['required_tier'] ?? 'free',
            ];
        }

        return $stats;
    }
}