import './bootstrap';

// تهيئة WIDDX AI الجديد
document.addEventListener('DOMContentLoaded', function() {
    console.log('WIDDX AI - تم تحميل الواجهة الجديدة');

    // تهيئة المكونات
    if (typeof WiddxUIComponents !== 'undefined') {
        window.widdxUI = new WiddxUIComponents();
        console.log('✅ تم تهيئة مكونات UI');
    }

    if (typeof WiddxSidebar !== 'undefined') {
        window.widdxSidebar = new WiddxSidebar();
        console.log('✅ تم تهيئة الشريط الجانبي');
    }

    if (typeof WiddxInputArea !== 'undefined') {
        window.widdxInputArea = new WiddxInputArea();
        console.log('✅ تم تهيئة منطقة الإدخال');
    }

    if (typeof WiddxChatArea !== 'undefined') {
        window.widdxChatArea = new WiddxChatArea();
        console.log('✅ تم تهيئة منطقة المحادثة');
    }

    console.log('🚀 WIDDX AI جاهز للاستخدام!');
});
