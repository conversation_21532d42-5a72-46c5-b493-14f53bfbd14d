<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>WIDDX AI - Advanced Intelligent Assistant</title>

    {{-- Fonts --}}
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    {{-- Styles --}}
    @vite(['resources/css/app.css', 'resources/css/design-system.css'])

    <style>
        /* Additional custom styles for the new interface */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            background: var(--widdx-bg-primary);
            color: var(--widdx-text-primary);
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .widdx-app {
            display: flex;
            height: 100vh;
            width: 100vw;
        }

        .widdx-main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            margin-right: 320px;
            transition: margin-right 0.3s ease-in-out;
        }

        .widdx-main-content.sidebar-collapsed {
            margin-right: 64px;
        }

        @media (max-width: 1024px) {
            .widdx-main-content {
                margin-right: 0;
            }
        }

        /* Loading screen */
        .widdx-loading {
            position: fixed;
            inset: 0;
            background: var(--widdx-bg-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }

        .widdx-loading.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .widdx-loading-content {
            text-align: center;
        }

        .widdx-loading-logo {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, var(--widdx-primary), var(--widdx-secondary));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            animation: pulse 2s infinite;
        }

        .widdx-loading-text {
            color: var(--widdx-text-secondary);
            font-size: 14px;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .widdx-app {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    {{-- Loading Screen --}}
    <div id="loading-screen" class="widdx-loading">
        <div class="widdx-loading-content">
            <div class="widdx-loading-logo">
                <span class="text-white font-bold text-2xl">W</span>
            </div>
            <div class="widdx-loading-text">Loading WIDDX AI...</div>
        </div>
    </div>

    {{-- Main Application --}}
    <div id="widdx-app" class="widdx-app">
        {{-- Sidebar --}}
        @include('components.sidebar')

        {{-- Main Content Area --}}
        <div id="main-content" class="widdx-main-content">
            {{-- Chat Area --}}
            @include('components.chat-area')

            {{-- Input Area --}}
            @include('components.input-area')
        </div>
    </div>

    {{-- Scripts --}}
    <script>
        // Global configuration
        window.WIDDX_CONFIG = {
            apiUrl: '{{ config("app.url") }}',
            csrfToken: '{{ csrf_token() }}',
            locale: 'ar',
            features: {
                search: {{ config('widdx.features.search.enabled', true) ? 'true' : 'false' }},
                imageGeneration: {{ config('widdx.features.image_generation.enabled', true) ? 'true' : 'false' }},
                voiceInput: {{ config('widdx.features.voice.enabled', true) ? 'true' : 'false' }},
                thinkMode: {{ config('widdx.features.think_mode.enabled', true) ? 'true' : 'false' }},
                documentAnalysis: {{ config('widdx.features.document_analysis.enabled', true) ? 'true' : 'false' }},
                vision: {{ config('widdx.features.vision.enabled', true) ? 'true' : 'false' }}
            },
            personalities: @json($personalities ?? []),
            maxFileSize: {{ config('widdx.max_file_size', 10485760) }}, // 10MB
            maxFiles: {{ config('widdx.max_files', 10) }}
        };

        // Initialize application
        document.addEventListener('DOMContentLoaded', function() {
            // Hide loading screen after a short delay
            setTimeout(() => {
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    loadingScreen.classList.add('hidden');
                    setTimeout(() => {
                        loadingScreen.remove();
                    }, 500);
                }
            }, 1000);

            // Initialize main application components
            initializeWiddxApp();
        });

        function initializeWiddxApp() {
            // Setup global event listeners
            setupGlobalEventListeners();

            // Setup responsive behavior
            setupResponsiveBehavior();

            // Setup keyboard shortcuts
            setupGlobalKeyboardShortcuts();

            // Initialize chat system
            initializeChatSystem();

            console.log('WIDDX AI initialized successfully');
        }

        function setupGlobalEventListeners() {
            // Handle sidebar toggle for mobile
            const mobileSidebarToggle = document.getElementById('mobile-sidebar-toggle');
            if (mobileSidebarToggle && window.widdxSidebar) {
                mobileSidebarToggle.addEventListener('click', () => {
                    window.widdxSidebar.toggleSidebar();
                });
            }

            // Handle window resize
            window.addEventListener('resize', debounce(() => {
                updateResponsiveLayout();
            }, 250));

            // Handle online/offline status
            window.addEventListener('online', () => {
                updateConnectionStatus(true);
            });

            window.addEventListener('offline', () => {
                updateConnectionStatus(false);
            });
        }

        function setupResponsiveBehavior() {
            const mainContent = document.getElementById('main-content');
            const sidebar = document.getElementById('widdx-sidebar');

            function updateLayout() {
                const isMobile = window.innerWidth < 1024;
                const isCollapsed = sidebar?.classList.contains('collapsed');

                if (mainContent) {
                    if (isMobile) {
                        mainContent.classList.remove('sidebar-collapsed');
                    } else {
                        mainContent.classList.toggle('sidebar-collapsed', isCollapsed);
                    }
                }
            }

            // Initial layout update
            updateLayout();

            // Listen for sidebar state changes
            if (sidebar) {
                const observer = new MutationObserver(updateLayout);
                observer.observe(sidebar, { attributes: true, attributeFilter: ['class'] });
            }
        }

        function setupGlobalKeyboardShortcuts() {
            document.addEventListener('keydown', (e) => {
                // Global shortcuts
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case 'k':
                            e.preventDefault();
                            focusSearchOrInput();
                            break;
                        case '/':
                            e.preventDefault();
                            showCommandPalette();
                            break;
                    }
                }

                // Function keys
                switch (e.key) {
                    case 'F1':
                        e.preventDefault();
                        showHelp();
                        break;
                }
            });
        }

        function initializeChatSystem() {
            // Connect all chat components
            if (window.widdxInputArea && window.widdxChatArea) {
                // Listen for send message events
                document.addEventListener('widdx-send-message', (e) => {
                    handleSendMessage(e.detail);
                });

                // Listen for feature activation events
                document.addEventListener('widdx-feature-activated', (e) => {
                    handleFeatureActivation(e.detail.feature);
                });

                // Listen for quick action events
                document.addEventListener('widdx-quick-action', (e) => {
                    handleQuickAction(e.detail.action);
                });
            }
        }

        function handleSendMessage(data) {
            const { message, files } = data;

            // Add user message to chat
            if (window.widdxChatArea) {
                window.widdxChatArea.addMessage('user', message, { status: 'sending' });
                window.widdxChatArea.showTyping();
            }

            // Send to backend
            sendMessageToBackend(message, files)
                .then(response => {
                    if (window.widdxChatArea) {
                        window.widdxChatArea.hideTyping();
                        window.widdxChatArea.addMessage('ai', response.message);
                    }
                })
                .catch(error => {
                    console.error('Error sending message:', error);
                    if (window.widdxChatArea) {
                        window.widdxChatArea.hideTyping();
                        window.widdxChatArea.addMessage('ai', 'عذراً، حدث خطأ أثناء معالجة رسالتك. يرجى المحاولة مرة أخرى.');
                    }
                });
        }

        async function sendMessageToBackend(message, files = []) {
            const formData = new FormData();
            formData.append('message', message);
            formData.append('session_id', getSessionId());

            files.forEach((file, index) => {
                formData.append(`files[${index}]`, file);
            });

            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': window.WIDDX_CONFIG.csrfToken
                },
                body: formData
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            return await response.json();
        }

        function handleFeatureActivation(feature) {
            console.log('Feature activated:', feature);
            // Handle specific feature activation logic here
        }

        function handleQuickAction(action) {
            console.log('Quick action:', action);
            // Handle quick action logic here
        }

        function updateConnectionStatus(isOnline) {
            const statusElement = document.getElementById('chat-status');
            if (statusElement) {
                statusElement.textContent = isOnline ? 'متصل - جاهز للمساعدة' : 'غير متصل';
            }
        }

        function getSessionId() {
            let sessionId = localStorage.getItem('widdx-session-id');
            if (!sessionId) {
                sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                localStorage.setItem('widdx-session-id', sessionId);
            }
            return sessionId;
        }

        function focusSearchOrInput() {
            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                messageInput.focus();
            }
        }

        function showCommandPalette() {
            // Show command palette (to be implemented)
            console.log('Command palette requested');
        }

        function showHelp() {
            // Show help modal (to be implemented)
            console.log('Help requested');
        }

        function updateResponsiveLayout() {
            // Update layout based on screen size
            setupResponsiveBehavior();
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>

    {{-- Load component scripts --}}
    @vite([
        'resources/js/app.js',
        'resources/js/components.js',
        'resources/js/sidebar.js',
        'resources/js/input-area.js'
    ])
</body>
</html>
