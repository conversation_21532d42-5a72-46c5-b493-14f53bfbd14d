<?php

namespace App\Exceptions\ApiExceptions;

use Exception;

class AuthenticationException extends Exception
{
    protected $code = 401;
    protected $message = 'API authentication failed';

    public function __construct(?string $message = null, ?string $details = null)
    {
        if ($message !== null) {
            $this->message = $message;
        }

        if ($details !== null) {
            $this->message .= " - Details: {$details}";
        }

        parent::__construct($this->message, $this->code);
    }
}
