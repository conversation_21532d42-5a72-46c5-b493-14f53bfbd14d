<?php

namespace App\Services;

use App\Models\KnowledgeEntry;
use App\Models\ConversationInsight;
use App\Models\UserPattern;
use App\Models\TopicRelationship;
use App\Models\PersonalityEvolution;
use App\Models\LearningSession;
use App\Models\ContextMemory;
use App\Models\ChatSession;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class WiddxKnowledgeService
{
    /**
     * Learn from a conversation interaction
     */
    public function learnFromConversation(
        ChatSession $session,
        string $userMessage,
        string $widdxResponse,
        array $context = []
    ): array {
        try {
            DB::beginTransaction();

            $learningResults = [
                'knowledge_entries' => [],
                'insights' => [],
                'patterns' => [],
                'personality_updates' => [],
            ];

            // Extract knowledge from the conversation
            $knowledgeItems = $this->extractKnowledge($userMessage, $widdxResponse, $context);
            foreach ($knowledgeItems as $item) {
                $entry = $this->storeKnowledge($item);
                $learningResults['knowledge_entries'][] = $entry;
            }

            // Generate conversation insights
            $insights = $this->generateInsights($session, $userMessage, $widdxResponse, $context);
            foreach ($insights as $insight) {
                $storedInsight = $this->storeInsight($session, $insight);
                $learningResults['insights'][] = $storedInsight;
            }

            // Update user patterns
            $patterns = $this->updateUserPatterns($session, $userMessage, $context);
            $learningResults['patterns'] = $patterns;

            // Evolve personality based on interaction
            $personalityUpdates = $this->evolvePersonality($userMessage, $widdxResponse, $context);
            $learningResults['personality_updates'] = $personalityUpdates;

            // Create learning session record
            $learningSession = LearningSession::create([
                'chat_session_id' => $session->id,
                'learned_items' => $learningResults,
                'learning_score' => $this->calculateLearningScore($learningResults),
                'primary_language' => $context['language'] ?? 'en',
            ]);

            DB::commit();

            Log::info('WIDDX learned from conversation', [
                'session_id' => $session->session_id,
                'learning_score' => $learningSession->learning_score,
                'items_learned' => count($learningResults['knowledge_entries']),
                'insights_generated' => count($learningResults['insights']),
            ]);

            return $learningResults;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to learn from conversation', [
                'error' => $e->getMessage(),
                'session_id' => $session->session_id,
            ]);

            return [
                'knowledge_entries' => [],
                'insights' => [],
                'patterns' => [],
                'personality_updates' => [],
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Extract knowledge items from conversation
     */
    private function extractKnowledge(string $userMessage, string $widdxResponse, array $context): array
    {
        $knowledgeItems = [];

        // Extract user preferences
        if ($this->containsPreference($userMessage)) {
            $knowledgeItems[] = [
                'topic' => 'user_preference',
                'category' => 'preference',
                'content' => $this->extractPreference($userMessage),
                'confidence_score' => 0.8,
                'language' => $context['language'] ?? 'en',
            ];
        }

        // Extract factual information
        $facts = $this->extractFacts($userMessage, $widdxResponse);
        foreach ($facts as $fact) {
            $knowledgeItems[] = [
                'topic' => $fact['topic'],
                'category' => 'fact',
                'content' => $fact['content'],
                'confidence_score' => $fact['confidence'],
                'language' => $context['language'] ?? 'en',
            ];
        }

        // Extract topics of interest
        $topics = $this->extractTopics($userMessage);
        foreach ($topics as $topic) {
            $knowledgeItems[] = [
                'topic' => $topic,
                'category' => 'topic_interest',
                'content' => "User showed interest in: {$topic}",
                'confidence_score' => 0.6,
                'language' => $context['language'] ?? 'en',
            ];
        }

        return $knowledgeItems;
    }

    /**
     * Store knowledge in the database
     */
    private function storeKnowledge(array $knowledgeItem): KnowledgeEntry
    {
        // Check if similar knowledge already exists
        $existing = KnowledgeEntry::where('topic', $knowledgeItem['topic'])
            ->where('category', $knowledgeItem['category'])
            ->where('language', $knowledgeItem['language'])
            ->first();

        if ($existing) {
            // Reinforce existing knowledge
            $existing->updateConfidence(0.1);
            $existing->recordUsage();
            return $existing;
        }

        // Create new knowledge entry
        return KnowledgeEntry::create($knowledgeItem);
    }

    /**
     * Generate insights from conversation
     */
    private function generateInsights(ChatSession $session, string $userMessage, string $widdxResponse, array $context): array
    {
        $insights = [];

        // Communication style insight
        $communicationStyle = $this->analyzeCommunicationStyle($userMessage);
        if ($communicationStyle) {
            $insights[] = [
                'insight_type' => 'communication_style',
                'insight_content' => $communicationStyle,
                'strength' => 0.7,
                'language' => $context['language'] ?? 'en',
            ];
        }

        // Topic interest insight
        $topicInterests = $this->analyzeTopicInterests($userMessage);
        foreach ($topicInterests as $interest) {
            $insights[] = [
                'insight_type' => 'topic_interest',
                'insight_content' => $interest,
                'strength' => 0.6,
                'language' => $context['language'] ?? 'en',
            ];
        }

        return $insights;
    }

    /**
     * Store conversation insight
     */
    private function storeInsight(ChatSession $session, array $insight): ConversationInsight
    {
        return ConversationInsight::create(array_merge($insight, [
            'chat_session_id' => $session->id,
        ]));
    }

    /**
     * Get relevant knowledge for a query
     */
    public function getRelevantKnowledge(string $query, string $language = 'en', int $limit = 10): array
    {
        return KnowledgeEntry::search($query)
            ->language($language)
            ->highConfidence()
            ->orderByDesc('confidence_score')
            ->orderByDesc('usage_count')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get WIDDX's current personality state
     */
    public function getCurrentPersonality(): array
    {
        return PersonalityEvolution::orderByDesc('last_updated_at')
            ->get()
            ->pluck('current_state', 'aspect')
            ->toArray();
    }

    // Helper methods for knowledge extraction
    private function containsPreference(string $message): bool
    {
        $preferenceIndicators = ['I like', 'I prefer', 'I love', 'I hate', 'I dislike', 'My favorite'];
        return collect($preferenceIndicators)->some(fn($indicator) => 
            stripos($message, $indicator) !== false
        );
    }

    private function extractPreference(string $message): string
    {
        // Simple preference extraction - can be enhanced with NLP
        return "User preference extracted from: " . substr($message, 0, 100);
    }

    private function extractFacts(string $userMessage, string $widdxResponse): array
    {
        // Placeholder for fact extraction logic
        return [];
    }

    private function extractTopics(string $message): array
    {
        // Simple topic extraction - can be enhanced with NLP
        $words = str_word_count(strtolower($message), 1);
        return array_filter($words, fn($word) => strlen($word) > 4);
    }

    private function analyzeCommunicationStyle(string $message): ?string
    {
        if (str_contains($message, '?')) {
            return 'Inquisitive - asks many questions';
        }
        if (str_contains($message, '!')) {
            return 'Expressive - uses exclamations';
        }
        return null;
    }

    private function analyzeTopicInterests(string $message): array
    {
        // Placeholder for topic interest analysis
        return [];
    }

    private function updateUserPatterns(ChatSession $session, string $userMessage, array $context): array
    {
        // Placeholder for user pattern updates
        return [];
    }

    private function evolvePersonality(string $userMessage, string $widdxResponse, array $context): array
    {
        // Placeholder for personality evolution
        return [];
    }

    private function calculateLearningScore(array $learningResults): float
    {
        $score = 0.0;
        $score += count($learningResults['knowledge_entries']) * 0.3;
        $score += count($learningResults['insights']) * 0.4;
        $score += count($learningResults['patterns']) * 0.2;
        $score += count($learningResults['personality_updates']) * 0.1;
        
        return min(1.0, $score);
    }
}
