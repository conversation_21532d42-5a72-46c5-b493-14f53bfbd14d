{{-- WIDDX AI Sidebar Component - Grok-inspired Design --}}
<aside id="widdx-sidebar" class="widdx-sidebar">
    {{-- Sidebar Header --}}
    <div class="widdx-sidebar-header">
        <div class="flex items-center justify-between p-4">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 widdx-gradient-primary rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-sm">W</span>
                </div>
                <h1 class="text-lg font-semibold text-widdx-text-primary sidebar-title">WIDDX AI</h1>
            </div>
            <button id="sidebar-toggle" class="widdx-btn widdx-btn-ghost p-2" data-tooltip="Collapse Sidebar">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
                </svg>
            </button>
        </div>
    </div>

    {{-- New Chat Button --}}
    <div class="px-4 pb-4">
        <button id="new-chat-btn" class="widdx-btn widdx-btn-primary w-full justify-center">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            <span class="sidebar-text">New Chat</span>
        </button>
    </div>

    {{-- Chat History Section --}}
    <div class="widdx-sidebar-section">
        <div class="px-4 py-2">
            <h3 class="text-xs font-medium text-widdx-text-tertiary uppercase tracking-wider sidebar-text">
                Recent Chats
            </h3>
        </div>
        <div class="widdx-chat-history" id="chat-history">
            {{-- Chat history items will be populated by JavaScript --}}
            <div class="widdx-chat-item active" data-chat-id="current">
                <div class="flex items-center space-x-3 p-3 mx-2 rounded-lg hover:bg-widdx-bg-hover cursor-pointer transition-colors">
                    <div class="w-2 h-2 bg-widdx-primary rounded-full flex-shrink-0"></div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm text-widdx-text-primary truncate sidebar-text">Current Chat</p>
                        <p class="text-xs text-widdx-text-tertiary sidebar-text">Now</p>
                    </div>
                    <button class="widdx-chat-options opacity-0 group-hover:opacity-100 transition-opacity">
                        <svg class="w-4 h-4 text-widdx-text-secondary" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    {{-- Quick Actions Section --}}
    <div class="widdx-sidebar-section">
        <div class="px-4 py-2">
            <h3 class="text-xs font-medium text-widdx-text-tertiary uppercase tracking-wider sidebar-text">
                Quick Actions
            </h3>
        </div>
        <div class="px-2 space-y-1">
            <button class="widdx-quick-action" data-action="search">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <span class="sidebar-text">Live Search</span>
            </button>
            <button class="widdx-quick-action" data-action="image-gen">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <span class="sidebar-text">Image Generation</span>
            </button>
            <button class="widdx-quick-action" data-action="voice">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                </svg>
                <span class="sidebar-text">Voice Control</span>
            </button>
            <button class="widdx-quick-action" data-action="think-mode">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                <span class="sidebar-text">Think Mode</span>
            </button>
        </div>
    </div>

    {{-- Settings Section --}}
    <div class="widdx-sidebar-section mt-auto">
        <div class="px-2 space-y-1">
            <button class="widdx-quick-action" data-action="settings">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span class="sidebar-text">Settings</span>
            </button>
            <button class="widdx-quick-action" data-action="help">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="sidebar-text">Help</span>
            </button>
        </div>
    </div>

    {{-- User Profile Section --}}
    <div class="widdx-sidebar-footer">
        <div class="p-4 border-t border-widdx-border-primary">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-widdx-bg-elevated rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-widdx-text-secondary" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-widdx-text-primary sidebar-text">User</p>
                    <p class="text-xs text-widdx-text-tertiary sidebar-text">Active now</p>
                </div>
                <button class="widdx-btn widdx-btn-ghost p-1" data-tooltip="User Options">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
</aside>

{{-- Sidebar Overlay for Mobile --}}
<div id="sidebar-overlay" class="widdx-sidebar-overlay"></div>

<style>
.widdx-sidebar {
    @apply fixed left-0 top-0 h-full w-80 bg-widdx-bg-secondary border-r border-widdx-border-primary flex flex-col z-50 transition-transform duration-300 ease-in-out;
}

.widdx-sidebar.collapsed {
    @apply -translate-x-full lg:translate-x-0 lg:w-16;
}

.widdx-sidebar.collapsed .sidebar-text,
.widdx-sidebar.collapsed .sidebar-title {
    @apply lg:hidden;
}

.widdx-sidebar-header {
    @apply flex-shrink-0;
}

.widdx-sidebar-section {
    @apply flex-1 overflow-y-auto scrollbar-thin;
}

.widdx-sidebar-footer {
    @apply flex-shrink-0;
}

.widdx-chat-item {
    @apply group;
}

.widdx-chat-item.active {
    @apply bg-widdx-bg-hover;
}

.widdx-chat-item.active .w-2 {
    @apply bg-widdx-primary animate-pulse;
}

.widdx-quick-action {
    @apply flex items-center space-x-3 w-full p-3 text-left text-widdx-text-secondary hover:text-widdx-text-primary hover:bg-widdx-bg-hover rounded-lg transition-all duration-200;
}

.widdx-quick-action:hover {
    @apply transform translate-x-1;
}

.widdx-sidebar-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden opacity-0 pointer-events-none transition-opacity duration-300;
}

.widdx-sidebar-overlay.active {
    @apply opacity-100 pointer-events-auto;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
    .widdx-sidebar {
        @apply -translate-x-full;
    }

    .widdx-sidebar.open {
        @apply translate-x-0;
    }
}

/* Animations */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.widdx-sidebar.animate-in {
    animation: slideInLeft 0.3s ease-out;
}
</style>
