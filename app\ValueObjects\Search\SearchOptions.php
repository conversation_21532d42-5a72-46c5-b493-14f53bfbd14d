<?php

namespace App\ValueObjects\Search;

class SearchOptions
{
    public function __construct(
        public readonly int $maxResults = 10,
        public readonly string $language = 'ar',
        public readonly string $region = 'SA',
        public readonly array $providers = ['duckduckgo'],
        public readonly bool $enableCache = true,
        public readonly int $timeout = 30,
        public readonly array $filters = [],
        public readonly bool $safeSearch = true,
        public readonly ?string $sortBy = null,
        public readonly ?string $timeRange = null
    ) {}

    /**
     * Create a new instance with modified values
     */
    public function with(array $changes): self
    {
        return new self(
            maxResults: $changes['maxResults'] ?? $this->maxResults,
            language: $changes['language'] ?? $this->language,
            region: $changes['region'] ?? $this->region,
            providers: $changes['providers'] ?? $this->providers,
            enableCache: $changes['enableCache'] ?? $this->enableCache,
            timeout: $changes['timeout'] ?? $this->timeout,
            filters: $changes['filters'] ?? $this->filters,
            safeSearch: $changes['safeSearch'] ?? $this->safeSearch,
            sortBy: $changes['sortBy'] ?? $this->sortBy,
            timeRange: $changes['timeRange'] ?? $this->timeRange
        );
    }

    /**
     * Convert to array for caching and serialization
     */
    public function toArray(): array
    {
        return [
            'maxResults' => $this->maxResults,
            'language' => $this->language,
            'region' => $this->region,
            'providers' => $this->providers,
            'enableCache' => $this->enableCache,
            'timeout' => $this->timeout,
            'filters' => $this->filters,
            'safeSearch' => $this->safeSearch,
            'sortBy' => $this->sortBy,
            'timeRange' => $this->timeRange,
        ];
    }

    /**
     * Create from array
     */
    public static function fromArray(array $data): self
    {
        return new self(
            maxResults: $data['maxResults'] ?? 10,
            language: $data['language'] ?? 'ar',
            region: $data['region'] ?? 'SA',
            providers: $data['providers'] ?? ['duckduckgo'],
            enableCache: $data['enableCache'] ?? true,
            timeout: $data['timeout'] ?? 30,
            filters: $data['filters'] ?? [],
            safeSearch: $data['safeSearch'] ?? true,
            sortBy: $data['sortBy'] ?? null,
            timeRange: $data['timeRange'] ?? null
        );
    }
}