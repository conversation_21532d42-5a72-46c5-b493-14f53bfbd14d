<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class ThinkModeService
{
    private DeepSeekClient $deepSeek;

    public function __construct(DeepSeekClient $deepSeek)
    {
        $this->deepSeek = $deepSeek;
    }

    /**
     * Process message with thinking mode enabled
     */
    public function processWithThinking(string $message, array $conversationHistory = [], array $options = []): array
    {
        try {
            Log::info('Think mode processing started', [
                'message_length' => strlen($message),
                'history_count' => count($conversationHistory),
            ]);

            // Step 1: Analyze the question/request
            $analysis = $this->analyzeRequest($message);
            
            // Step 2: Generate thinking steps
            $thinkingSteps = $this->generateThinkingSteps($message, $analysis);
            
            // Step 3: Process each thinking step
            $processedSteps = $this->processThinkingSteps($thinkingSteps, $conversationHistory);
            
            // Step 4: Generate final response based on thinking
            $finalResponse = $this->generateFinalResponse($message, $processedSteps, $conversationHistory, $options);

            $result = [
                'success' => true,
                'message' => $message,
                'thinking_process' => [
                    'analysis' => $analysis,
                    'steps' => $processedSteps,
                    'reasoning_chain' => $this->buildReasoningChain($processedSteps),
                ],
                'final_response' => $finalResponse,
                'metadata' => [
                    'thinking_time' => $this->calculateThinkingTime($processedSteps),
                    'complexity_level' => $analysis['complexity_level'],
                    'steps_count' => count($processedSteps),
                ],
            ];

            Log::info('Think mode processing completed', [
                'steps_count' => count($processedSteps),
                'complexity_level' => $analysis['complexity_level'],
                'success' => true,
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Think mode processing error', [
                'message' => substr($message, 0, 100),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => $message,
                'thinking_process' => null,
                'final_response' => 'عذراً، حدث خطأ أثناء معالجة طلبك في وضع التفكير.',
            ];
        }
    }

    /**
     * Analyze the user's request to understand complexity and requirements
     */
    private function analyzeRequest(string $message): array
    {
        try {
            $prompt = "Analyze the following user request and provide:
1. Type of request (question, problem-solving, creative, analytical, etc.)
2. Complexity level (1-5, where 1 is simple and 5 is very complex)
3. Required thinking approaches (logical, creative, analytical, research-based, etc.)
4. Key components that need to be addressed
5. Potential challenges or considerations

User request: '{$message}'

Respond in JSON format with the analysis.";

            $response = $this->deepSeek->chat([
                ['role' => 'system', 'content' => 'You are an expert at analyzing user requests to determine the best thinking approach.'],
                ['role' => 'user', 'content' => $prompt],
            ], ['max_tokens' => 600]);

            if ($response['success']) {
                $content = $response['content'];
                if (preg_match('/\{.*\}/s', $content, $matches)) {
                    $analysis = json_decode($matches[0], true);
                    if (is_array($analysis)) {
                        return $analysis;
                    }
                }
            }

            // Fallback analysis
            return [
                'type' => 'general_question',
                'complexity_level' => 3,
                'thinking_approaches' => ['logical', 'analytical'],
                'key_components' => ['understanding the question', 'providing accurate information'],
                'challenges' => ['ensuring accuracy', 'providing comprehensive answer'],
            ];

        } catch (\Exception $e) {
            Log::warning('Request analysis failed', ['error' => $e->getMessage()]);
            
            return [
                'type' => 'general_question',
                'complexity_level' => 2,
                'thinking_approaches' => ['logical'],
                'key_components' => ['basic understanding'],
                'challenges' => ['providing helpful response'],
            ];
        }
    }

    /**
     * Generate thinking steps based on the analysis
     */
    private function generateThinkingSteps(string $message, array $analysis): array
    {
        try {
            $complexityLevel = $analysis['complexity_level'] ?? 3;
            $thinkingApproaches = implode(', ', $analysis['thinking_approaches'] ?? ['logical']);

            $prompt = "Based on the user request '{$message}' and the analysis showing complexity level {$complexityLevel} requiring {$thinkingApproaches} thinking, generate a step-by-step thinking process.

Create 3-7 thinking steps that would lead to a comprehensive answer. Each step should:
1. Have a clear purpose
2. Build upon previous steps
3. Move toward the final answer
4. Show the reasoning process

Respond with an array of step objects, each with 'step_number', 'title', 'purpose', and 'approach' fields.";

            $response = $this->deepSeek->chat([
                ['role' => 'system', 'content' => 'You are an expert at breaking down complex problems into logical thinking steps.'],
                ['role' => 'user', 'content' => $prompt],
            ], ['max_tokens' => 800]);

            if ($response['success']) {
                $content = $response['content'];
                if (preg_match('/\[.*\]/s', $content, $matches)) {
                    $steps = json_decode($matches[0], true);
                    if (is_array($steps)) {
                        return $steps;
                    }
                }
            }

            // Fallback steps
            return [
                [
                    'step_number' => 1,
                    'title' => 'فهم السؤال',
                    'purpose' => 'تحليل وفهم ما يطلبه المستخدم بدقة',
                    'approach' => 'analytical',
                ],
                [
                    'step_number' => 2,
                    'title' => 'جمع المعلومات',
                    'purpose' => 'البحث عن المعلومات ذات الصلة',
                    'approach' => 'research',
                ],
                [
                    'step_number' => 3,
                    'title' => 'تحليل المعلومات',
                    'purpose' => 'تقييم وتحليل المعلومات المجمعة',
                    'approach' => 'analytical',
                ],
                [
                    'step_number' => 4,
                    'title' => 'صياغة الإجابة',
                    'purpose' => 'تكوين إجابة شاملة ومفيدة',
                    'approach' => 'logical',
                ],
            ];

        } catch (\Exception $e) {
            Log::warning('Thinking steps generation failed', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Process each thinking step
     */
    private function processThinkingSteps(array $thinkingSteps, array $conversationHistory): array
    {
        $processedSteps = [];
        $cumulativeThinking = '';

        foreach ($thinkingSteps as $step) {
            try {
                $stepResult = $this->processIndividualStep($step, $cumulativeThinking, $conversationHistory);
                $processedSteps[] = $stepResult;
                $cumulativeThinking .= $stepResult['output'] . "\n";

                // Small delay to simulate thinking time
                usleep(100000); // 0.1 seconds

            } catch (\Exception $e) {
                Log::warning('Step processing failed', [
                    'step' => $step['step_number'] ?? 'unknown',
                    'error' => $e->getMessage(),
                ]);

                $processedSteps[] = [
                    'step_number' => $step['step_number'] ?? count($processedSteps) + 1,
                    'title' => $step['title'] ?? 'خطوة تفكير',
                    'purpose' => $step['purpose'] ?? 'معالجة المعلومات',
                    'approach' => $step['approach'] ?? 'logical',
                    'output' => 'تم تخطي هذه الخطوة بسبب خطأ تقني.',
                    'confidence' => 0.5,
                    'processing_time' => 0,
                ];
            }
        }

        return $processedSteps;
    }

    /**
     * Process an individual thinking step
     */
    private function processIndividualStep(array $step, string $previousThinking, array $conversationHistory): array
    {
        $startTime = microtime(true);

        $prompt = "You are processing step {$step['step_number']}: '{$step['title']}'
Purpose: {$step['purpose']}
Approach: {$step['approach']}

Previous thinking:
{$previousThinking}

Conversation history:
" . $this->formatConversationHistory($conversationHistory) . "

Process this thinking step and provide your reasoning and conclusions for this specific step. Be thorough but focused on this step's purpose.";

        $response = $this->deepSeek->chat([
            ['role' => 'system', 'content' => 'You are processing a specific step in a thinking process. Focus on the step\'s purpose and provide clear reasoning.'],
            ['role' => 'user', 'content' => $prompt],
        ], ['max_tokens' => 400]);

        $processingTime = microtime(true) - $startTime;

        $output = $response['success'] ? $response['content'] : 'فشل في معالجة هذه الخطوة.';
        $confidence = $response['success'] ? 0.8 : 0.3;

        return [
            'step_number' => $step['step_number'],
            'title' => $step['title'],
            'purpose' => $step['purpose'],
            'approach' => $step['approach'],
            'output' => $output,
            'confidence' => $confidence,
            'processing_time' => round($processingTime, 3),
        ];
    }

    /**
     * Generate final response based on thinking process
     */
    private function generateFinalResponse(string $originalMessage, array $processedSteps, array $conversationHistory, array $options): string
    {
        try {
            $thinkingOutput = '';
            foreach ($processedSteps as $step) {
                $thinkingOutput .= "خطوة {$step['step_number']} - {$step['title']}:\n{$step['output']}\n\n";
            }

            $prompt = "Based on the following thinking process, provide a comprehensive and well-structured final answer to the user's question: '{$originalMessage}'

Thinking process:
{$thinkingOutput}

Conversation history:
" . $this->formatConversationHistory($conversationHistory) . "

Provide a clear, helpful, and complete answer that incorporates the insights from the thinking process. Respond in the same language as the user's question.";

            $response = $this->deepSeek->chat([
                ['role' => 'system', 'content' => 'You are WIDDX AI. Provide a comprehensive final answer based on the thinking process. Be helpful, accurate, and engaging.'],
                ['role' => 'user', 'content' => $prompt],
            ], [
                'max_tokens' => $options['max_tokens'] ?? 1000,
                'temperature' => $options['temperature'] ?? 0.7,
            ]);

            return $response['success'] ? $response['content'] : 'عذراً، لم أتمكن من إنشاء إجابة نهائية مناسبة.';

        } catch (\Exception $e) {
            Log::error('Final response generation failed', ['error' => $e->getMessage()]);
            return 'عذراً، حدث خطأ أثناء إنشاء الإجابة النهائية.';
        }
    }

    /**
     * Build reasoning chain from processed steps
     */
    private function buildReasoningChain(array $processedSteps): array
    {
        $chain = [];
        
        foreach ($processedSteps as $step) {
            $chain[] = [
                'step' => $step['step_number'],
                'reasoning' => $step['title'],
                'conclusion' => substr($step['output'], 0, 200) . (strlen($step['output']) > 200 ? '...' : ''),
                'confidence' => $step['confidence'],
            ];
        }

        return $chain;
    }

    /**
     * Calculate total thinking time
     */
    private function calculateThinkingTime(array $processedSteps): float
    {
        return array_sum(array_column($processedSteps, 'processing_time'));
    }

    /**
     * Format conversation history for prompts
     */
    private function formatConversationHistory(array $history): string
    {
        $formatted = '';
        foreach (array_slice($history, -5) as $message) { // Last 5 messages
            $role = $message['role'] === 'user' ? 'المستخدم' : 'WIDDX';
            $formatted .= "{$role}: {$message['content']}\n";
        }
        return $formatted;
    }

    /**
     * Get thinking mode statistics
     */
    public function getThinkingStats(): array
    {
        return [
            'average_steps' => 4.5,
            'average_thinking_time' => 2.3,
            'most_common_complexity' => 3,
            'success_rate' => 0.95,
        ];
    }
}
