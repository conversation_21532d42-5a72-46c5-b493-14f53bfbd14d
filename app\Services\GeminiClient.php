<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Exceptions\ApiExceptions\{
    RateLimitException,
    AuthenticationException,
    ServerException,
    ClientException
};

class GeminiClient
{
    private string $apiKey;
    private string $baseUrl;
    private int $timeout;
    private int $maxRetries;
    private int $initialRetryDelay;
    private float $backoffFactor;

    public function __construct()
    {
        $this->validateConfig([
            'api_key' => 'required|string',
            'base_url' => 'required|url',
            'timeout' => 'required|integer|min:1',
            'max_retries' => 'required|integer|min:0',
            'initial_retry_delay' => 'required|integer|min:100',
            'backoff_factor' => 'required|numeric|min:1',
        ]);

        $this->apiKey = config('services.gemini.api_key');
        $this->baseUrl = config('services.gemini.base_url', 'https://generativelanguage.googleapis.com');
        $this->timeout = config('services.gemini.timeout', 30);
        $this->maxRetries = config('services.gemini.max_retries', 3);
        $this->initialRetryDelay = config('services.gemini.initial_retry_delay', 1000);
        $this->backoffFactor = config('services.gemini.backoff_factor', 2.0);
    }

    private function validateConfig(array $rules): void
    {
        foreach ($rules as $key => $rule) {
            $value = config("services.gemini.{$key}");

            if (str_contains($rule, 'required') && empty($value)) {
                throw new \InvalidArgumentException("Missing required configuration: services.gemini.{$key}");
            }

            if ($rule === 'url' && !filter_var($value, FILTER_VALIDATE_URL)) {
                throw new \InvalidArgumentException("Invalid URL in configuration: services.gemini.{$key}");
            }

            if ($rule === 'integer' && !is_int($value)) {
                throw new \InvalidArgumentException("Invalid integer in configuration: services.gemini.{$key}");
            }

            if ($rule === 'numeric' && !is_numeric($value)) {
                throw new \InvalidArgumentException("Invalid number in configuration: services.gemini.{$key}");
            }

            if (str_contains($rule, 'min:') && $value < (int)explode(':', $rule)[1]) {
                throw new \InvalidArgumentException(
                    "Value too small for configuration: services.gemini.{$key} (min: " . explode(':', $rule)[1] . ")"
                );
            }
        }
    }

    public function chat(array $messages, array $options = []): array
    {
        try {
            // Convert messages to Gemini format
            $contents = $this->convertMessagesToGeminiFormat($messages);

            $payload = [
                'contents' => $contents,
                'generationConfig' => [
                    'maxOutputTokens' => $options['max_tokens'] ?? 2000,
                    'temperature' => $options['temperature'] ?? 0.7,
                ],
            ];

            $model = $options['model'] ?? 'gemini-1.5-flash';
            $url = $this->baseUrl . '/v1beta/models/' . $model . ':generateContent?key=' . $this->apiKey;

            $response = $this->retryRequest(function() use ($url, $payload) {
                return Http::withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->timeout($this->timeout)
                ->post($url, $payload);
            });

            if (!$response->successful()) {
                $status = $response->status();
                $body = $response->body();

                Log::error('Gemini API Error', [
                    'status' => $status,
                    'body' => $body,
                ]);

                $retryAfter = $response->header('Retry-After');

                switch ($status) {
                    case 401:
                    case 403:
                        throw new AuthenticationException('Gemini API authentication failed', $body);
                    case 429:
                        throw new RateLimitException('Gemini API rate limit exceeded', $retryAfter);
                    case 400:
                    case 404:
                    case 422:
                        throw new ClientException("Gemini API client error (HTTP $status)", $body);
                    default:
                        if ($status >= 500) {
                            throw new ServerException("Gemini API server error (HTTP $status)", $body);
                        }
                        throw new \Exception("Gemini API request failed (HTTP $status): $body");
                }
            }

            $data = $response->json();

            return [
                'success' => true,
                'content' => $data['candidates'][0]['content']['parts'][0]['text'] ?? '',
                'model' => $model,
                'raw_response' => $data,
            ];

        } catch (\Exception $e) {
            Log::error('Gemini Client Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'content' => '',
            ];
        }
    }

    private function convertMessagesToGeminiFormat(array $messages): array
    {
        $contents = [];
        $systemPrompt = '';

        foreach ($messages as $message) {
            if ($message['role'] === 'system') {
                $systemPrompt = $message['content'];
                continue;
            }

            $role = $message['role'] === 'assistant' ? 'model' : 'user';
            $content = $message['content'];

            // If this is the first user message and we have a system prompt, prepend it
            if ($role === 'user' && !empty($systemPrompt) && empty($contents)) {
                $content = $systemPrompt . "\n\n" . $content;
                $systemPrompt = ''; // Clear it so we don't add it again
            }

            $contents[] = [
                'role' => $role,
                'parts' => [
                    ['text' => $content]
                ]
            ];
        }

        return $contents;
    }

    public function buildMessages(string $systemPrompt, string $userMessage, array $conversationHistory = []): array
    {
        $messages = [];

        // Add system message
        if (!empty($systemPrompt)) {
            $messages[] = [
                'role' => 'system',
                'content' => $systemPrompt,
            ];
        }

        // Add conversation history
        foreach ($conversationHistory as $message) {
            $messages[] = [
                'role' => $message['role'] === 'widdx' ? 'assistant' : $message['role'],
                'content' => $message['content'],
            ];
        }

        // Add current user message
        $messages[] = [
            'role' => 'user',
            'content' => $userMessage,
        ];

        return $messages;
    }

    public function isConfigured(): bool
    {
        return !empty($this->apiKey);
    }

    private function retryRequest(callable $request, int $attempt = 1)
    {
        $startTime = microtime(true);

        try {
            $response = $request();
            $durationMs = round((microtime(true) - $startTime) * 1000, 2);

            if ($response->successful()) {
                Log::info('Gemini API request succeeded', [
                    'attempt' => "$attempt/$this->maxRetries",
                    'duration_ms' => $durationMs,
                    'status' => $response->status(),
                ]);
                return $response;
            }

            if ($attempt >= $this->maxRetries) {
                Log::error('Gemini API request failed (final attempt)', [
                    'attempt' => "$attempt/$this->maxRetries",
                    'duration_ms' => $durationMs,
                    'status' => $response->status(),
                    'error' => $response->body(),
                ]);
                return $response;
            }

            $status = $response->status();
            $retryAfter = $response->header('Retry-After');
            $delay = $retryAfter
                ? (int)$retryAfter * 1000
                : $this->calculateRetryDelay($attempt);

            Log::warning('Gemini API request failed (retrying)', [
                'attempt' => "$attempt/$this->maxRetries",
                'duration_ms' => $durationMs,
                'status' => $status,
                'retry_in_ms' => $delay,
                'error' => $response->body(),
            ]);

            usleep($delay * 1000);
            return $this->retryRequest($request, $attempt + 1);

        } catch (\Exception $e) {
            $durationMs = round((microtime(true) - $startTime) * 1000, 2);

            if ($attempt >= $this->maxRetries) {
                Log::error('Gemini API request failed (final attempt)', [
                    'attempt' => "$attempt/$this->maxRetries",
                    'duration_ms' => $durationMs,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
                throw $e;
            }

            $delay = $this->calculateRetryDelay($attempt);
            Log::warning('Gemini API request failed (retrying)', [
                'attempt' => "$attempt/$this->maxRetries",
                'duration_ms' => $durationMs,
                'error' => $e->getMessage(),
                'retry_in_ms' => $delay,
            ]);

            usleep($delay * 1000);
            return $this->retryRequest($request, $attempt + 1);
        }
    }

    private function calculateRetryDelay(int $attempt): int
    {
        // Exponential backoff with jitter
        $exponentialDelay = $this->initialRetryDelay * pow($this->backoffFactor, $attempt - 1);
        $jitter = rand(0, $this->initialRetryDelay / 2);
        return (int)min($exponentialDelay + $jitter, 10000); // Max 10 seconds
    }
}
