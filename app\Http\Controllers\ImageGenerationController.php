<?php

namespace App\Http\Controllers;

use App\Services\ImageGenerationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ImageGenerationController extends Controller
{
    protected $imageService;

    public function __construct(ImageGenerationService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Generate an image from a text prompt
     */
    public function generate(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'prompt' => 'required|string|max:500',
            'provider' => 'sometimes|string|in:gemini,huggingface,freeDescription,freeSvg,freePlaceholder',
            'model' => 'sometimes|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()->first(),
            ], 422);
        }

        $prompt = $request->input('prompt');
        $options = [
            'provider' => $request->input('provider'),
            'model' => $request->input('model'),
        ];

        try {
            // Generate the image
            $result = $this->imageService->generateImage($prompt, $options);

            Log::info('Image generated successfully', [
                'prompt' => $prompt,
                'provider' => $options['provider'] ?? 'default',
                'model' => $options['model'] ?? 'default',
            ]);

            return response()->json([
                'success' => true,
                'images' => $result['images'] ?? [],
                'provider' => $result['provider'] ?? 'unknown',
                'model' => $result['model'] ?? 'unknown',
            ]);

        } catch (\Exception $e) {
            Log::error('Image generation failed', [
                'prompt' => $prompt,
                'options' => $options,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to generate image: ' . $e->getMessage(),
                'provider' => $options['provider'] ?? 'unknown',
            ], 500);
        }
    }
}
