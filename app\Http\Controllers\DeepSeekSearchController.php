<?php

namespace App\Http\Controllers;

use App\Services\DeepSeekSearchService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class DeepSeekSearchController extends Controller
{
    private DeepSeekSearchService $deepSeekSearch;

    public function __construct(DeepSeekSearchService $deepSeekSearch)
    {
        $this->deepSeekSearch = $deepSeekSearch;
    }

    /**
     * البحث الذكي باستخدام DeepSeek
     */
    public function intelligentSearch(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'query' => 'required|string|max:1000',
                'language' => 'sometimes|string|in:ar,en',
                'max_results' => 'sometimes|integer|min:5|max:50',
                'region' => 'sometimes|string|max:10',
                'providers' => 'sometimes|array',
                'providers.*' => 'string|in:duckduckgo,searx,startpage',
                'include_summary' => 'sometimes|boolean',
                'include_insights' => 'sometimes|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid request parameters',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $options = [
                'language' => $request->input('language', 'ar'),
                'max_results' => $request->input('max_results', 20),
                'region' => $request->input('region', 'SA'),
                'providers' => $request->input('providers', ['duckduckgo']),
                'include_summary' => $request->input('include_summary', true),
                'include_insights' => $request->input('include_insights', true),
            ];

            $startTime = microtime(true);
            $result = $this->deepSeekSearch->intelligentSearch($request->input('query'), $options);
            $endTime = microtime(true);

            $duration = round(($endTime - $startTime) * 1000, 2);

            // Add response metadata
            $result['request_id'] = uniqid('deepseek_search_', true);
            $result['processing_time_ms'] = $duration;
            $result['timestamp'] = now()->toISOString();

            return response()->json($result)
                ->header('X-Search-Mode', 'deepseek-intelligent')
                ->header('X-AI-Powered', 'true')
                ->header('X-Processing-Time', $duration . 'ms')
                ->header('X-Request-ID', $result['request_id']);

        } catch (\Exception $e) {
            Log::error('DeepSeek intelligent search API error', [
                'query' => $request->input('query'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'فشل في البحث الذكي',
                'message' => $e->getMessage(),
                'deepseek_powered' => true,
                'timestamp' => now()->toISOString(),
            ], 500);
        }
    }

    /**
     * البحث المقارن - مقارنة بين عدة مواضيع
     */
    public function comparativeSearch(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'topics' => 'required|array|min:2|max:5',
                'topics.*' => 'required|string|max:200',
                'language' => 'sometimes|string|in:ar,en',
                'comparison_aspects' => 'sometimes|array',
                'comparison_aspects.*' => 'string|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid comparative search parameters',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $topics = $request->input('topics');
            $language = $request->input('language', 'ar');
            $comparisonAspects = $request->input('comparison_aspects', []);

            $results = [];
            $totalProcessingTime = 0;

            // البحث عن كل موضوع
            foreach ($topics as $index => $topic) {
                $startTime = microtime(true);
                
                $searchResult = $this->deepSeekSearch->intelligentSearch($topic, [
                    'language' => $language,
                    'max_results' => 10,
                    'include_summary' => true,
                    'include_insights' => true,
                ]);

                $endTime = microtime(true);
                $duration = round(($endTime - $startTime) * 1000, 2);
                $totalProcessingTime += $duration;

                $results[] = [
                    'topic' => $topic,
                    'index' => $index,
                    'search_result' => $searchResult,
                    'processing_time_ms' => $duration,
                ];
            }

            // توليد مقارنة ذكية باستخدام DeepSeek
            $comparison = $this->generateComparison($topics, $results, $comparisonAspects, $language);

            return response()->json([
                'success' => true,
                'comparative_search' => true,
                'topics' => $topics,
                'comparison_aspects' => $comparisonAspects,
                'individual_results' => $results,
                'intelligent_comparison' => $comparison,
                'total_processing_time_ms' => $totalProcessingTime,
                'deepseek_powered' => true,
                'timestamp' => now()->toISOString(),
            ])->header('X-Search-Mode', 'deepseek-comparative');

        } catch (\Exception $e) {
            Log::error('DeepSeek comparative search error', [
                'topics' => $request->input('topics', []),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'فشل في البحث المقارن',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * البحث التفاعلي - سلسلة من الأسئلة والأجوبة
     */
    public function interactiveSearch(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'initial_query' => 'required|string|max:500',
                'follow_up_questions' => 'sometimes|array|max:10',
                'follow_up_questions.*' => 'string|max:300',
                'language' => 'sometimes|string|in:ar,en',
                'context' => 'sometimes|string|max:2000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid interactive search parameters',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $initialQuery = $request->input('initial_query');
            $followUpQuestions = $request->input('follow_up_questions', []);
            $language = $request->input('language', 'ar');
            $context = $request->input('context', '');

            $searchSession = [];
            $totalProcessingTime = 0;

            // البحث الأولي
            $startTime = microtime(true);
            $initialResult = $this->deepSeekSearch->intelligentSearch($initialQuery, [
                'language' => $language,
                'max_results' => 15,
                'include_summary' => true,
                'include_insights' => true,
            ]);
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            $totalProcessingTime += $duration;

            $searchSession[] = [
                'type' => 'initial_search',
                'query' => $initialQuery,
                'result' => $initialResult,
                'processing_time_ms' => $duration,
            ];

            $cumulativeContext = $context . "\n\nنتائج البحث الأولي:\n" . 
                                ($initialResult['intelligent_summary']['summary'] ?? '');

            // الأسئلة التفاعلية
            foreach ($followUpQuestions as $index => $question) {
                $startTime = microtime(true);
                
                $contextualQuery = $this->buildContextualQuery($question, $cumulativeContext, $language);
                
                $followUpResult = $this->deepSeekSearch->intelligentSearch($contextualQuery, [
                    'language' => $language,
                    'max_results' => 10,
                    'include_summary' => true,
                    'include_insights' => true,
                ]);

                $endTime = microtime(true);
                $duration = round(($endTime - $startTime) * 1000, 2);
                $totalProcessingTime += $duration;

                $searchSession[] = [
                    'type' => 'follow_up_search',
                    'original_question' => $question,
                    'contextual_query' => $contextualQuery,
                    'result' => $followUpResult,
                    'processing_time_ms' => $duration,
                ];

                // تحديث السياق
                $cumulativeContext .= "\n\nسؤال " . ($index + 1) . ": " . $question . "\n" .
                                     ($followUpResult['intelligent_summary']['summary'] ?? '');
            }

            // توليد ملخص شامل للجلسة
            $sessionSummary = $this->generateSessionSummary($initialQuery, $searchSession, $language);

            return response()->json([
                'success' => true,
                'interactive_search' => true,
                'initial_query' => $initialQuery,
                'follow_up_questions' => $followUpQuestions,
                'search_session' => $searchSession,
                'session_summary' => $sessionSummary,
                'total_processing_time_ms' => $totalProcessingTime,
                'deepseek_powered' => true,
                'timestamp' => now()->toISOString(),
            ])->header('X-Search-Mode', 'deepseek-interactive');

        } catch (\Exception $e) {
            Log::error('DeepSeek interactive search error', [
                'initial_query' => $request->input('initial_query'),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'فشل في البحث التفاعلي',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * الحصول على قدرات البحث الذكي
     */
    public function capabilities(): JsonResponse
    {
        try {
            $capabilities = $this->deepSeekSearch->getCapabilities();
            
            $capabilities['api_endpoints'] = [
                'intelligent_search' => '/api/deepseek-search/intelligent',
                'comparative_search' => '/api/deepseek-search/comparative',
                'interactive_search' => '/api/deepseek-search/interactive',
                'capabilities' => '/api/deepseek-search/capabilities',
            ];

            $capabilities['system_status'] = [
                'deepseek_api_available' => !empty(config('services.deepseek.api_key')),
                'unlimited_search_enabled' => config('widdx.features.unlimited_search.enabled', true),
                'ai_analysis_enabled' => true,
                'intelligent_summarization_enabled' => true,
            ];

            return response()->json($capabilities)
                ->header('X-Search-Mode', 'deepseek-capabilities');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'فشل في الحصول على معلومات النظام',
            ], 500);
        }
    }

    /**
     * توليد مقارنة ذكية بين المواضيع
     */
    private function generateComparison(array $topics, array $results, array $aspects, string $language): array
    {
        // This would use DeepSeek to generate intelligent comparison
        // Implementation would be similar to other DeepSeek integration methods
        return [
            'comparison_summary' => 'مقارنة ذكية بين ' . implode(' و ', $topics),
            'key_differences' => ['الاختلاف الأول', 'الاختلاف الثاني'],
            'similarities' => ['التشابه الأول', 'التشابه الثاني'],
            'recommendations' => ['التوصية الأولى', 'التوصية الثانية'],
        ];
    }

    /**
     * بناء استعلام سياقي للأسئلة التفاعلية
     */
    private function buildContextualQuery(string $question, string $context, string $language): string
    {
        // Build a contextual query that includes previous search context
        return $question . ' في سياق: ' . substr($context, -500);
    }

    /**
     * توليد ملخص شامل لجلسة البحث التفاعلي
     */
    private function generateSessionSummary(string $initialQuery, array $searchSession, string $language): array
    {
        return [
            'session_overview' => 'جلسة بحث تفاعلية شاملة حول ' . $initialQuery,
            'key_findings' => ['النتيجة الأولى', 'النتيجة الثانية'],
            'progression' => 'تطور البحث من الاستعلام الأولي إلى أسئلة أكثر تخصصاً',
            'final_insights' => ['الرؤية الأولى', 'الرؤية الثانية'],
        ];
    }
}
