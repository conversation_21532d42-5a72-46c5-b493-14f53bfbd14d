<?php

namespace Tests\Unit\Services\Search;

use App\Contracts\Search\SearchProviderInterface;
use App\Services\Search\SearchProviderManager;
use Illuminate\Support\Facades\Cache;
use Mockery;
use Tests\TestCase;

class SearchProviderManagerTest extends TestCase
{
    private SearchProviderManager $manager;
    private $mockProvider1;
    private $mockProvider2;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->manager = new SearchProviderManager();
        
        // Create mock providers
        $this->mockProvider1 = Mockery::mock(SearchProviderInterface::class);
        $this->mockProvider1->shouldReceive('getName')->andReturn('test1');
        $this->mockProvider1->shouldReceive('isConfigured')->andReturn(true);
        $this->mockProvider1->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider1->shouldReceive('getTimeout')->andReturn(5);
        
        $this->mockProvider2 = Mockery::mock(SearchProviderInterface::class);
        $this->mockProvider2->shouldReceive('getName')->andReturn('test2');
        $this->mockProvider2->shouldReceive('isConfigured')->andReturn(true);
        $this->mockProvider2->shouldReceive('isAvailable')->andReturn(true);
        $this->mockProvider2->shouldReceive('getTimeout')->andReturn(10);
        
        // Register providers
        $this->manager->registerProvider('test1', $this->mockProvider1, 3, ['api_key' => 'key1']);
        $this->manager->registerProvider('test2', $this->mockProvider2, 2, ['api_key' => 'key2']);
        
        // Clear cache before each test
        Cache::flush();
    }
    
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
    
    public function test_register_and_get_provider()
    {
        $provider = $this->manager->getProvider('test1');
        $this->assertSame($this->mockProvider1, $provider);
        
        $allProviders = $this->manager->getAllProviders();
        $this->assertCount(2, $allProviders);
        $this->assertArrayHasKey('test1', $allProviders);
        $this->assertArrayHasKey('test2', $allProviders);
    }
    
    public function test_get_available_providers()
    {
        // Both providers are available by default in setup
        $available = $this->manager->getAvailableProviders();
        $this->assertCount(2, $available);
        
        // Make one provider unavailable
        $unavailableProvider = Mockery::mock(SearchProviderInterface::class);
        $unavailableProvider->shouldReceive('getName')->andReturn('unavailable');
        $unavailableProvider->shouldReceive('isConfigured')->andReturn(false);
        $unavailableProvider->shouldReceive('isAvailable')->andReturn(false);
        $unavailableProvider->shouldReceive('getTimeout')->andReturn(5);
        
        $this->manager->registerProvider('unavailable', $unavailableProvider);
        
        $available = $this->manager->getAvailableProviders();
        $this->assertCount(2, $available);
        $this->assertArrayNotHasKey('unavailable', $available);
    }
    
    public function test_is_provider_available()
    {
        $this->assertTrue($this->manager->isProviderAvailable('test1'));
        $this->assertTrue($this->manager->isProviderAvailable($this->mockProvider1));
        
        // Test with non-existent provider
        $this->assertFalse($this->manager->isProviderAvailable('nonexistent'));
    }
    
    public function test_get_next_provider_with_weighted_distribution()
    {
        // Test multiple times to ensure distribution works
        $selections = [];
        $iterations = 1000;
        
        for ($i = 0; $i < $iterations; $i++) {
            $provider = $this->manager->getNextProvider();
            $name = $provider->getName();
            $selections[$name] = ($selections[$name] ?? 0) + 1;
        }
        
        // Check that both providers were selected
        $this->assertArrayHasKey('test1', $selections);
        $this->assertArrayHasKey('test2', $selections);
        
        // Check that distribution is roughly according to weights (3:2 ratio)
        $ratio = $selections['test1'] / $selections['test2'];
        $this->assertGreaterThan(1.0, $ratio); // test1 should be selected more often
        $this->assertLessThan(2.0, $ratio); // but not more than 2x as often (3:2 ratio)
    }
    
    public function test_get_next_provider_with_no_available_providers()
    {
        // Create a new manager with no providers
        $emptyManager = new SearchProviderManager();
        $this->assertNull($emptyManager->getNextProvider());
    }
    
    public function test_set_provider_weight()
    {
        $this->manager->setProviderWeight('test1', 5);
        
        // Test that the weight was updated by checking the distribution
        $selections = [];
        $iterations = 1000;
        
        for ($i = 0; $i < $iterations; $i++) {
            $provider = $this->manager->getNextProvider();
            $name = $provider->getName();
            $selections[$name] = ($selections[$name] ?? 0) + 1;
        }
        
        // With weights 5:2, test1 should be selected more often
        $ratio = $selections['test1'] / $selections['test2'];
        $this->assertGreaterThan(2.0, $ratio);
    }
    
    public function test_provider_configuration()
    {
        $config = $this->manager->getProviderConfig('test1');
        $this->assertEquals(['api_key' => 'key1'], $config);
        
        // Update config
        $this->manager->updateProviderConfig('test1', ['api_key' => 'new_key', 'timeout' => 10]);
        $updatedConfig = $this->manager->getProviderConfig('test1');
        $this->assertEquals(['api_key' => 'new_key', 'timeout' => 10], $updatedConfig);
    }
    
    public function test_check_all_providers_health()
    {
        $health = $this->manager->checkAllProvidersHealth();
        
        $this->assertCount(2, $health);
        $this->assertArrayHasKey('test1', $health);
        $this->assertArrayHasKey('test2', $health);
        $this->assertTrue($health['test1']);
        $this->assertTrue($health['test2']);
    }
    
    public function test_get_statistics()
    {
        $stats = $this->manager->getStatistics();
        
        $this->assertEquals(2, $stats['total_providers']);
        $this->assertEquals(2, $stats['available_providers']);
        $this->assertCount(2, $stats['providers']);
        
        $this->assertArrayHasKey('test1', $stats['providers']);
        $this->assertArrayHasKey('test2', $stats['providers']);
        
        $provider1Stats = $stats['providers']['test1'];
        $this->assertTrue($provider1Stats['available']);
        $this->assertEquals(3, $provider1Stats['weight']);
        $this->assertTrue($provider1Stats['configured']);
        $this->assertEquals(5, $provider1Stats['timeout']);
        $this->assertEquals(['api_key' => 'key1'], $provider1Stats['config']);
    }
    
    public function test_health_check_caching()
    {
        // First call should check the provider
        $this->mockProvider1->shouldReceive('isAvailable')->once()->andReturn(true);
        $this->assertTrue($this->manager->isProviderAvailable('test1'));
        
        // Second call should use cache
        $this->assertTrue($this->manager->isProviderAvailable('test1'));
        
        // Update config should invalidate cache
        $this->manager->updateProviderConfig('test1', ['new' => 'config']);
        
        // Should check availability again after cache invalidation
        $this->mockProvider1->shouldReceive('isAvailable')->once()->andReturn(true);
        $this->assertTrue($this->manager->isProviderAvailable('test1'));
    }
}
