<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add language support to chat_sessions table
        Schema::table('chat_sessions', function (Blueprint $table) {
            $table->string('preferred_language')->default('english')->after('personality_type');
            $table->string('preferred_language_code')->default('en')->after('preferred_language');
            $table->json('language_history')->nullable()->after('preferred_language_code'); // Track language usage patterns
        });

        // Add language support to messages table
        Schema::table('messages', function (Blueprint $table) {
            $table->string('detected_language')->nullable()->after('content');
            $table->string('detected_language_code')->nullable()->after('detected_language');
            $table->float('language_confidence')->nullable()->after('detected_language_code');
            $table->string('language_detection_method')->nullable()->after('language_confidence'); // 'automatic', 'override', 'fallback'
            $table->boolean('language_override_used')->default(false)->after('language_detection_method');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('chat_sessions', function (Blueprint $table) {
            $table->dropColumn(['preferred_language', 'preferred_language_code', 'language_history']);
        });

        Schema::table('messages', function (Blueprint $table) {
            $table->dropColumn([
                'detected_language',
                'detected_language_code',
                'language_confidence',
                'language_detection_method',
                'language_override_used'
            ]);
        });
    }
};
