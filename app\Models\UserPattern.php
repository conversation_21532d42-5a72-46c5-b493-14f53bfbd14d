<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserPattern extends Model
{
    protected $fillable = [
        'user_identifier',
        'pattern_type',
        'pattern_data',
        'confidence',
        'occurrence_count',
        'last_observed_at',
    ];

    protected $casts = [
        'pattern_data' => 'array',
        'confidence' => 'float',
        'occurrence_count' => 'integer',
        'last_observed_at' => 'datetime',
    ];

    /**
     * Record a new observation of this pattern
     */
    public function recordObservation(): void
    {
        $this->increment('occurrence_count');
        $this->update(['last_observed_at' => now()]);
        
        // Increase confidence with more observations
        $newConfidence = min(1.0, $this->confidence + 0.05);
        $this->update(['confidence' => $newConfidence]);
    }

    /**
     * Scope for specific user
     */
    public function scopeForUser($query, string $userIdentifier)
    {
        return $query->where('user_identifier', $userIdentifier);
    }

    /**
     * Scope for pattern type
     */
    public function scopeType($query, string $type)
    {
        return $query->where('pattern_type', $type);
    }

    /**
     * Scope for high confidence patterns
     */
    public function scopeHighConfidence($query, float $threshold = 0.7)
    {
        return $query->where('confidence', '>=', $threshold);
    }
}
