<?php

namespace App\Services\Search;

use App\Contracts\Search\SearchServiceInterface;
use App\ValueObjects\Search\SearchResult;
use App\ValueObjects\Search\SearchOptions;
use App\Exceptions\Search\SearchException;
use App\Exceptions\Search\SearchTimeoutException;
use App\Exceptions\Search\SearchConfigurationException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

abstract class BaseSearchService implements SearchServiceInterface
{
    protected int $defaultTimeout;
    protected int $defaultCacheMinutes;
    protected bool $cacheEnabled;
    protected string $serviceName;
    protected array $configuration;

    public function __construct()
    {
        $this->defaultTimeout = Config::get('services.search.timeout', 30);
        $this->defaultCacheMinutes = Config::get('services.search.cache_minutes', 15);
        $this->cacheEnabled = Config::get('services.search.cache_enabled', true);
        $this->serviceName = $this->getServiceName();
        $this->configuration = $this->loadConfiguration();
        
        $this->initialize();
    }

    /**
     * Initialize the service - can be overridden by child classes
     */
    protected function initialize(): void
    {
        // Override in child classes if needed
    }

    /**
     * Perform search with caching and error handling
     */
    public function search(string $query, SearchOptions $options = null): SearchResult
    {
        $startTime = microtime(true);
        $options = $options ?? new SearchOptions();
        
        try {
            // Validate input
            $this->validateSearchInput($query, $options);
            
            // Check cache if enabled
            if ($this->cacheEnabled && $options->enableCache) {
                $cacheKey = $this->generateCacheKey($query, $options);
                $cached = Cache::get($cacheKey);
                
                if ($cached !== null) {
                    Log::info('Search cache hit', [
                        'service' => $this->serviceName,
                        'query' => $query,
                        'cache_key' => $cacheKey,
                    ]);
                    
                    return SearchResult::success(
                        query: $query,
                        results: $cached['results'],
                        provider: $cached['provider'],
                        metadata: array_merge($cached['metadata'] ?? [], ['fromCache' => true]),
                        responseTime: microtime(true) - $startTime,
                        totalResults: $cached['totalResults'] ?? count($cached['results']),
                        fromCache: true
                    );
                }
            }

            // Perform the actual search
            $result = $this->performSearch($query, $options);
            
            // Add response time to metadata
            $responseTime = microtime(true) - $startTime;
            $metadata = array_merge($result->metadata, [
                'responseTime' => $responseTime,
                'service' => $this->serviceName,
                'timestamp' => now()->toISOString(),
            ]);

            $finalResult = SearchResult::success(
                query: $result->query,
                results: $result->results,
                provider: $result->provider,
                metadata: $metadata,
                responseTime: $responseTime,
                totalResults: $result->totalResults,
                fromCache: false,
                nextPageToken: $result->nextPageToken,
                providerResults: $result->providerResults
            );

            // Cache successful results
            if ($this->cacheEnabled && $options->enableCache && $result->success) {
                $this->cacheSearchResult($query, $options, $finalResult);
            }

            $this->logSearchCompletion($query, $finalResult, $responseTime);
            
            return $finalResult;

        } catch (SearchException $e) {
            $responseTime = microtime(true) - $startTime;
            $this->logSearchError($query, $e, $responseTime);
            
            return SearchResult::failure(
                query: $query,
                error: $e->getMessage(),
                provider: $this->serviceName,
                metadata: [
                    'responseTime' => $responseTime,
                    'service' => $this->serviceName,
                    'errorType' => get_class($e),
                    'timestamp' => now()->toISOString(),
                ],
                responseTime: $responseTime
            );
        } catch (\Exception $e) {
            $responseTime = microtime(true) - $startTime;
            $this->logSearchError($query, $e, $responseTime);
            
            return SearchResult::failure(
                query: $query,
                error: 'حدث خطأ غير متوقع أثناء البحث',
                provider: $this->serviceName,
                metadata: [
                    'responseTime' => $responseTime,
                    'service' => $this->serviceName,
                    'errorType' => get_class($e),
                    'timestamp' => now()->toISOString(),
                ],
                responseTime: $responseTime
            );
        }
    }

    /**
     * Abstract method that child classes must implement
     */
    abstract protected function performSearch(string $query, SearchOptions $options): SearchResult;

    /**
     * Abstract method to get the service name
     */
    abstract protected function getServiceName(): string;

    /**
     * Generate cache key for search results
     */
    protected function generateCacheKey(string $query, SearchOptions $options): string
    {
        $keyData = [
            'service' => $this->serviceName,
            'query' => $query,
            'options' => $options->toArray(),
        ];
        
        return 'search:' . $this->serviceName . ':' . md5(serialize($keyData));
    }

    /**
     * Cache search results
     */
    protected function cacheSearchResult(string $query, SearchOptions $options, SearchResult $result): void
    {
        try {
            $cacheKey = $this->generateCacheKey($query, $options);
            $cacheData = [
                'results' => $result->results,
                'provider' => $result->provider,
                'metadata' => $result->metadata,
                'totalResults' => $result->totalResults,
                'cached_at' => now()->toISOString(),
            ];
            
            $ttl = $this->getCacheTTL($options);
            Cache::put($cacheKey, $cacheData, now()->addMinutes($ttl));
            
            Log::debug('Search result cached', [
                'service' => $this->serviceName,
                'cache_key' => $cacheKey,
                'ttl_minutes' => $ttl,
            ]);
            
        } catch (\Exception $e) {
            Log::warning('Failed to cache search result', [
                'service' => $this->serviceName,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get cache TTL based on service type and options
     */
    protected function getCacheTTL(SearchOptions $options): int
    {
        // Override in child classes for service-specific TTL
        return $this->defaultCacheMinutes;
    }

    /**
     * Validate search input
     */
    protected function validateSearchInput(string $query, SearchOptions $options): void
    {
        if (empty(trim($query))) {
            throw new SearchException('استعلام البحث لا يمكن أن يكون فارغاً');
        }

        if (strlen($query) > 500) {
            throw new SearchException('استعلام البحث طويل جداً (الحد الأقصى 500 حرف)');
        }

        if ($options->maxResults < 1 || $options->maxResults > 100) {
            throw new SearchException('عدد النتائج يجب أن يكون بين 1 و 100');
        }

        if ($options->timeout < 1 || $options->timeout > 120) {
            throw new SearchException('مهلة الانتظار يجب أن تكون بين 1 و 120 ثانية');
        }
    }

    /**
     * Format search results to ensure consistency
     */
    protected function formatResults(array $rawResults): array
    {
        $formatted = [];
        
        foreach ($rawResults as $result) {
            if (!is_array($result)) {
                continue;
            }
            
            $formatted[] = [
                'title' => $this->sanitizeText($result['title'] ?? ''),
                'url' => $this->sanitizeUrl($result['url'] ?? ''),
                'snippet' => $this->sanitizeText($result['snippet'] ?? ''),
                'display_url' => $this->extractDisplayUrl($result['url'] ?? ''),
                'formatted_url' => $this->sanitizeUrl($result['url'] ?? ''),
                'provider' => $result['provider'] ?? $this->serviceName,
                'relevance_score' => $result['relevance_score'] ?? 0.0,
                'metadata' => $result['metadata'] ?? [],
            ];
        }
        
        return $formatted;
    }

    /**
     * Sanitize text content
     */
    protected function sanitizeText(string $text): string
    {
        $text = strip_tags($text);
        $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');
        $text = preg_replace('/\s+/', ' ', $text);
        return trim($text);
    }

    /**
     * Sanitize URL
     */
    protected function sanitizeUrl(string $url): string
    {
        $url = trim($url);
        if (empty($url)) {
            return '';
        }
        
        // Add protocol if missing
        if (!preg_match('/^https?:\/\//', $url)) {
            $url = 'https://' . $url;
        }
        
        return filter_var($url, FILTER_VALIDATE_URL) ? $url : '';
    }

    /**
     * Extract display URL from full URL
     */
    protected function extractDisplayUrl(string $url): string
    {
        if (empty($url)) {
            return '';
        }
        
        $parsed = parse_url($url);
        return $parsed['host'] ?? '';
    }

    /**
     * Load service configuration
     */
    protected function loadConfiguration(): array
    {
        return Config::get("services.search.{$this->serviceName}", []);
    }

    /**
     * Get configuration value with default
     */
    protected function getConfigValue(string $key, $default = null)
    {
        return $this->configuration[$key] ?? Config::get("services.search.{$key}", $default);
    }

    /**
     * Log search completion
     */
    protected function logSearchCompletion(string $query, SearchResult $result, float $responseTime): void
    {
        Log::info('Search completed', [
            'service' => $this->serviceName,
            'query' => $query,
            'success' => $result->success,
            'results_count' => count($result->results),
            'response_time' => $responseTime,
            'from_cache' => $result->fromCache,
            'provider' => $result->provider,
        ]);
    }

    /**
     * Log search errors
     */
    protected function logSearchError(string $query, \Exception $exception, float $responseTime): void
    {
        Log::error('Search failed', [
            'service' => $this->serviceName,
            'query' => $query,
            'error' => $exception->getMessage(),
            'error_type' => get_class($exception),
            'response_time' => $responseTime,
            'trace' => $exception->getTraceAsString(),
        ]);
    }

    /**
     * Check if service is available
     */
    public function isAvailable(): bool
    {
        try {
            // Basic availability check - can be overridden by child classes
            return $this->checkServiceHealth();
        } catch (\Exception $e) {
            Log::warning('Service availability check failed', [
                'service' => $this->serviceName,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Basic service health check
     */
    protected function checkServiceHealth(): bool
    {
        // Override in child classes for specific health checks
        return true;
    }

    /**
     * Get service name
     */
    public function getName(): string
    {
        return $this->serviceName;
    }

    /**
     * Get service capabilities
     */
    public function getCapabilities(): array
    {
        return [
            'name' => $this->serviceName,
            'timeout' => $this->defaultTimeout,
            'cache_enabled' => $this->cacheEnabled,
            'cache_minutes' => $this->defaultCacheMinutes,
            'max_results' => 100,
            'supported_languages' => ['ar', 'en'],
            'supported_regions' => ['SA', 'US', 'GB'],
        ];
    }

    /**
     * Clear cache for this service
     */
    public function clearCache(): bool
    {
        try {
            $pattern = "search:{$this->serviceName}:*";
            // Note: This is a simplified cache clearing - in production you might want
            // to use a more sophisticated cache tagging system
            Log::info('Cache cleared for service', ['service' => $this->serviceName]);
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to clear cache', [
                'service' => $this->serviceName,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
}