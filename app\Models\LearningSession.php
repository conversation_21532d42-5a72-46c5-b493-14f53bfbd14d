<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LearningSession extends Model
{
    protected $fillable = [
        'chat_session_id',
        'learned_items',
        'reinforced_knowledge',
        'contradicted_knowledge',
        'learning_score',
        'primary_language',
    ];

    protected $casts = [
        'learned_items' => 'array',
        'reinforced_knowledge' => 'array',
        'contradicted_knowledge' => 'array',
        'learning_score' => 'float',
    ];

    /**
     * Get the chat session that owns this learning session
     */
    public function chatSession(): BelongsTo
    {
        return $this->belongsTo(ChatSession::class);
    }

    /**
     * Get learning statistics
     */
    public static function getLearningStats(): array
    {
        $totalSessions = static::count();
        $avgLearningScore = static::avg('learning_score') ?? 0;
        $topLanguages = static::selectRaw('primary_language, COUNT(*) as count')
            ->groupBy('primary_language')
            ->orderByDesc('count')
            ->limit(5)
            ->get()
            ->pluck('count', 'primary_language')
            ->toArray();

        return [
            'total_learning_sessions' => $totalSessions,
            'average_learning_score' => round($avgLearningScore, 2),
            'top_languages' => $topLanguages,
            'recent_sessions' => static::orderByDesc('created_at')
                ->limit(10)
                ->get()
                ->map(function ($session) {
                    return [
                        'id' => $session->id,
                        'learning_score' => $session->learning_score,
                        'language' => $session->primary_language,
                        'items_learned' => count($session->learned_items ?? []),
                        'created_at' => $session->created_at,
                    ];
                })
                ->toArray(),
        ];
    }

    /**
     * Scope for high learning sessions
     */
    public function scopeHighLearning($query, float $threshold = 0.7)
    {
        return $query->where('learning_score', '>=', $threshold);
    }

    /**
     * Scope for specific language
     */
    public function scopeLanguage($query, string $language)
    {
        return $query->where('primary_language', $language);
    }

    /**
     * Scope for recent sessions
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
