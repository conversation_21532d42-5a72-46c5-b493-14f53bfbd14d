<?php

namespace App\Exceptions\Search;

class SearchTimeoutException extends SearchException
{
    private int $timeoutSeconds;

    public function __construct(
        string $message = '',
        int $code = 0,
        ?\Exception $previous = null,
        string $searchQuery = '',
        string $provider = '',
        array $context = [],
        int $timeoutSeconds = 0
    ) {
        parent::__construct($message, $code, $previous, $searchQuery, $provider, $context);
        $this->timeoutSeconds = $timeoutSeconds;
    }

    /**
     * Get the timeout duration in seconds
     */
    public function getTimeoutSeconds(): int
    {
        return $this->timeoutSeconds;
    }

    /**
     * Create timeout exception for search operations
     */
    public static function searchTimeout(
        string $query,
        int $timeoutSeconds,
        string $provider = '',
        array $context = []
    ): self {
        return new self(
            message: "Search operation timed out after {$timeoutSeconds} seconds",
            code: 2001,
            searchQuery: $query,
            provider: $provider,
            context: $context,
            timeoutSeconds: $timeoutSeconds
        );
    }

    /**
     * Create timeout exception for provider operations
     */
    public static function providerTimeout(
        string $provider,
        string $query,
        int $timeoutSeconds,
        array $context = []
    ): self {
        return new self(
            message: "Provider '{$provider}' timed out after {$timeoutSeconds} seconds",
            code: 2002,
            searchQuery: $query,
            provider: $provider,
            context: $context,
            timeoutSeconds: $timeoutSeconds
        );
    }

    /**
     * Convert exception to array with timeout information
     */
    public function toArray(): array
    {
        $array = parent::toArray();
        $array['timeoutSeconds'] = $this->timeoutSeconds;
        return $array;
    }
}