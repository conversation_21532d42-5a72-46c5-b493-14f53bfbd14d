<?php

namespace App\Exceptions\ApiExceptions;

use Exception;

class ClientException extends Exception
{
    protected $code = 400;
    protected $message = 'API client error';

    public function __construct(?string $message = null, ?string $details = null)
    {
        if ($message !== null) {
            $this->message = $message;
        }

        if ($details !== null) {
            $this->message .= " - Details: {$details}";
        }

        parent::__construct($this->message, $this->code);
    }
}
