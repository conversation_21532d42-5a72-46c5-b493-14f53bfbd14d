<?php

require __DIR__.'/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$apiKey = $_ENV['GEMINI_API_KEY'];
$baseUrl = $_ENV['GEMINI_BASE_URL'] ?? 'https://generativelanguage.googleapis.com';
$model = 'gemini-2.0-flash';

if (empty($apiKey)) {
    die("Error: GEMINI_API_KEY is not set in .env file\n");
}

$url = "{$baseUrl}/v1beta/models/{$model}:generateContent?key={$apiKey}";
$prompt = "A beautiful sunset over mountains";

$payload = [
    'contents' => [
        'parts' => [
            ['text' => $prompt]
        ]
    ],
    'generationConfig' => [
        'temperature' => 0.7,
        'topK' => 40,
        'topP' => 0.95,
        'maxOutputTokens' => 2048,
    ]
];

$ch = curl_init($url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    die("cURL Error: " . $error . "\n");
}

echo "HTTP Status: " . $httpCode . "\n";
echo "Response: " . $response . "\n";

// Try to decode and pretty print JSON
$decoded = json_decode($response, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "\nDecoded JSON:\n";
    echo json_encode($decoded, JSON_PRETTY_PRINT) . "\n";
} else {
    echo "\nResponse is not valid JSON\n";
}
