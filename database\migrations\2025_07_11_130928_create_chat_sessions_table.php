<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('session_id')->unique();
            $table->string('user_identifier')->nullable(); // IP or user ID
            $table->string('personality_type')->default('neutral');
            $table->string('preferred_language')->default('english');
            $table->string('preferred_language_code')->default('en');
            $table->json('language_history')->nullable(); // Track language usage patterns
            $table->json('context_data')->nullable(); // Store user preferences, learned patterns
            $table->timestamp('last_activity');
            $table->timestamps();

            $table->index(['user_identifier', 'last_activity']);
            $table->index('session_id');
            $table->index(['preferred_language_code', 'last_activity']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_sessions');
    }
};
