<?php

namespace App\Contracts\Search;

use App\ValueObjects\Search\SearchOptions;
use App\ValueObjects\Search\ProviderResult;

interface SearchProviderInterface
{
    /**
     * Perform a search using this provider
     */
    public function search(string $query, SearchOptions $options): ProviderResult;

    /**
     * Get the provider name
     */
    public function getName(): string;

    /**
     * Check if the provider is properly configured
     */
    public function isConfigured(): bool;

    /**
     * Get the timeout for this provider in seconds
     */
    public function getTimeout(): int;

    /**
     * Check if the provider is currently available
     */
    public function isAvailable(): bool;

    /**
     * Get provider-specific configuration
     */
    public function getConfiguration(): array;
}