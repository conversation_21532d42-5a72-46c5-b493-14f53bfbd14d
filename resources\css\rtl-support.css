/* RTL Support for WIDDX AI - Forceful Implementation */

/* Base RTL styles for the entire message */
.rtl-message,
[dir="rtl"],
.widdx-message[dir="rtl"],
.widdx-message.rtl-message {
  direction: rtl !important;
  text-align: right !important;
  unicode-bidi: embed !important;
}

/* Force RTL on the message text specifically */
.widdx-message-text,
.rtl-text {
  unicode-bidi: plaintext !important;
  text-align: right !important;
  text-align-last: right !important;
  direction: rtl !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* Force RTL for the rtl-text class specifically */
.rtl-text {
  direction: rtl !important;
  text-align: right !important;
  unicode-bidi: plaintext !important;
  text-align-last: right !important;
}

/* Force RTL on all text elements within RTL messages */
.rtl-message *,
[dir="rtl"] * {
  direction: inherit !important;
  text-align: inherit !important;
  unicode-bidi: inherit !important;
}

/* Force RTL on all text elements */
.rtl-message p,
.rtl-message div,
.rtl-message span,
.rtl-message li,
.rtl-message td,
.rtl-message th,
[dir="rtl"] p,
[dir="rtl"] div,
[dir="rtl"] span,
[dir="rtl"] li,
[dir="rtl"] td,
[dir="rtl"] th {
  direction: rtl !important;
  text-align: right !important;
  unicode-bidi: plaintext !important;
}

/* Force RTL on message content */
.widdx-message-container.rtl-message .widdx-message-content {
  direction: rtl !important;
  text-align: right !important;
}

/* Force RTL on message text */
.widdx-message-container.rtl-message .widdx-message-text,
.widdx-message[dir="rtl"] .widdx-message-text,
.widdx-message-text[dir="rtl"],
.rtl-message .widdx-message-text,
[dir="rtl"] .widdx-message-text {
  direction: rtl !important;
  text-align: right !important;
  unicode-bidi: plaintext !important;
  display: block !important;
  width: 100% !important;
  text-align: inherit !important;
  font-family: inherit !important;
  text-align-last: right !important;
}

/* Force LTR for message actions */
.widdx-message-container.rtl-message .widdx-message-actions {
  direction: ltr !important;
  text-align: left !important;
  justify-content: flex-start !important;
}

/* Force RTL for input fields */
.widdx-input[dir="rtl"],
.widdx-textarea[dir="rtl"] {
  text-align: right !important;
  direction: rtl !important;
  unicode-bidi: plaintext !important;
}

/* Force RTL for message header */
.widdx-message-container.rtl-message .widdx-message-header,
.widdx-message[dir="rtl"] .widdx-message-header {
  flex-direction: row-reverse !important;
  justify-content: flex-end !important;
  text-align: right !important;
}

/* Force RTL for timestamps */
.widdx-message-container.rtl-message .widdx-message-time {
  margin-left: 0 !important;
  margin-right: 0.5rem !important;
}

/* Force LTR for code blocks */
.widdx-message-container.rtl-message pre,
.widdx-message-container.rtl-message code {
  direction: ltr !important;
  text-align: left !important;
  unicode-bidi: embed !important;
}

/* Force RTL on the message body */
.widdx-message-container.rtl-message .widdx-message-body,
.widdx-message[dir="rtl"] .widdx-message-body,
.widdx-message-body[dir="rtl"] {
  direction: rtl !important;
  text-align: right !important;
  unicode-bidi: plaintext !important;
  width: 100% !important;
  display: block !important;
}

/* Force RTL on the message container */
.widdx-message-container.rtl-message {
  direction: rtl !important;
  text-align: right !important;
  unicode-bidi: plaintext !important;
}
