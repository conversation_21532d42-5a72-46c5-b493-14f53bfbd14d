<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ContextMemory extends Model
{
    protected $table = 'context_memories';

    protected $fillable = [
        'memory_type',
        'key',
        'value',
        'scope',
        'scope_identifier',
        'importance',
        'expires_at',
    ];

    protected $casts = [
        'value' => 'array',
        'importance' => 'float',
        'expires_at' => 'datetime',
    ];

    /**
     * Scope for non-expired memories
     */
    public function scopeActive($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * Scope for specific memory type
     */
    public function scopeType($query, string $type)
    {
        return $query->where('memory_type', $type);
    }

    /**
     * Scope for specific scope
     */
    public function scopeScope($query, string $scope, ?string $identifier = null)
    {
        $query = $query->where('scope', $scope);
        
        if ($identifier !== null) {
            $query = $query->where('scope_identifier', $identifier);
        }
        
        return $query;
    }

    /**
     * Scope for important memories
     */
    public function scopeImportant($query, float $threshold = 0.7)
    {
        return $query->where('importance', '>=', $threshold);
    }

    /**
     * Get memory by key and scope
     */
    public static function getMemory(string $key, string $scope = 'global', ?string $scopeIdentifier = null): ?array
    {
        $memory = static::where('key', $key)
            ->scope($scope, $scopeIdentifier)
            ->active()
            ->first();

        return $memory ? $memory->value : null;
    }

    /**
     * Set memory with key and scope
     */
    public static function setMemory(
        string $key,
        $value,
        string $scope = 'global',
        ?string $scopeIdentifier = null,
        float $importance = 0.5,
        ?\DateTime $expiresAt = null
    ): self {
        return static::updateOrCreate([
            'key' => $key,
            'scope' => $scope,
            'scope_identifier' => $scopeIdentifier,
        ], [
            'memory_type' => 'general',
            'value' => is_array($value) ? $value : ['content' => $value],
            'importance' => $importance,
            'expires_at' => $expiresAt,
        ]);
    }

    /**
     * Clean up expired memories
     */
    public static function cleanupExpired(): int
    {
        return static::where('expires_at', '<=', now())->delete();
    }

    /**
     * Get memory statistics
     */
    public static function getStats(): array
    {
        return [
            'total_memories' => static::count(),
            'active_memories' => static::active()->count(),
            'expired_memories' => static::where('expires_at', '<=', now())->count(),
            'by_type' => static::selectRaw('memory_type, COUNT(*) as count')
                ->groupBy('memory_type')
                ->pluck('count', 'memory_type')
                ->toArray(),
            'by_scope' => static::selectRaw('scope, COUNT(*) as count')
                ->groupBy('scope')
                ->pluck('count', 'scope')
                ->toArray(),
            'average_importance' => round(static::avg('importance') ?? 0, 2),
        ];
    }

    /**
     * Increase importance of a memory
     */
    public function increaseImportance(float $amount = 0.1): void
    {
        $newImportance = min(1.0, $this->importance + $amount);
        $this->update(['importance' => $newImportance]);
    }

    /**
     * Decrease importance of a memory
     */
    public function decreaseImportance(float $amount = 0.1): void
    {
        $newImportance = max(0.0, $this->importance - $amount);
        $this->update(['importance' => $newImportance]);
    }
}
