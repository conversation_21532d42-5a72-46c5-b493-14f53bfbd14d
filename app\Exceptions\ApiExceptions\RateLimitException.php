<?php

namespace App\Exceptions\ApiExceptions;

use Exception;

class RateLimitException extends Exception
{
    protected $code = 429;
    protected $message = 'API rate limit exceeded';

    public function __construct(?string $message = null, ?int $retryAfter = null)
    {
        if ($message !== null) {
            $this->message = $message;
        }

        if ($retryAfter !== null) {
            $this->message .= " - Retry after: {$retryAfter} seconds";
        }

        parent::__construct($this->message, $this->code);
    }
}
