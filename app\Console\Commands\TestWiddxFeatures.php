<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\LanguageDetectionService;
use App\Services\WiddxIdentityService;
use App\Services\WiddxKnowledgeService;
use App\Services\WiddxLearningService;
use App\Services\ModelMergerService;
use App\Services\GeminiClient;
use App\Services\DeepSeekClient;
use App\Services\ImageGenerationService;
use App\Services\DeepSeekSearchService;
use App\Models\ChatSession;
use App\Models\KnowledgeEntry;
use App\Models\PersonalityEvolution;

class TestWiddxFeatures extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'widdx:test-features {--feature= : Test specific feature}';

    /**
     * The console command description.
     */
    protected $description = 'Test all WIDDX AI features and capabilities';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🧪 Testing WIDDX AI Features...');
        $this->info('');

        $feature = $this->option('feature');

        if ($feature) {
            return $this->testSpecificFeature($feature);
        }

        $results = [];

        // Test all features
        $results['language_detection'] = $this->testLanguageDetection();
        $results['knowledge_system'] = $this->testKnowledgeSystem();
        $results['identity_system'] = $this->testIdentitySystem();
        $results['learning_system'] = $this->testLearningSystem();
        $results['api_integrations'] = $this->testApiIntegrations();
        $results['image_generation'] = $this->testImageGeneration();
        $results['search_capabilities'] = $this->testSearchCapabilities();
        $results['database_integration'] = $this->testDatabaseIntegration();

        // Display summary
        $this->displayTestSummary($results);

        return 0;
    }

    /**
     * Test specific feature
     */
    private function testSpecificFeature(string $feature): int
    {
        $method = 'test' . str_replace('_', '', ucwords($feature, '_'));

        if (!method_exists($this, $method)) {
            $this->error("❌ Unknown feature: {$feature}");
            return 1;
        }

        $result = $this->$method();
        $status = $result ? '✅ PASSED' : '❌ FAILED';
        $this->info("{$status} - {$feature}");

        return $result ? 0 : 1;
    }

    /**
     * Test language detection
     */
    private function testLanguageDetection(): bool
    {
        $this->info('🌐 Testing Language Detection...');

        try {
            $service = app(LanguageDetectionService::class);

            // Test English
            $result = $service->detectLanguage('Hello, how are you today?');
            $this->line("   English detection: {$result['language']} (confidence: {$result['confidence']})");

            // Test Arabic
            $result = $service->detectLanguage('مرحبا، كيف حالك اليوم؟');
            $this->line("   Arabic detection: {$result['language']} (confidence: {$result['confidence']})");

            // Test mixed content
            $result = $service->detectLanguage('Hello مرحبا');
            $this->line("   Mixed content: {$result['language']} (confidence: {$result['confidence']})");

            $this->info('   ✅ Language detection working');
            return true;

        } catch (\Exception $e) {
            $this->error("   ❌ Language detection failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Test knowledge system
     */
    private function testKnowledgeSystem(): bool
    {
        $this->info('🧠 Testing Knowledge System...');

        try {
            $service = app(WiddxKnowledgeService::class);

            // Test knowledge retrieval
            $knowledge = $service->getRelevantKnowledge('artificial intelligence', 'en', 5);
            $this->line("   Retrieved " . count($knowledge) . " knowledge entries");

            // Test knowledge storage (create a test entry)
            KnowledgeEntry::create([
                'topic' => 'test_topic',
                'category' => 'test',
                'content' => 'This is a test knowledge entry',
                'confidence_score' => 0.8,
                'language' => 'en',
            ]);

            $testKnowledge = $service->getRelevantKnowledge('test_topic', 'en', 1);
            $this->line("   Test knowledge entry: " . (count($testKnowledge) > 0 ? 'Found' : 'Not found'));

            $this->info('   ✅ Knowledge system working');
            return true;

        } catch (\Exception $e) {
            $this->error("   ❌ Knowledge system failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Test identity system
     */
    private function testIdentitySystem(): bool
    {
        $this->info('🤖 Testing Identity System...');

        try {
            $service = app(WiddxIdentityService::class);

            // Test autonomous response building
            $response = $service->buildAutonomousResponse(
                'Hello, tell me about yourself',
                [],
                ['target_language' => 'english', 'target_language_code' => 'en']
            );

            $this->line("   Autonomous prompt generated: " . (strlen($response['autonomous_prompt']) > 0 ? 'Yes' : 'No'));
            $this->line("   Identity profile: " . json_encode($response['widdx_identity']['name'] ?? 'Unknown'));

            $this->info('   ✅ Identity system working');
            return true;

        } catch (\Exception $e) {
            $this->error("   ❌ Identity system failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Test learning system
     */
    private function testLearningSystem(): bool
    {
        $this->info('📚 Testing Learning System...');

        try {
            $service = app(WiddxLearningService::class);

            // Test conversation analysis
            $analysis = $service->analyzeConversation(
                new ChatSession(['session_id' => 'test-session']),
                'I love programming and artificial intelligence',
                'That\'s great! Programming and AI are fascinating fields.',
                ['language' => 'english']
            );

            $this->line("   Conversation analysis: " . (is_array($analysis) ? 'Generated' : 'Failed'));

            // Test preference extraction
            $preferences = $service->extractUserPreferences('I prefer dark themes and minimal interfaces');
            $this->line("   Preferences extracted: " . count($preferences));

            $this->info('   ✅ Learning system working');
            return true;

        } catch (\Exception $e) {
            $this->error("   ❌ Learning system failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Test API integrations
     */
    private function testApiIntegrations(): bool
    {
        $this->info('🔌 Testing API Integrations...');

        $results = [];

        // Test Gemini API
        try {
            $gemini = app(GeminiClient::class);
            $response = $gemini->chat([
                ['role' => 'user', 'content' => 'Say "Gemini API test successful"']
            ]);
            $results['gemini'] = $response['success'] ?? false;
            $this->line("   Gemini API: " . ($results['gemini'] ? '✅ Connected' : '❌ Failed'));
        } catch (\Exception $e) {
            $results['gemini'] = false;
            $this->line("   Gemini API: ❌ Error - {$e->getMessage()}");
        }

        // Test DeepSeek API
        try {
            $deepseek = app(DeepSeekClient::class);
            $response = $deepseek->chat([
                ['role' => 'user', 'content' => 'Say "DeepSeek API test successful"']
            ]);
            $results['deepseek'] = $response['success'] ?? false;
            $this->line("   DeepSeek API: " . ($results['deepseek'] ? '✅ Connected' : '❌ Failed'));
        } catch (\Exception $e) {
            $results['deepseek'] = false;
            $this->line("   DeepSeek API: ❌ Error - {$e->getMessage()}");
        }

        $success = $results['gemini'] || $results['deepseek'];
        $this->info($success ? '   ✅ At least one API working' : '   ❌ No APIs working');
        return $success;
    }

    /**
     * Test image generation
     */
    private function testImageGeneration(): bool
    {
        $this->info('🎨 Testing Image Generation...');

        try {
            $service = app(ImageGenerationService::class);

            // Test capability detection
            $this->line("   Image generation service: Available");

            // Note: We don't actually generate an image in tests to avoid API costs
            $this->line("   Image generation capability: Ready (not tested to avoid API costs)");

            $this->info('   ✅ Image generation system ready');
            return true;

        } catch (\Exception $e) {
            $this->error("   ❌ Image generation failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Test search capabilities
     */
    private function testSearchCapabilities(): bool
    {
        $this->info('🔍 Testing Search Capabilities...');

        try {
            $service = app(DeepSeekSearchService::class);

            $this->line("   DeepSeek search service: Available");

            // Note: We don't actually perform a search in tests to avoid API costs
            $this->line("   Search capability: Ready (not tested to avoid API costs)");

            $this->info('   ✅ Search system ready');
            return true;

        } catch (\Exception $e) {
            $this->error("   ❌ Search system failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Test database integration
     */
    private function testDatabaseIntegration(): bool
    {
        $this->info('💾 Testing Database Integration...');

        try {
            // Test knowledge entries
            $knowledgeCount = KnowledgeEntry::count();
            $this->line("   Knowledge entries: {$knowledgeCount}");

            // Test personality evolution
            $personalityCount = PersonalityEvolution::count();
            $this->line("   Personality aspects: {$personalityCount}");

            // Test chat sessions
            $sessionCount = ChatSession::count();
            $this->line("   Chat sessions: {$sessionCount}");

            $this->info('   ✅ Database integration working');
            return true;

        } catch (\Exception $e) {
            $this->error("   ❌ Database integration failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Display test summary
     */
    private function displayTestSummary(array $results): void
    {
        $this->info('');
        $this->info('📊 Test Summary:');
        $this->info('================');

        $passed = 0;
        $total = count($results);

        foreach ($results as $feature => $result) {
            $status = $result ? '✅ PASSED' : '❌ FAILED';
            $this->info("   {$feature}: {$status}");
            if ($result) $passed++;
        }

        $this->info('');
        $this->info("Overall: {$passed}/{$total} tests passed");

        if ($passed === $total) {
            $this->info('🎉 All tests passed! WIDDX AI is ready to go!');
        } else {
            $this->warn('⚠️  Some tests failed. Please check the configuration and try again.');
        }
    }
}
