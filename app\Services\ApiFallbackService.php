<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use App\Exceptions\ApiExceptions\{
    RateLimitException,
    AuthenticationException,
    ServerException,
    ClientException
};

class ApiFallbackService
{
    private DeepSeekClient $deepSeek;
    private GeminiClient $gemini;
    private string $primaryApi;

    public function __construct(
        DeepSeekClient $deepSeek,
        GeminiClient $gemini,
        string $primaryApi = 'deepseek'
    ) {
        $this->deepSeek = $deepSeek;
        $this->gemini = $gemini;
        $this->primaryApi = $primaryApi;

        Log::info('ApiFallbackService initialized', [
            'primary' => $primaryApi,
            'deepseek_configured' => $deepSeek->isConfigured(),
            'gemini_configured' => $gemini->isConfigured(),
        ]);
    }

    public function chat(array $messages, array $options = []): array
    {
        try {
            if ($this->primaryApi === 'deepseek' && $this->deepSeek->isConfigured()) {
                try {
                    return $this->deepSeek->chat($messages, $options);
                } catch (\Exception $e) {
                    if ($this->shouldFallback($e)) {
                        return $this->gemini->chat($messages, $options);
                    }
                    throw $e;
                }
            }

            if ($this->primaryApi === 'gemini' && $this->gemini->isConfigured()) {
                try {
                    return $this->gemini->chat($messages, $options);
                } catch (\Exception $e) {
                    if ($this->shouldFallback($e)) {
                        return $this->deepSeek->chat($messages, $options);
                    }
                    throw $e;
                }
            }

            throw new \RuntimeException('No configured API available');

        } catch (\Exception $e) {
            Log::error('API Fallback Service Error', [
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'previous' => $e->getPrevious() ? $e->getPrevious()->getMessage() : null,
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'content' => '',
            ];
        }
    }

    private function shouldFallback(\Exception $e): bool
    {
        // Don't fallback for client errors (4xx) except rate limits
        if ($e instanceof ClientException && !$e instanceof RateLimitException) {
            return false;
        }

        // Log unexpected exception types
        if (!($e instanceof RateLimitException) &&
            !($e instanceof AuthenticationException) &&
            !($e instanceof ServerException) &&
            !($e instanceof ClientException)) {
            Log::warning('Unexpected exception type in fallback', [
                'type' => get_class($e),
                'message' => $e->getMessage()
            ]);
        }

        // Don't fallback for authentication errors
        if ($e instanceof AuthenticationException) {
            return false;
        }

        return true;
    }

    public function buildMessages(string $systemPrompt, string $userMessage, array $conversationHistory = []): array
    {
        return $this->primaryApi === 'deepseek'
            ? $this->deepSeek->buildMessages($systemPrompt, $userMessage, $conversationHistory)
            : $this->gemini->buildMessages($systemPrompt, $userMessage, $conversationHistory);
    }
}
