// WIDDX AI Image Generation Handler
class ImageGenerationHandler {
    constructor() {
        // Modal elements
        this.modal = document.getElementById('image-gen-modal');
        this.promptInput = document.getElementById('image-prompt');
        this.generateBtn = document.getElementById('generate-image');
        this.cancelBtn = document.getElementById('cancel-image');
        this.closeBtn = document.getElementById('close-image-modal');
        this.imagePreview = document.getElementById('image-preview');
        this.generatedImage = document.getElementById('generated-image');
        this.loadingIndicator = document.getElementById('image-loading');
        
        // Chat elements
        this.messageInput = document.getElementById('message-input');
        this.chatForm = document.getElementById('chat-form');
        
        this.init();
    }
    
    init() {
        // Event listeners for modal
        this.generateBtn?.addEventListener('click', () => this.handleGenerate());        
        this.cancelBtn?.addEventListener('click', () => this.closeModal());
        this.closeBtn?.addEventListener('click', () => this.closeModal());
        this.promptInput?.addEventListener('input', () => this.toggleGenerateButton());
        
        // Close modal on outside click
        this.modal?.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.closeModal();
            }
        });
        
        // Add image generation button to chat input if not exists
        this.addImageButtonToChat();
    }
    
    addImageButtonToChat() {
        const inputArea = this.messageInput?.parentElement;
        if (!inputArea || document.getElementById('image-gen-btn')) return;
        
        const imageBtn = document.createElement('button');
        imageBtn.id = 'image-gen-btn';
        imageBtn.type = 'button';
        imageBtn.className = 'text-gray-400 hover:text-blue-500 p-2';
        imageBtn.title = 'Generate Image';
        imageBtn.innerHTML = `
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
            </svg>
        `;
        
        imageBtn.addEventListener('click', () => this.openModal());
        inputArea.insertBefore(imageBtn, this.messageInput.nextSibling);
    }
    
    openModal() {
        this.modal?.classList.remove('hidden');
        this.promptInput?.focus();
        document.body.style.overflow = 'hidden';
    }
    
    closeModal() {
        this.modal?.classList.add('hidden');
        this.imagePreview?.classList.add('hidden');
        this.loadingIndicator?.classList.add('hidden');
        this.promptInput.value = '';
        document.body.style.overflow = '';
    }
    
    toggleGenerateButton() {
        if (!this.generateBtn) return;
        this.generateBtn.disabled = !this.promptInput?.value.trim();
    }
    
    async handleGenerate() {
        const prompt = this.promptInput?.value.trim();
        if (!prompt) return;
        
        // Show loading state
        this.loadingIndicator?.classList.remove('hidden');
        this.generateBtn.disabled = true;
        this.imagePreview?.classList.add('hidden');
        
        try {
            const response = await fetch('/api/features/generate-image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({ prompt })
            });
            
            const data = await response.json();
            
            if (data.success && data.image_url) {
                // Show the generated image
                this.generatedImage.src = data.image_url;
                this.imagePreview?.classList.remove('hidden');
                
                // Update the chat input with the image markdown
                if (this.messageInput) {
                    this.messageInput.value = `![${prompt}](${data.image_url})`;
                    this.messageInput.dispatchEvent(new Event('input'));
                }
            } else {
                throw new Error(data.error || 'Failed to generate image');
            }
        } catch (error) {
            console.error('Image generation error:', error);
            alert(`Error generating image: ${error.message}`);
            this.closeModal();
        } finally {
            this.loadingIndicator?.classList.add('hidden');
            this.generateBtn.disabled = false;
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.imageGenerationHandler = new ImageGenerationHandler();
});
