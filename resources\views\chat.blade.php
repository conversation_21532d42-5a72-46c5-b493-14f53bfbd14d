@extends('layouts.app')

@section('title', 'WIDDX AI Chat')

@push('styles')
    <style>
        /* Moved from chat-production inline styles for reuse */
        .widdx-app {
            display: flex;
            height: 100vh;
            width: 100vw;
        }
        .widdx-sidebar {
            width: 320px;
            background: #111111;
            border-right: 1px solid #27272a;
            flex-shrink: 0;
            transition: transform 0.3s ease-in-out;
            position: relative;
            z-index: 50;
        }
        .widdx-sidebar.collapsed { transform: translateX(-100%); }
        .widdx-main-content { flex: 1; display: flex; flex-direction: column; }
        .widdx-gradient-primary { background: linear-gradient(135deg, #1d4ed8, #7c3aed); }
        .widdx-chat-header { background: #111111; border-bottom: 1px solid #27272a; padding: 1rem; flex-shrink: 0; }
        .widdx-messages-container { flex: 1; overflow-y: auto; padding: 1rem; }
        .widdx-input-area { background: #111111; border-top: 1px solid #27272a; padding: 1rem; flex-shrink: 0; }
        .widdx-message { margin-bottom: 1.5rem; opacity: 0; animation: slideUp 0.3s ease-out forwards; }
        .widdx-message-container { display: flex; align-items: flex-start; gap: 0.75rem; max-width: 1024px; margin: 0 auto; }
        .widdx-message-user .widdx-message-container { flex-direction: row-reverse; }
        .widdx-message-content { flex: 1; min-width: 0; }
        .widdx-message-text { background: #222222; border: 1px solid #27272a; border-radius: 1rem; padding: 1rem; color: #ffffff; line-height: 1.6; }
        /* typing indicator */
        .widdx-typing-indicator { display: flex; gap: 0.25rem; }
        .widdx-typing-dot { width: 0.5rem; height: 0.5rem; background: #1d4ed8; border-radius: 50%; animation: typing 1.4s infinite ease-in-out; }
        .widdx-typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .widdx-typing-dot:nth-child(3) { animation-delay: 0.4s; }
        @keyframes slideUp { from { opacity:0; transform: translateY(20px);} to { opacity:1; transform: translateY(0);} }
        @keyframes typing { 0%, 80%, 100% { transform: scale(0);} 40% { transform: scale(1);} }
        /* custom scrollbar */
        ::-webkit-scrollbar { width: 6px; background: #111111; }
        ::-webkit-scrollbar-thumb { background: #27272a; border-radius: 3px; }
        ::-webkit-scrollbar-thumb:hover { background: #3f3f46; }
    </style>
@endpush

@section('content')
<div class="widdx-app">
    {{-- Sidebar --}}
    <aside id="widdx-sidebar" class="widdx-sidebar">
        <div class="p-4">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 widdx-gradient-primary rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-sm">W</span>
                    </div>
                    <h1 class="text-lg font-semibold text-white">WIDDX AI</h1>
                </div>
                <button id="sidebar-toggle" class="text-gray-400 hover:text-white lg:hidden">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <button id="new-chat-btn" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg mb-6 transition-colors">New Chat</button>
            <div class="space-y-2">
                <h3 class="text-xs font-medium text-gray-400 uppercase tracking-wider mb-2">Recent Chats</h3>
                <div class="bg-gray-800 rounded-lg p-3">
                    <div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-500 rounded-full"></div><span class="text-sm text-white">Current Chat</span></div>
                    <p class="text-xs text-gray-400 mt-1">Now</p>
                </div>
            </div>
        </div>
    </aside>

    {{-- Main Content --}}
    <div class="widdx-main-content">
        {{-- Header --}}
        <header class="widdx-chat-header">
            <div class="flex items-center justify-between">
                <button id="mobile-sidebar-toggle" class="text-gray-400 hover:text-white lg:hidden">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/></svg>
                </button>
                <div class="flex items-center space-x-3">
                    <div class="widdx-gradient-primary w-8 h-8 rounded-full flex items-center justify-center"><span class="text-white font-bold text-sm">W</span></div>
                    <div><h1 class="text-lg font-semibold text-white">WIDDX AI</h1><p class="text-xs text-gray-400" id="chat-status">Online - Ready to help</p></div>
                </div>
                <div class="flex items-center space-x-2">
                    <select id="personality-selector" class="bg-gray-700 text-white rounded-lg px-3 py-2 text-sm border border-gray-600">
                        @foreach($personalities as $personality)
                            <option value="{{ $personality['name'] }}" {{ $personality['name'] === 'neutral' ? 'selected' : '' }}>{{ ucfirst($personality['name']) }}</option>
                        @endforeach
                    </select>
                    <button id="think-mode-toggle" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center space-x-2"><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/></svg><span>Deep Think</span></button>
                    <button id="theme-toggle" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center space-x-2" title="Toggle Dark / Light"><svg id="theme-toggle-icon" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"></svg><span id="theme-toggle-label">Light</span></button>
                </div>
            </div>
        </header>
        {{-- Messages --}}
        <main class="widdx-messages-container">
            <div id="messages-list">@include('partials.chat_welcome')</div>
            <div id="typing-indicator" class="widdx-message hidden"><div class="widdx-message-container"><div class="widdx-gradient-primary w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0"><span class="text-white font-bold text-sm">W</span></div><div class="bg-gray-800 rounded-lg p-4"><div class="widdx-typing-indicator"><div class="widdx-typing-dot"></div><div class="widdx-typing-dot"></div><div class="widdx-typing-dot"></div></div></div></div></div>
        </main>
        {{-- Input Area --}}
        <footer class="widdx-input-area"><form id="chat-form" class="widdx-input-container"><textarea id="message-input" placeholder="Type your message here... (Ctrl+Enter to send)" class="widdx-textarea" rows="1" maxlength="10000"></textarea><button type="submit" id="send-button" class="widdx-send-btn" disabled><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/></svg></button></form></footer>
    </div>
</div>

{{-- Image generation modal retained from original --}}
@include('partials.image_gen_modal')
@endsection

@push('scripts')
<script src="/js/chat.js"></script>
@endpush
