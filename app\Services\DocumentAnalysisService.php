<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\UploadedFile;

class DocumentAnalysisService
{
    private DeepSeekClient $deepSeek;
    private array $supportedTypes;
    private int $maxFileSize;

    public function __construct(DeepSeekClient $deepSeek)
    {
        $this->deepSeek = $deepSeek;
        $this->supportedTypes = [
            'pdf' => 'application/pdf',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'doc' => 'application/msword',
            'txt' => 'text/plain',
            'rtf' => 'application/rtf',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'xls' => 'application/vnd.ms-excel',
            'csv' => 'text/csv',
        ];
        $this->maxFileSize = 10 * 1024 * 1024; // 10MB
    }

    /**
     * Analyze uploaded document
     */
    public function analyzeDocument(UploadedFile $file, array $options = []): array
    {
        try {
            Log::info('Document analysis started', [
                'filename' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'type' => $file->getMimeType(),
            ]);

            // Validate file
            $validation = $this->validateFile($file);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => $validation['error'],
                    'filename' => $file->getClientOriginalName(),
                ];
            }

            // Save file temporarily
            $savedPath = $this->saveTemporaryFile($file);

            // Extract text content
            $textContent = $this->extractTextContent($savedPath, $file->getClientOriginalExtension());

            if (!$textContent) {
                return [
                    'success' => false,
                    'error' => 'فشل في استخراج النص من المستند',
                    'filename' => $file->getClientOriginalName(),
                ];
            }

            // Perform analysis
            $analysis = $this->performDocumentAnalysis($textContent, $options);

            // Generate summary
            $summary = $this->generateSummary($textContent, $options);

            // Extract key information
            $keyInfo = $this->extractKeyInformation($textContent, $options);

            $result = [
                'success' => true,
                'filename' => $file->getClientOriginalName(),
                'file_size' => $file->getSize(),
                'file_type' => $file->getClientOriginalExtension(),
                'text_length' => strlen($textContent),
                'analysis' => $analysis,
                'summary' => $summary,
                'key_information' => $keyInfo,
                'metadata' => [
                    'processed_at' => now()->toISOString(),
                    'language_detected' => $this->detectLanguage($textContent),
                    'word_count' => str_word_count($textContent),
                    'character_count' => strlen($textContent),
                ],
            ];

            // Clean up temporary file
            Storage::delete($savedPath);

            Log::info('Document analysis completed', [
                'filename' => $file->getClientOriginalName(),
                'text_length' => strlen($textContent),
                'success' => true,
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Document analysis error', [
                'filename' => $file->getClientOriginalName(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'filename' => $file->getClientOriginalName(),
            ];
        }
    }

    /**
     * Validate uploaded file
     */
    private function validateFile(UploadedFile $file): array
    {
        // Check file size
        if ($file->getSize() > $this->maxFileSize) {
            return [
                'valid' => false,
                'error' => 'حجم الملف كبير جداً. الحد الأقصى هو 10 ميجابايت.',
            ];
        }

        // Check file type
        $extension = strtolower($file->getClientOriginalExtension());
        if (!array_key_exists($extension, $this->supportedTypes)) {
            return [
                'valid' => false,
                'error' => 'نوع الملف غير مدعوم. الأنواع المدعومة: ' . implode(', ', array_keys($this->supportedTypes)),
            ];
        }

        // Check MIME type
        $mimeType = $file->getMimeType();
        if ($mimeType !== $this->supportedTypes[$extension]) {
            return [
                'valid' => false,
                'error' => 'نوع الملف لا يتطابق مع امتداده.',
            ];
        }

        return ['valid' => true];
    }

    /**
     * Save file temporarily for processing
     */
    private function saveTemporaryFile(UploadedFile $file): string
    {
        $filename = 'temp_' . uniqid() . '.' . $file->getClientOriginalExtension();
        $path = 'temp_documents/' . $filename;
        
        Storage::put($path, $file->getContent());
        
        return $path;
    }

    /**
     * Extract text content from file
     */
    private function extractTextContent(string $filePath, string $extension): ?string
    {
        try {
            $fullPath = Storage::path($filePath);

            switch (strtolower($extension)) {
                case 'txt':
                    return Storage::get($filePath);

                case 'pdf':
                    return $this->extractFromPDF($fullPath);

                case 'docx':
                    return $this->extractFromDocx($fullPath);

                case 'doc':
                    return $this->extractFromDoc($fullPath);

                case 'xlsx':
                case 'xls':
                    return $this->extractFromExcel($fullPath);

                case 'csv':
                    return $this->extractFromCSV($fullPath);

                case 'rtf':
                    return $this->extractFromRTF($fullPath);

                default:
                    return null;
            }

        } catch (\Exception $e) {
            Log::error('Text extraction failed', [
                'file_path' => $filePath,
                'extension' => $extension,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Extract text from PDF (simplified - would need proper PDF library)
     */
    private function extractFromPDF(string $filePath): ?string
    {
        // This is a placeholder - in production, use a library like smalot/pdfparser
        try {
            // For now, return a placeholder message
            return "PDF content extraction requires additional libraries. Please install smalot/pdfparser or similar.";
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Extract text from DOCX
     */
    private function extractFromDocx(string $filePath): ?string
    {
        try {
            // This is a simplified version - would need PhpOffice/PhpWord in production
            $zip = new \ZipArchive();
            if ($zip->open($filePath) === TRUE) {
                $content = $zip->getFromName('word/document.xml');
                $zip->close();
                
                if ($content) {
                    // Simple XML parsing to extract text
                    $content = strip_tags($content);
                    return $content;
                }
            }
            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Extract text from DOC (placeholder)
     */
    private function extractFromDoc(string $filePath): ?string
    {
        // Would need antiword or similar tool
        return "DOC file processing requires additional tools.";
    }

    /**
     * Extract text from Excel files
     */
    private function extractFromExcel(string $filePath): ?string
    {
        // Placeholder - would need PhpOffice/PhpSpreadsheet
        return "Excel file processing requires PhpOffice/PhpSpreadsheet library.";
    }

    /**
     * Extract text from CSV
     */
    private function extractFromCSV(string $filePath): ?string
    {
        try {
            $content = Storage::get(str_replace(Storage::path(''), '', $filePath));
            $lines = explode("\n", $content);
            $text = '';
            
            foreach ($lines as $line) {
                $text .= str_replace(',', ' | ', $line) . "\n";
            }
            
            return $text;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Extract text from RTF
     */
    private function extractFromRTF(string $filePath): ?string
    {
        try {
            $content = Storage::get(str_replace(Storage::path(''), '', $filePath));
            // Simple RTF text extraction (very basic)
            $content = preg_replace('/\{[^}]*\}/', '', $content);
            $content = preg_replace('/\\\\[a-z]+[0-9]*/', '', $content);
            return trim($content);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Perform comprehensive document analysis
     */
    private function performDocumentAnalysis(string $content, array $options): array
    {
        try {
            $analysisType = $options['analysis_type'] ?? 'comprehensive';
            
            $prompt = "Analyze the following document content and provide:
1. Document type and structure
2. Main topics and themes
3. Key arguments or points
4. Writing style and tone
5. Target audience
6. Document quality assessment

Content (first 2000 characters):
" . substr($content, 0, 2000) . "

Provide analysis in JSON format with detailed insights.";

            $response = $this->deepSeek->chat([
                ['role' => 'system', 'content' => 'You are a document analysis expert. Provide comprehensive analysis of document content.'],
                ['role' => 'user', 'content' => $prompt],
            ], ['max_tokens' => 800]);

            if ($response['success']) {
                $responseContent = $response['content'];
                if (preg_match('/\{.*\}/s', $responseContent, $matches)) {
                    $analysis = json_decode($matches[0], true);
                    if (is_array($analysis)) {
                        return $analysis;
                    }
                }
            }

            // Fallback analysis
            return [
                'document_type' => 'نص عام',
                'main_topics' => ['موضوع رئيسي'],
                'writing_style' => 'رسمي',
                'quality_score' => 7,
            ];

        } catch (\Exception $e) {
            Log::warning('Document analysis failed', ['error' => $e->getMessage()]);
            return ['error' => 'فشل في تحليل المستند'];
        }
    }

    /**
     * Generate document summary
     */
    private function generateSummary(string $content, array $options): string
    {
        try {
            $summaryLength = $options['summary_length'] ?? 'medium';
            $maxTokens = match($summaryLength) {
                'short' => 200,
                'medium' => 400,
                'long' => 600,
                default => 400,
            };

            $prompt = "Summarize the following document content in a clear and concise manner. Focus on the main points and key information:

Content:
" . substr($content, 0, 3000) . "

Provide a {$summaryLength} summary that captures the essential information.";

            $response = $this->deepSeek->chat([
                ['role' => 'system', 'content' => 'You are an expert at creating clear, informative document summaries.'],
                ['role' => 'user', 'content' => $prompt],
            ], ['max_tokens' => $maxTokens]);

            return $response['success'] ? $response['content'] : 'فشل في إنشاء ملخص للمستند.';

        } catch (\Exception $e) {
            Log::warning('Summary generation failed', ['error' => $e->getMessage()]);
            return 'فشل في إنشاء ملخص للمستند.';
        }
    }

    /**
     * Extract key information from document
     */
    private function extractKeyInformation(string $content, array $options): array
    {
        try {
            $prompt = "Extract key information from the following document:
1. Important dates
2. Names and entities
3. Key numbers and statistics
4. Important facts
5. Action items or conclusions

Content:
" . substr($content, 0, 2500) . "

Respond in JSON format with categorized key information.";

            $response = $this->deepSeek->chat([
                ['role' => 'system', 'content' => 'You are an expert at extracting key information from documents.'],
                ['role' => 'user', 'content' => $prompt],
            ], ['max_tokens' => 600]);

            if ($response['success']) {
                $responseContent = $response['content'];
                if (preg_match('/\{.*\}/s', $responseContent, $matches)) {
                    $keyInfo = json_decode($matches[0], true);
                    if (is_array($keyInfo)) {
                        return $keyInfo;
                    }
                }
            }

            return [
                'dates' => [],
                'entities' => [],
                'numbers' => [],
                'facts' => [],
                'conclusions' => [],
            ];

        } catch (\Exception $e) {
            Log::warning('Key information extraction failed', ['error' => $e->getMessage()]);
            return ['error' => 'فشل في استخراج المعلومات الرئيسية'];
        }
    }

    /**
     * Detect document language
     */
    private function detectLanguage(string $content): string
    {
        // Simple language detection based on character patterns
        if (preg_match('/[\x{0600}-\x{06FF}]/u', $content)) {
            return 'Arabic';
        } elseif (preg_match('/[a-zA-Z]/', $content)) {
            return 'English';
        } else {
            return 'Unknown';
        }
    }

    /**
     * Get supported file types
     */
    public function getSupportedTypes(): array
    {
        return array_keys($this->supportedTypes);
    }

    /**
     * Get analysis options
     */
    public function getAnalysisOptions(): array
    {
        return [
            'analysis_types' => ['comprehensive', 'quick', 'detailed'],
            'summary_lengths' => ['short', 'medium', 'long'],
            'supported_formats' => array_keys($this->supportedTypes),
            'max_file_size' => $this->maxFileSize,
        ];
    }
}
