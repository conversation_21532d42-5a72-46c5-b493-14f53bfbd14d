<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class FreeVoiceService
{
    /**
     * Generate browser-based TTS instructions
     */
    public function generateTtsInstructions(string $text, array $options = []): array
    {
        try {
            $language = $options['language'] ?? 'ar';
            $voice = $options['voice'] ?? 'default';
            $speed = $options['speed'] ?? 1.0;
            $pitch = $options['pitch'] ?? 1.0;

            // Generate JavaScript code for browser TTS
            $jsCode = $this->generateTtsJavaScript($text, $language, $voice, $speed, $pitch);

            Log::info('Free TTS instructions generated', [
                'text_length' => strlen($text),
                'language' => $language,
            ]);

            return [
                'success' => true,
                'provider' => 'browser_tts',
                'text' => $text,
                'javascript_code' => $jsCode,
                'instructions' => [
                    'ar' => 'استخدم الكود التالي في المتصفح لتشغيل النص صوتياً',
                    'en' => 'Use the following code in the browser to play the text as speech',
                ],
                'metadata' => [
                    'language' => $language,
                    'voice' => $voice,
                    'speed' => $speed,
                    'pitch' => $pitch,
                    'free_alternative' => true,
                ],
            ];

        } catch (\Exception $e) {
            Log::error('Free TTS error', [
                'text' => substr($text, 0, 100),
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'text' => $text,
            ];
        }
    }

    /**
     * Generate STT instructions for browser
     */
    public function generateSttInstructions(array $options = []): array
    {
        try {
            $language = $options['language'] ?? 'ar';
            $continuous = $options['continuous'] ?? false;

            // Generate JavaScript code for browser STT
            $jsCode = $this->generateSttJavaScript($language, $continuous);

            return [
                'success' => true,
                'provider' => 'browser_stt',
                'javascript_code' => $jsCode,
                'instructions' => [
                    'ar' => 'استخدم الكود التالي في المتصفح للتعرف على الكلام',
                    'en' => 'Use the following code in the browser for speech recognition',
                ],
                'metadata' => [
                    'language' => $language,
                    'continuous' => $continuous,
                    'free_alternative' => true,
                ],
            ];

        } catch (\Exception $e) {
            Log::error('Free STT error', ['error' => $e->getMessage()]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Generate TTS JavaScript code
     */
    private function generateTtsJavaScript(string $text, string $language, string $voice, float $speed, float $pitch): string
    {
        $escapedText = addslashes($text);
        $langCode = $this->getLanguageCode($language);

        return "
// Free Text-to-Speech using Web Speech API
function playTextToSpeech() {
    if ('speechSynthesis' in window) {
        // Cancel any ongoing speech
        speechSynthesis.cancel();
        
        // Create speech utterance
        const utterance = new SpeechSynthesisUtterance('{$escapedText}');
        
        // Set language
        utterance.lang = '{$langCode}';
        
        // Set voice properties
        utterance.rate = {$speed};
        utterance.pitch = {$pitch};
        utterance.volume = 1.0;
        
        // Try to find appropriate voice
        const voices = speechSynthesis.getVoices();
        const arabicVoice = voices.find(voice => 
            voice.lang.startsWith('{$langCode}') || 
            voice.name.includes('Arabic') ||
            voice.name.includes('عربي')
        );
        
        if (arabicVoice) {
            utterance.voice = arabicVoice;
        }
        
        // Event handlers
        utterance.onstart = () => console.log('Speech started');
        utterance.onend = () => console.log('Speech ended');
        utterance.onerror = (e) => console.error('Speech error:', e);
        
        // Speak the text
        speechSynthesis.speak(utterance);
        
        return true;
    } else {
        alert('متصفحك لا يدعم تقنية تحويل النص إلى كلام');
        return false;
    }
}

// Load voices (some browsers need this)
if ('speechSynthesis' in window) {
    speechSynthesis.onvoiceschanged = () => {
        console.log('Voices loaded:', speechSynthesis.getVoices().length);
    };
}

// Call the function
playTextToSpeech();
";
    }

    /**
     * Generate STT JavaScript code
     */
    private function generateSttJavaScript(string $language, bool $continuous): string
    {
        $langCode = $this->getLanguageCode($language);
        $continuousStr = $continuous ? 'true' : 'false';

        return "
// Free Speech-to-Text using Web Speech API
function startSpeechRecognition() {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();
        
        // Configure recognition
        recognition.lang = '{$langCode}';
        recognition.continuous = {$continuousStr};
        recognition.interimResults = true;
        recognition.maxAlternatives = 3;
        
        // Event handlers
        recognition.onstart = () => {
            console.log('Speech recognition started');
            document.body.style.backgroundColor = '#ffebee'; // Visual feedback
        };
        
        recognition.onresult = (event) => {
            let finalTranscript = '';
            let interimTranscript = '';
            
            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                
                if (event.results[i].isFinal) {
                    finalTranscript += transcript;
                } else {
                    interimTranscript += transcript;
                }
            }
            
            console.log('Final:', finalTranscript);
            console.log('Interim:', interimTranscript);
            
            // You can send the result to your application here
            if (finalTranscript) {
                // Example: send to chat input
                const chatInput = document.getElementById('message-input');
                if (chatInput) {
                    chatInput.value = finalTranscript;
                }
            }
        };
        
        recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            document.body.style.backgroundColor = ''; // Reset background
        };
        
        recognition.onend = () => {
            console.log('Speech recognition ended');
            document.body.style.backgroundColor = ''; // Reset background
        };
        
        // Start recognition
        recognition.start();
        
        return recognition;
    } else {
        alert('متصفحك لا يدعم تقنية التعرف على الكلام');
        return null;
    }
}

// Start speech recognition
const recognition = startSpeechRecognition();

// Stop recognition after 30 seconds (optional)
setTimeout(() => {
    if (recognition) {
        recognition.stop();
    }
}, 30000);
";
    }

    /**
     * Get language code for Web Speech API
     */
    private function getLanguageCode(string $language): string
    {
        $languageCodes = [
            'ar' => 'ar-SA',
            'arabic' => 'ar-SA',
            'en' => 'en-US',
            'english' => 'en-US',
            'fr' => 'fr-FR',
            'french' => 'fr-FR',
            'es' => 'es-ES',
            'spanish' => 'es-ES',
            'de' => 'de-DE',
            'german' => 'de-DE',
        ];

        return $languageCodes[strtolower($language)] ?? 'ar-SA';
    }

    /**
     * Generate voice control HTML/JS widget
     */
    public function generateVoiceWidget(array $options = []): array
    {
        try {
            $language = $options['language'] ?? 'ar';
            $theme = $options['theme'] ?? 'dark';

            $html = $this->generateVoiceWidgetHtml($language, $theme);
            $css = $this->generateVoiceWidgetCss($theme);
            $js = $this->generateVoiceWidgetJs($language);

            return [
                'success' => true,
                'provider' => 'browser_voice_widget',
                'html' => $html,
                'css' => $css,
                'javascript' => $js,
                'instructions' => [
                    'ar' => 'أضف هذا الكود إلى صفحتك للحصول على عنصر تحكم صوتي',
                    'en' => 'Add this code to your page to get a voice control widget',
                ],
                'metadata' => [
                    'language' => $language,
                    'theme' => $theme,
                    'free_alternative' => true,
                ],
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Generate voice widget HTML
     */
    private function generateVoiceWidgetHtml(string $language, string $theme): string
    {
        $isArabic = $language === 'ar' || $language === 'arabic';
        $micText = $isArabic ? '🎤 اضغط للتحدث' : '🎤 Click to Speak';
        $speakText = $isArabic ? '🔊 قراءة النص' : '🔊 Read Text';

        return "
<div id='voice-widget' class='voice-widget {$theme}'>
    <div class='voice-controls'>
        <button id='mic-btn' class='voice-btn mic-btn'>
            {$micText}
        </button>
        <button id='speak-btn' class='voice-btn speak-btn'>
            {$speakText}
        </button>
    </div>
    <div id='voice-status' class='voice-status'></div>
    <div id='voice-result' class='voice-result'></div>
</div>
";
    }

    /**
     * Generate voice widget CSS
     */
    private function generateVoiceWidgetCss(string $theme): string
    {
        $bgColor = $theme === 'dark' ? '#1f2937' : '#ffffff';
        $textColor = $theme === 'dark' ? '#f9fafb' : '#1f2937';
        $btnColor = $theme === 'dark' ? '#3b82f6' : '#2563eb';

        return "
.voice-widget {
    background: {$bgColor};
    color: {$textColor};
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
    font-family: Arial, sans-serif;
}

.voice-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
}

.voice-btn {
    background: {$btnColor};
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.voice-btn:hover {
    opacity: 0.9;
}

.voice-btn:active {
    transform: scale(0.98);
}

.voice-btn.recording {
    background: #ef4444;
    animation: pulse 1s infinite;
}

.voice-status {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 8px;
}

.voice-result {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 4px;
    padding: 8px;
    min-height: 40px;
    font-size: 14px;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}
";
    }

    /**
     * Generate voice widget JavaScript
     */
    private function generateVoiceWidgetJs(string $language): string
    {
        $langCode = $this->getLanguageCode($language);

        return "
// Voice Widget JavaScript
(function() {
    const micBtn = document.getElementById('mic-btn');
    const speakBtn = document.getElementById('speak-btn');
    const status = document.getElementById('voice-status');
    const result = document.getElementById('voice-result');
    
    let recognition = null;
    let isRecording = false;
    
    // Speech Recognition
    if (micBtn) {
        micBtn.addEventListener('click', () => {
            if (isRecording) {
                stopRecording();
            } else {
                startRecording();
            }
        });
    }
    
    // Text to Speech
    if (speakBtn) {
        speakBtn.addEventListener('click', () => {
            const textToSpeak = result.textContent || 'مرحباً، هذا اختبار للصوت';
            speakText(textToSpeak);
        });
    }
    
    function startRecording() {
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            status.textContent = 'متصفحك لا يدعم التعرف على الكلام';
            return;
        }
        
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognition = new SpeechRecognition();
        
        recognition.lang = '{$langCode}';
        recognition.continuous = false;
        recognition.interimResults = true;
        
        recognition.onstart = () => {
            isRecording = true;
            micBtn.classList.add('recording');
            micBtn.textContent = '🛑 إيقاف التسجيل';
            status.textContent = 'جاري الاستماع...';
        };
        
        recognition.onresult = (event) => {
            let transcript = '';
            for (let i = 0; i < event.results.length; i++) {
                transcript += event.results[i][0].transcript;
            }
            result.textContent = transcript;
        };
        
        recognition.onend = () => {
            stopRecording();
        };
        
        recognition.onerror = (event) => {
            status.textContent = 'خطأ في التعرف على الكلام: ' + event.error;
            stopRecording();
        };
        
        recognition.start();
    }
    
    function stopRecording() {
        if (recognition) {
            recognition.stop();
        }
        isRecording = false;
        micBtn.classList.remove('recording');
        micBtn.textContent = '🎤 اضغط للتحدث';
        status.textContent = 'تم إيقاف التسجيل';
    }
    
    function speakText(text) {
        if (!('speechSynthesis' in window)) {
            status.textContent = 'متصفحك لا يدعم تحويل النص إلى كلام';
            return;
        }
        
        speechSynthesis.cancel();
        
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = '{$langCode}';
        utterance.rate = 1.0;
        utterance.pitch = 1.0;
        
        utterance.onstart = () => {
            status.textContent = 'جاري القراءة...';
            speakBtn.textContent = '⏸️ إيقاف القراءة';
        };
        
        utterance.onend = () => {
            status.textContent = 'انتهت القراءة';
            speakBtn.textContent = '🔊 قراءة النص';
        };
        
        speechSynthesis.speak(utterance);
    }
})();
";
    }

    /**
     * Get available free voice options
     */
    public function getAvailableOptions(): array
    {
        return [
            'providers' => ['browser_tts', 'browser_stt', 'browser_voice_widget'],
            'languages' => ['ar', 'en', 'fr', 'es', 'de'],
            'features' => [
                'text_to_speech' => 'تحويل النص إلى كلام باستخدام متصفح الويب',
                'speech_to_text' => 'التعرف على الكلام باستخدام متصفح الويب',
                'voice_widget' => 'عنصر تحكم صوتي تفاعلي',
            ],
            'limitations' => [
                'يتطلب متصفح ويب حديث',
                'يحتاج إذن المستخدم للوصول للميكروفون',
                'جودة الصوت تعتمد على المتصفح والنظام',
            ],
            'free_alternative' => true,
        ];
    }
}
