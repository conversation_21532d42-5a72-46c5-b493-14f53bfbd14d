<?php

namespace Tests\Feature;

use App\Services\ImageGenerationService;
use App\Services\ImageStorageService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ImageGenerationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Configure test storage
        Storage::fake('public');
        
        // Create test directory
        Storage::disk('public')->makeDirectory('generated_images');
    }

    /** @test */
    public function it_can_generate_an_image()
    {
        // Create mock services
        $storageService = new ImageStorageService();
        $imageService = new ImageGenerationService($storageService);
        
        // Test with a simple prompt
        $prompt = 'A beautiful sunset over mountains';
        $result = $imageService->generateImage($prompt, [
            'provider' => 'huggingface',
            'model' => 'stabilityai/stable-diffusion-2-1'
        ]);
        
        // Assert the result
        $this->assertArrayHasKey('success', $result);
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('images', $result);
        $this->assertNotEmpty($result['images']);
        
        // Check if the image was saved
        $imagePath = $result['images'][0]['path'] ?? null;
        if ($imagePath) {
            $this->assertTrue(Storage::disk('public')->exists($imagePath));
        }
    }
    
    /** @test */
    public function it_returns_storage_stats()
    {
        $storageService = new ImageStorageService();
        $stats = $storageService->getStorageStats();
        
        $this->assertArrayHasKey('total_files', $stats);
        $this->assertArrayHasKey('total_size', $stats);
        $this->assertArrayHasKey('usage_percentage', $stats);
        $this->assertArrayHasKey('status', $stats);
    }
    
    /** @test */
    public function it_can_cleanup_old_images()
    {
        $storageService = new ImageStorageService();
        
        // Test with a very recent cutoff date (should delete nothing)
        $result = $storageService->cleanupOldImages(1); // 1 day
        
        $this->assertArrayHasKey('deleted_count', $result);
        $this->assertEquals(0, $result['deleted_count']);
        $this->assertArrayHasKey('freed_space', $result);
    }
    
    /** @test */
    public function it_validates_image_requests()
    {
        $storageService = new ImageStorageService();
        $imageService = new ImageGenerationService($storageService);
        
        // Test empty prompt
        try {
            $imageService->generateImage('', ['provider' => 'huggingface']);
            $this->fail('Expected validation exception for empty prompt');
        } catch (\InvalidArgumentException $e) {
            $this->assertStringContainsString('cannot be empty', $e->getMessage());
        }
        
        // Test very long prompt
        $longPrompt = str_repeat('a', 1001);
        try {
            $imageService->generateImage($longPrompt, ['provider' => 'huggingface']);
            $this->fail('Expected validation exception for long prompt');
        } catch (\InvalidArgumentException $e) {
            $this->assertStringContainsString('too long', $e->getMessage());
        }
    }
}
