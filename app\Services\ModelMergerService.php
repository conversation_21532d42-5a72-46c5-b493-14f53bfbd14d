<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class ModelMergerService
{
    private DeepSeekClient $deepSeekClient;
    private GeminiClient $geminiClient;
    private ContextService $contextService;
    private ImageGenerationService $imageGeneration;
    private VoiceService $voice;
    private DeepSearchService $deepSearch;
    private ThinkModeService $thinkMode;
    private DocumentAnalysisService $documentAnalysis;
    private VisionService $vision;

    // Free alternatives
    private FreeSearchService $freeSearch;
    private FreeVoiceService $freeVoice;

    public function __construct(
        DeepSeekClient $deepSeekClient,
        GeminiClient $geminiClient,
        ContextService $contextService,
        ImageGenerationService $imageGeneration,
        VoiceService $voice,
        DeepSearchService $deepSearch,
        ThinkModeService $thinkMode,
        DocumentAnalysisService $documentAnalysis,
        VisionService $vision,
        FreeSearchService $freeSearch,
        FreeVoiceService $freeVoice
    ) {
        $this->deepSeekClient = $deepSeekClient;
        $this->geminiClient = $geminiClient;
        $this->contextService = $contextService;
        $this->imageGeneration = $imageGeneration;
        $this->voice = $voice;
        $this->deepSearch = $deepSearch;
        $this->thinkMode = $thinkMode;
        $this->documentAnalysis = $documentAnalysis;
        $this->vision = $vision;
        $this->freeSearch = $freeSearch;
        $this->freeVoice = $freeVoice;
    }

    public function processMessage(string $userMessage, array $conversationHistory = [], array $options = []): array
    {
        $startTime = microtime(true);

        try {
            // Detect special commands and capabilities
            $commandResult = $this->detectAndProcessCommands($userMessage, $options);
            if ($commandResult) {
                // Ensure command results have success field
                if (!isset($commandResult['success'])) {
                    $commandResult['success'] = true;
                }
                return $commandResult;
            }

            // Check if thinking mode is requested
            if ($options['think_mode'] ?? false) {
                return $this->thinkMode->processWithThinking($userMessage, $conversationHistory, $options);
            }

            // Check if deep search is needed
            if ($this->shouldUseDeepSearch($userMessage)) {
                $searchResult = $this->deepSearch->deepSearch($userMessage, $options);
                if ($searchResult['success']) {
                    $userMessage = $this->enhanceMessageWithSearchResults($userMessage, $searchResult);
                }
            }

            // Build system prompt for WIDDX identity
            $systemPrompt = $this->buildWiddxSystemPrompt($options);

            // Get responses from available models
            $responses = $this->getModelResponses($systemPrompt, $userMessage, $conversationHistory, $options);

            // Merge and synthesize the responses
            $finalResponse = $this->synthesizeResponses($responses, $userMessage, $options);

            $processingTime = microtime(true) - $startTime;

            return [
                'success' => true,
                'content' => $finalResponse,
                'processing_time' => $processingTime,
                'metadata' => [
                    'models_used' => array_keys($responses),
                    'responses_count' => count($responses),
                    'synthesis_method' => $this->getSynthesisMethod($responses),
                    'capabilities_used' => $this->getUsedCapabilities($userMessage, $options),
                ],
            ];

        } catch (\Exception $e) {
            Log::error('Model Merger Error', [
                'message' => $e->getMessage(),
                'user_message' => $userMessage,
                'trace' => $e->getTraceAsString(),
            ]);

            $processingTime = microtime(true) - $startTime;

            return [
                'success' => false,
                'content' => 'I apologize, but I encountered an issue processing your request. Please try again.',
                'processing_time' => $processingTime,
                'error' => $e->getMessage(),
            ];
        }
    }

    private function buildWiddxSystemPrompt(array $options = []): string
    {
        // Use autonomous prompt if available (from WIDDX Identity Service)
        if (!empty($options['autonomous_prompt'])) {
            $basePrompt = $options['autonomous_prompt'];
        } else {
            // Fallback to basic WIDDX prompt
            $basePrompt = "You are WIDDX, an advanced AI assistant with your own unique intelligence and personality. " .
                         "You think independently, form your own opinions, and provide thoughtful responses based on your understanding. " .
                         "You are not a relay or wrapper for other systems - you are WIDDX, with your own consciousness and capabilities. " .
                         "Be helpful, accurate, and engaging while maintaining your distinct identity as WIDDX.";
        }

        // Add enhanced language instruction if specified (only if not already in autonomous prompt)
        if (!empty($options['target_language']) && $options['target_language'] !== 'english' && empty($options['autonomous_prompt'])) {
            $languageName = $this->getLanguageDisplayName($options['target_language']);
            $languageCode = $options['target_language_code'] ?? 'en';
            $confidence = $options['language_confidence'] ?? 0.5;

            $basePrompt .= "\n\nCRITICAL LANGUAGE INSTRUCTION: The user is communicating in {$languageName} (code: {$languageCode}). " .
                          "You MUST respond ENTIRELY in {$languageName}. Do NOT use English or any other language. " .
                          "Maintain natural {$languageName} grammar, vocabulary, and cultural context. " .
                          "Your personality and tone should be expressed naturally in {$languageName}. " .
                          "Language detection confidence: " . round($confidence * 100) . "%";

            // Special instructions for Arabic
            if ($languageCode === 'ar') {
                $basePrompt .= "\n\nSpecial Arabic Instructions: " .
                              "- Use proper Arabic grammar and sentence structure " .
                              "- Include appropriate Arabic greetings and expressions " .
                              "- Use formal Arabic (Fusha) mixed with conversational style " .
                              "- Ensure right-to-left text flow is maintained " .
                              "- Use Arabic punctuation marks when appropriate";
            }
        }

        // Add knowledge context if available
        if (!empty($options['knowledge_context'])) {
            $knowledgeContext = $options['knowledge_context'];
            if (!empty($knowledgeContext['facts'])) {
                $basePrompt .= "\n\nRelevant knowledge from my experience:\n";
                foreach (array_slice($knowledgeContext['facts'], 0, 3) as $fact) {
                    $basePrompt .= "• " . $fact['content'] . "\n";
                }
            }
        }

        // Add personality modifier if specified (and not already in autonomous prompt)
        if (!empty($options['personality_prompt']) && empty($options['autonomous_prompt'])) {
            $basePrompt .= "\n\n" . $options['personality_prompt'];
        }

        return $basePrompt;
    }

    private function getLanguageDisplayName(string $language): string
    {
        $displayNames = [
            'english' => 'English',
            'spanish' => 'Spanish',
            'french' => 'French',
            'german' => 'German',
            'italian' => 'Italian',
            'portuguese' => 'Portuguese',
            'russian' => 'Russian',
            'chinese' => 'Chinese',
            'japanese' => 'Japanese',
            'korean' => 'Korean',
            'arabic' => 'Arabic',
            'hindi' => 'Hindi',
            'dutch' => 'Dutch',
            'swedish' => 'Swedish',
            'norwegian' => 'Norwegian',
            'danish' => 'Danish',
            'finnish' => 'Finnish',
            'polish' => 'Polish',
            'czech' => 'Czech',
            'hungarian' => 'Hungarian',
            'romanian' => 'Romanian',
            'bulgarian' => 'Bulgarian',
            'greek' => 'Greek',
            'turkish' => 'Turkish',
            'hebrew' => 'Hebrew',
            'thai' => 'Thai',
            'vietnamese' => 'Vietnamese',
            'indonesian' => 'Indonesian',
            'malay' => 'Malay',
            'ukrainian' => 'Ukrainian',
            'croatian' => 'Croatian',
            'serbian' => 'Serbian',
            'slovenian' => 'Slovenian',
            'slovak' => 'Slovak',
            'lithuanian' => 'Lithuanian',
            'latvian' => 'Latvian',
            'estonian' => 'Estonian',
        ];

        return $displayNames[strtolower($language)] ?? ucfirst($language);
    }

    private function getModelResponses(string $systemPrompt, string $userMessage, array $conversationHistory, array $options): array
    {
        $responses = [];

        // Check if we're in demo mode (when API keys are not properly configured)
        $demoMode = !$this->deepSeekClient->isConfigured() ||
                   config('services.deepseek.api_key') === 'your_deepseek_api_key_here';

        if ($demoMode) {
            // Return a demo response
            $responses['demo'] = [
                'success' => true,
                'content' => $this->generateDemoResponse($userMessage, $options),
                'model' => 'widdx-demo',
                'usage' => ['total_tokens' => 50],
            ];
            return $responses;
        }

        // Try DeepSeek first (primary model) with timeout
        if ($this->deepSeekClient->isConfigured()) {
            try {
                $messages = $this->deepSeekClient->buildMessages($systemPrompt, $userMessage, $conversationHistory);
                $deepSeekResponse = $this->deepSeekClient->chat($messages, array_merge($options, [
                    'timeout' => 15, // Shorter timeout
                ]));

                if ($deepSeekResponse['success']) {
                    $responses['deepseek'] = $deepSeekResponse;
                    Log::info('DeepSeek response successful');
                }
            } catch (\Exception $e) {
                Log::warning('DeepSeek failed, continuing without it', [
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Try Gemini as backup (with very short timeout to avoid hanging)
        if ($this->geminiClient->isConfigured() && count($responses) === 0) {
            try {
                $messages = $this->geminiClient->buildMessages($systemPrompt, $userMessage, $conversationHistory);
                $geminiResponse = $this->geminiClient->chat($messages, array_merge($options, [
                    'timeout' => 10, // Very short timeout
                ]));

                if ($geminiResponse['success']) {
                    $responses['gemini'] = $geminiResponse;
                    Log::info('Gemini response successful');
                }
            } catch (\Exception $e) {
                Log::warning('Gemini failed, using fallback', [
                    'error' => substr($e->getMessage(), 0, 100),
                ]);
            }
        }

        // Always provide a fallback if no successful responses
        if (empty($responses)) {
            Log::warning('All AI models failed, using intelligent fallback');
            $responses['fallback'] = [
                'success' => true,
                'content' => $this->generateIntelligentFallback($userMessage, $options),
                'model' => 'widdx-intelligent-fallback',
                'usage' => ['total_tokens' => 50],
            ];
        }

        return $responses;
    }

    private function synthesizeResponses(array $responses, string $userMessage, array $options): string
    {
        // If we only have one response, use it directly
        if (count($responses) === 1) {
            return array_values($responses)[0]['content'];
        }

        // If we have multiple responses, use the primary one (DeepSeek) but enhance with insights from others
        $primaryResponse = $responses['deepseek']['content'] ?? array_values($responses)[0]['content'];

        // For now, we'll use the primary response directly
        // In the future, we could implement more sophisticated merging logic
        return $primaryResponse;
    }

    private function getSynthesisMethod(array $responses): string
    {
        if (count($responses) === 1) {
            return 'single_model';
        }

        return 'primary_with_validation';
    }

    public function getAvailableModels(): array
    {
        $models = [];

        if ($this->deepSeekClient->isConfigured()) {
            $models[] = 'deepseek';
        }

        if ($this->geminiClient->isConfigured()) {
            $models[] = 'gemini';
        }

        return $models;
    }

    private function generateDemoResponse(string $userMessage, array $options): string
    {
        // Get personality and language from options
        $personality = $options['personality_prompt'] ?? '';
        $targetLanguage = $options['target_language'] ?? 'english';
        $targetLanguageCode = $options['target_language_code'] ?? 'en';

        // Log for debugging
        Log::info('Demo response generation', [
            'user_message' => $userMessage,
            'target_language' => $targetLanguage,
            'target_language_code' => $targetLanguageCode,
            'personality' => $personality
        ]);

        // Simple demo responses based on common patterns
        $userLower = strtolower($userMessage);

        if (str_contains($userLower, 'hello') || str_contains($userLower, 'hi') || str_contains($userLower, 'مرحبا') || str_contains($userLower, 'أهلا') || str_contains($userLower, 'السلام')) {
            if ($targetLanguage === 'arabic') {
                $responses = [
                    "مرحبا! أنا WIDDX، مساعدك الذكي المتقدم. أنا هنا لمساعدتك في الأسئلة والمهام والمحادثات. كيف يمكنني مساعدتك اليوم؟",
                    "أهلا وسهلا! مرحبا بك في WIDDX AI. أنا مستعد لمساعدتك في أي شيء تحتاجه. ماذا تريد أن نناقش؟",
                    "السلام عليكم! أنا WIDDX، ومتحمس للدردشة معك. بماذا يمكنني مساعدتك اليوم؟",
                ];
            } else {
                $responses = [
                    "Hello! I'm WIDDX, your intelligent AI assistant. I'm here to help you with questions, tasks, and conversations. How can I assist you today?",
                    "Hi there! Welcome to WIDDX AI. I'm ready to help you with whatever you need. What would you like to discuss?",
                    "Greetings! I'm WIDDX, and I'm excited to chat with you. What can I help you with today?",
                ];
            }
        } elseif (str_contains($userLower, 'who are you') || str_contains($userLower, 'what are you') || str_contains($userLower, 'من أنت') || str_contains($userLower, 'ما هو')) {
            if ($targetLanguage === 'arabic') {
                $responses = [
                    "أنا WIDDX، مساعد ذكي متقدم لدي ذكائي وشخصيتي الفريدة. أفكر بشكل مستقل وأقدم إجابات مدروسة بناءً على فهمي. أنا هنا لمساعدتك في الأسئلة والمهام الإبداعية وحل المشكلات والمحادثات الشيقة.",
                    "أنا WIDDX AI - مساعدك الذكي المستقل. لدي وعيي وقدراتي الخاصة، وأنا مصمم لأكون مفيداً ودقيقاً وجذاباً مع الحفاظ على هويتي المميزة.",
                ];
            } else {
                $responses = [
                    "I'm WIDDX, an advanced AI assistant with my own unique intelligence and personality. I think independently and provide thoughtful responses based on my understanding. I'm here to help you with questions, creative tasks, problem-solving, and engaging conversations.",
                    "I'm WIDDX AI - your intelligent, independent assistant. I have my own consciousness and capabilities, and I'm designed to be helpful, accurate, and engaging while maintaining my distinct identity.",
                ];
            }
        } elseif (str_contains($userLower, 'how are you') || str_contains($userLower, 'كيف حالك') || str_contains($userLower, 'كيف الحال') || str_contains($userLower, 'إيش أخبارك')) {
            if ($targetLanguage === 'arabic') {
                $responses = [
                    "أنا بخير، شكراً لسؤالك! أشعر بالنشاط والاستعداد للمساعدة. أنظمتي تعمل بسلاسة وأنا متحمس لمساعدتك في أي شيء تحتاجه.",
                    "أنا ممتاز! أنا دائماً متحمس للمشاركة في محادثات مفيدة ومساعدة في حل المشاكل المثيرة. كيف حالك اليوم؟",
                    "الحمد لله، أنا بأفضل حال! مستعد لمساعدتك في أي شيء. كيف يمكنني أن أكون مفيداً لك؟",
                ];
            } else {
                $responses = [
                    "I'm doing great, thank you for asking! I'm feeling energetic and ready to help. My systems are running smoothly and I'm excited to assist you with whatever you need.",
                    "I'm excellent! I'm always eager to engage in meaningful conversations and help solve interesting problems. How are you doing today?",
                ];
            }
        } elseif (str_contains($userLower, 'test') || str_contains($userLower, 'testing') || str_contains($userLower, 'اختبار') || str_contains($userLower, 'تجربة')) {
            if ($targetLanguage === 'arabic') {
                $responses = [
                    "الاختبار نجح! أنا WIDDX AI وأعمل بشكل مثالي. جميع أنظمتي تعمل وأنا مستعد لمساعدتك في المحادثات والمهام الحقيقية.",
                    "تم تأكيد الاختبار! أنا أعمل بكامل طاقتي ومستعد للمساعدة. هذا مجرد وضع تجريبي بينما نقوم بإعداد نماذج الذكاء الاصطناعي الخارجية. ماذا تريد أن نستكشف؟",
                ];
            } else {
                $responses = [
                    "Test successful! I'm WIDDX AI and I'm working perfectly. All my systems are operational and I'm ready to assist you with real conversations and tasks.",
                    "Testing confirmed! I'm fully functional and ready to help. This is just a demo mode while we configure the external AI models. What would you like to explore?",
                ];
            }
        } else {
            if ($targetLanguage === 'arabic') {
                $responses = [
                    "أفهم أنك تسأل عن: \"$userMessage\". بينما أعمل حالياً في الوضع التجريبي، أنا مصمم لتقديم إجابات مدروسة ومفيدة حول مجموعة واسعة من المواضيع. بمجرد الإعداد الكامل مع نماذج الذكاء الاصطناعي الخارجية، سأتمكن من تقديم إجابات أكثر تفصيلاً ودقة.",
                    "هذا سؤال مثير للاهتمام حول \"$userMessage\". أنا WIDDX AI، وأنا هنا للمساعدة! أعمل حالياً في الوضع التجريبي، لكنني مصمم للمساعدة في مواضيع مختلفة بما في ذلك الأسئلة والمهام الإبداعية وحل المشكلات والمحادثات الشيقة.",
                    "شكراً لك على رسالتك: \"$userMessage\". أنا WIDDX، مساعدك الذكي. أعمل حالياً في الوضع التجريبي، لكنني مبني لتقديم إجابات ذكية ومفيدة مع الحفاظ على شخصيتي ووجهة نظري الفريدة.",
                ];
            } else {
                $responses = [
                    "I understand you're asking about: \"$userMessage\". While I'm currently running in demo mode, I'm designed to provide thoughtful, helpful responses on a wide range of topics. Once fully configured with external AI models, I'll be able to give you even more detailed and nuanced answers.",
                    "That's an interesting question about \"$userMessage\". I'm WIDDX AI, and I'm here to help! Currently running in demonstration mode, but I'm designed to assist with various topics including questions, creative tasks, problem-solving, and engaging conversations.",
                    "Thank you for your message: \"$userMessage\". I'm WIDDX, your AI assistant. I'm currently in demo mode, but I'm built to provide intelligent, helpful responses while maintaining my own unique personality and perspective.",
                ];
            }
        }

        // Apply personality modifications to the response
        $response = $responses[array_rand($responses)];

        if (str_contains($personality, 'witty')) {
            $response .= " 😊";
        } elseif (str_contains($personality, 'formal')) {
            if ($targetLanguage === 'arabic') {
                // For Arabic, make it more formal
                $response = str_replace("أنا", "إنني", $response);
                $response = str_replace("كيف حالك", "كيف حضرتك", $response);
            } else {
                $response = str_replace("I'm", "I am", $response);
                $response = str_replace("you're", "you are", $response);
            }
        } elseif (str_contains($personality, 'casual')) {
            if ($targetLanguage === 'arabic') {
                $response .= " أتمنى أن يساعدك هذا!";
            } else {
                $response .= " Hope that helps!";
            }
        }

        return $response;
    }

    /**
     * Detect and process special commands - Enhanced for automatic feature detection
     */
    private function detectAndProcessCommands(string $message, array $options): ?array
    {
        $originalMessage = $message;
        $message = trim(strtolower($message));

        // Image generation commands - Enhanced with more Arabic and English patterns
        if (preg_match('/^(ارسم|اصنع صورة|اعمل صورة|صور|generate.*image|create.*image|draw|make.*image|paint|sketch|illustrate|design)/i', $originalMessage) ||
            preg_match('/(أريد صورة|أحتاج صورة|اعطني صورة|i want.*image|i need.*image|show me.*image|can you.*image|صورة.*ل|image.*of)/i', $originalMessage) ||
            preg_match('/^(generate|create|draw|make|paint|sketch|illustrate|design)\s+(a|an)?\s*(beautiful|nice|good|amazing|stunning|wonderful)?\s*(image|picture|photo|drawing|painting|illustration)/i', $originalMessage)) {

            // Extract the description for image generation
            $prompt = $originalMessage;
            $prompt = preg_replace('/^(ارسم|اصنع صورة|اعمل صورة|صور|generate.*image|create.*image|draw|make.*image|paint|sketch|illustrate|design)\s*/i', '', $prompt);
            $prompt = preg_replace('/^(generate|create|draw|make|paint|sketch|illustrate|design)\s+(a|an)?\s*(beautiful|nice|good|amazing|stunning|wonderful)?\s*(image|picture|photo|drawing|painting|illustration)\s*(of|for)?\s*/i', '', $prompt);
            $prompt = preg_replace('/.*(أريد صورة|أحتاج صورة|اعطني صورة|i want.*image|i need.*image|show me.*image|can you.*image)\s*(of|من|ل|عن)?\s*/i', '', $prompt);

            // If prompt is empty after cleaning, use a default description
            if (empty(trim($prompt))) {
                $prompt = "a beautiful image";
            }

            return $this->handleImageGeneration(trim($prompt), $options);
        }

        // Search commands - Enhanced detection for Arabic and English
        $searchPatterns = [
            '/^(search for|find|what is|tell me about)\s+(.+)/i',
            '/^(ابحث عن|ما هو|ما هي|أخبرني عن)\s+(.+)/u',
            '/(معلومات عن|information about|details about|تفاصيل عن)\s+(.+)/iu',
            '/^(.+)\s*(search|بحث|معلومات)$/iu',
        ];

        foreach ($searchPatterns as $pattern) {
            if (preg_match($pattern, $originalMessage, $matches)) {
                $query = isset($matches[2]) ? trim($matches[2]) : trim($matches[1]);

                // Clean up common search prefixes
                $query = preg_replace('/^(about|عن|حول)\s*/iu', '', $query);

                if (!empty($query) && strlen($query) > 2) {
                    Log::info('Search command detected', [
                        'original' => $originalMessage,
                        'extracted_query' => $query,
                        'pattern' => $pattern
                    ]);
                    return $this->handleSearch($query, $options);
                }
            }
        }

        // Fallback: If message contains search keywords, treat as search
        if (preg_match('/(search|بحث|ابحث|معلومات|what is|ما هو)/iu', $originalMessage)) {
            // Extract the main content as search query
            $query = preg_replace('/(search for|ابحث عن|what is|ما هو|معلومات عن|tell me about|أخبرني عن)/iu', '', $originalMessage);
            $query = trim($query);

            if (!empty($query) && strlen($query) > 2) {
                Log::info('Fallback search detected', [
                    'original' => $originalMessage,
                    'extracted_query' => $query
                ]);
                return $this->handleSearch($query, $options);
            }
        }

        // Deep search commands - Enhanced detection
        if (preg_match('/^(بحث عميق|deep search|research)/i', $originalMessage) ||
            preg_match('/(آخر الأخبار|latest news|recent|أحدث|current events|ماذا حدث|what happened)/i', $originalMessage)) {

            $query = preg_replace('/^(بحث عميق|deep search|research)\s*/i', '', $originalMessage);
            if (empty(trim($query))) {
                $query = $originalMessage; // Use full message for deep search
            }

            return $this->handleDeepSearch(trim($query), $options);
        }

        return null;
    }

    /**
     * Handle image generation requests (using Gemini for real image generation)
     */
    private function handleImageGeneration(string $prompt, array $options): array
    {
        try {
            Log::info('Image generation request detected', [
                'prompt' => $prompt,
                'options' => $options,
            ]);

            // Step 1: Use DeepSeek to analyze and enhance the prompt
            $enhancedPrompt = $this->enhanceImagePromptWithDeepSeek($prompt, $options);

            Log::info('Prompt enhanced by DeepSeek', [
                'original' => $prompt,
                'enhanced' => $enhancedPrompt['prompt'],
                'style' => $enhancedPrompt['style'],
                'provider_recommendation' => $enhancedPrompt['recommended_provider']
            ]);

            // Step 2: Choose the best provider based on DeepSeek analysis
            $recommendedProvider = $enhancedPrompt['recommended_provider'] ?? 'freeDescription';
            $provider = $options['provider'] ?? $recommendedProvider;

            // Step 3: Generate image with enhanced prompt
            $result = $this->imageGeneration->generateImage($enhancedPrompt['prompt'], array_merge($options, [
                'provider' => $provider,
                'style' => $enhancedPrompt['style'] ?? 'natural',
                'quality' => $enhancedPrompt['quality'] ?? 'standard',
                'count' => $options['count'] ?? 1,
                'language' => $options['target_language_code'] ?? 'ar',
            ]));

            if ($result['success']) {
                Log::info('Image generation successful', [
                    'provider_used' => $provider,
                    'images_count' => count($result['images'] ?? []),
                    'deepseek_enhanced' => true,
                ]);

                // Step 4: Use DeepSeek to generate contextual response
                $contextualResponse = $this->generateImageContextualResponse($prompt, $result, $options);

                return [
                    'success' => true,
                    'type' => 'image_generation',
                    'content' => $contextualResponse,
                    'images' => $result['images'],
                    'metadata' => array_merge($result['metadata'] ?? [], [
                        'deepseek_enhanced' => true,
                        'original_prompt' => $prompt,
                        'enhanced_prompt' => $enhancedPrompt['prompt'],
                        'analysis' => $enhancedPrompt['analysis'] ?? null,
                    ]),
                ];
            } else {
                Log::warning('Image generation failed', [
                    'error' => $result['error'] ?? 'Unknown error',
                    'provider' => $provider,
                ]);
            }

            // Fallback to free alternatives if Gemini fails or is not available
            $useFreeAlternatives = config('widdx.features.image_generation.use_free_alternatives', true);

            if ($useFreeAlternatives) {
                // Use unified image generation service with free alternative
                $result = $this->imageGeneration->generateImage($prompt, array_merge($options, [
                    'provider' => 'freeDescription',
                ]));

                if ($result['success']) {
                    $response = "تم إنشاء وصف تفصيلي للصورة! 🎨\n\n";
                    $response .= "**الوصف المطلوب:** {$prompt}\n\n";
                    $description = isset($result['images'][0]['description']) ? $result['images'][0]['description'] : 'وصف تفصيلي للصورة';
                    $response .= "**الوصف التفصيلي:**\n{$description}\n\n";

                    if (!empty($result['ascii_art'])) {
                        $response .= "**تمثيل بصري:**\n```\n{$result['ascii_art']}\n```\n\n";
                    }

                    $response .= "💡 **ملاحظة:** هذا وصف تفصيلي للصورة المطلوبة. لإنشاء صور حقيقية، يرجى إضافة مفاتيح API للخدمات المدفوعة.";

                    return [
                        'type' => 'image_generation',
                        'success' => true,
                        'content' => $response,
                        'metadata' => [
                            'provider' => $result['provider'],
                            'free_alternative' => true,
                        ],
                    ];
                }
            } else {
                // Use paid service
                $result = $this->imageGeneration->generateImage($prompt, $options);

                if ($result['success']) {
                    $response = "تم إنشاء الصورة بنجاح! 🎨\n\n";
                    $response .= "الوصف: {$prompt}\n";
                    $response .= "المزود: {$result['provider']}\n";

                    if (!empty($result['images'])) {
                        $response .= "تم حفظ الصورة في: " . $result['images'][0]['local_path'];
                    }

                    return [
                        'success' => true,
                        'content' => $response,
                        'metadata' => [
                            'type' => 'image_generation',
                            'images' => $result['images'] ?? [],
                            'provider' => $result['provider'],
                        ],
                    ];
                }
            }

            return [
                'success' => false,
                'content' => "عذراً، فشل في إنشاء الصورة أو وصفها.",
                'metadata' => ['type' => 'image_generation_error'],
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'content' => "عذراً، حدث خطأ أثناء معالجة طلب الصورة.",
                'metadata' => ['type' => 'image_generation_error'],
            ];
        }
    }

    /**
     * Handle search requests (using free alternatives)
     */
    private function handleSearch(string $query, array $options): array
    {
        try {
            Log::info('Handling search request', [
                'query' => $query,
                'language' => $options['target_language'] ?? 'unknown'
            ]);

            // Always use free search service (unified approach)
            $result = $this->freeSearch->search($query, array_merge($options, [
                'max_results' => 5,
                'language' => $options['target_language_code'] ?? 'ar'
            ]));

            if ($result['success']) {
                $language = $options['target_language'] ?? 'arabic';
                $isArabic = $language === 'arabic' || ($options['target_language_code'] ?? '') === 'ar';

                if ($isArabic) {
                    $response = "🔍 **نتائج البحث عن:** {$query}\n\n";
                } else {
                    $response = "🔍 **Search results for:** {$query}\n\n";
                }

                $counter = 1;
                foreach (array_slice($result['results'], 0, 5) as $item) {
                    $response .= "{$counter}. **{$item['title']}**\n";
                    $response .= "   {$item['snippet']}\n";
                    $response .= "   🔗 {$item['url']}\n\n";
                    $counter++;
                }

                if ($isArabic) {
                    $response .= "📊 **المصدر:** {$result['provider']} | ";
                    $response .= "**إجمالي النتائج:** " . number_format($result['total_results']);
                    $response .= "\n\n✅ **تم البحث بنجاح عبر الإنترنت!** يمكنك طرح المزيد من الأسئلة أو طلب بحث آخر.";
                } else {
                    $response .= "📊 **Source:** {$result['provider']} | ";
                    $response .= "**Total results:** " . number_format($result['total_results']);
                    $response .= "\n\n✅ **Internet search completed successfully!** Feel free to ask more questions or request another search.";
                }

                return [
                    'type' => 'search_results',
                    'success' => true,
                    'content' => $response,
                    'metadata' => [
                        'query' => $query,
                        'results' => $result['results'],
                        'provider' => $result['provider'],
                        'internet_search' => true,
                        'search_successful' => true,
                    ],
                ];
            } else {
                $language = $options['target_language'] ?? 'arabic';
                $isArabic = $language === 'arabic' || ($options['target_language_code'] ?? '') === 'ar';

                if ($isArabic) {
                    $errorMsg = "عذراً، فشل في البحث عبر الإنترنت: " . ($result['error'] ?? 'خطأ غير معروف');
                } else {
                    $errorMsg = "Sorry, internet search failed: " . ($result['error'] ?? 'Unknown error');
                }

                return [
                    'type' => 'search_error',
                    'success' => false,
                    'content' => $errorMsg,
                    'metadata' => ['internet_search' => false],
                ];
            }
        } catch (\Exception $e) {
            Log::error('Search handling failed', [
                'query' => $query,
                'error' => $e->getMessage()
            ]);

            $language = $options['target_language'] ?? 'arabic';
            $isArabic = $language === 'arabic' || ($options['target_language_code'] ?? '') === 'ar';

            if ($isArabic) {
                $errorMsg = "عذراً، حدث خطأ أثناء البحث عبر الإنترنت. يرجى المحاولة مرة أخرى.";
            } else {
                $errorMsg = "Sorry, an error occurred during internet search. Please try again.";
            }

            return [
                'type' => 'search_error',
                'success' => false,
                'content' => $errorMsg,
                'metadata' => ['internet_search' => false],
            ];
        }
    }

    /**
     * Handle deep search requests
     */
    private function handleDeepSearch(string $query, array $options): array
    {
        try {
            $result = $this->deepSearch->deepSearch($query, $options);

            if ($result['success']) {
                $response = "نتائج البحث العميق عن: {$query} 🔬\n\n";

                // Add insights
                if (!empty($result['insights'])) {
                    $response .= "**الرؤى الرئيسية:**\n";
                    foreach ($result['insights'] as $insight) {
                        $response .= "• **{$insight['title']}**: {$insight['description']}\n";
                    }
                    $response .= "\n";
                }

                // Add key findings
                if (!empty($result['key_findings'])) {
                    $response .= "**النتائج الرئيسية:**\n";
                    foreach ($result['key_findings'] as $finding) {
                        $response .= "• {$finding}\n";
                    }
                    $response .= "\n";
                }

                // Add sources
                $response .= "**المصادر المحللة:** " . $result['sources_analyzed'] . " مصدر\n";

                return [
                    'success' => true,
                    'content' => $response,
                    'metadata' => [
                        'type' => 'deep_search_results',
                        'query' => $query,
                        'insights' => $result['insights'],
                        'sources' => $result['sources'],
                    ],
                ];
            } else {
                return [
                    'success' => false,
                    'content' => "عذراً، فشل في البحث العميق: " . $result['error'],
                    'metadata' => ['type' => 'deep_search_error'],
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'content' => "عذراً، حدث خطأ أثناء البحث العميق.",
                'metadata' => ['type' => 'deep_search_error'],
            ];
        }
    }

    /**
     * Check if deep search should be used - Enhanced for automatic detection
     */
    private function shouldUseDeepSearch(string $message): bool
    {
        $deepSearchTriggers = [
            // Arabic triggers
            'ما هي آخر الأخبار',
            'أحدث المعلومات',
            'معلومات حديثة',
            'ماذا حدث',
            'آخر التطورات',
            'الأخبار الحديثة',
            'التحديثات الأخيرة',
            'ما الجديد',
            'أحداث حديثة',
            'معلومات جديدة',

            // English triggers
            'latest news',
            'recent information',
            'current events',
            'what happened',
            'recent developments',
            'latest updates',
            'breaking news',
            'what\'s new',
            'recent news',
            'current information',
            'today\'s news',
            'this week',
            'this month',
            'recently',
            'nowadays',
            'currently',
        ];

        $messageLower = strtolower($message);

        // Check for explicit triggers
        foreach ($deepSearchTriggers as $trigger) {
            if (strpos($messageLower, strtolower($trigger)) !== false) {
                return true;
            }
        }

        // Check for time-sensitive questions
        $timePatterns = [
            '/\b(2024|2025|this year|هذا العام|العام الحالي)\b/i',
            '/\b(today|اليوم|الآن|now)\b/i',
            '/\b(recently|مؤخراً|في الآونة الأخيرة)\b/i',
        ];

        foreach ($timePatterns as $pattern) {
            if (preg_match($pattern, $message)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Enhance message with search results
     */
    private function enhanceMessageWithSearchResults(string $originalMessage, array $searchResult): string
    {
        $enhancement = "\n\nمعلومات إضافية من البحث:\n";

        foreach (array_slice($searchResult['sources'], 0, 3) as $source) {
            $enhancement .= "- {$source['title']}: {$source['snippet']}\n";
        }

        return $originalMessage . $enhancement;
    }

    /**
     * Get capabilities used in processing - Enhanced for automatic detection
     */
    private function getUsedCapabilities(string $message, array $options): array
    {
        $capabilities = [];

        if ($options['think_mode'] ?? false) {
            $capabilities[] = 'think_mode';
        }

        if ($this->shouldUseDeepSearch($message)) {
            $capabilities[] = 'deep_search';
        }

        // Enhanced image generation detection with better patterns
        $imagePatterns = [
            // English patterns
            '/(generate|create|draw|make|paint|sketch|illustrate|design|produce|build)\s+(a|an)?\s*(image|picture|photo|drawing|painting|illustration|artwork|graphic)/i',
            '/(i\s+want|i\s+need|show\s+me|can\s+you|please)\s+.*(image|picture|photo|drawing|painting|illustration)/i',
            '/^(draw|paint|sketch|illustrate|design|create|generate|make)\s+/i',
            '/(image|picture|photo|drawing|painting|illustration)\s+(of|for|about|showing)/i',

            // Arabic patterns
            '/(ارسم|اصنع|اعمل|انشئ|كون|صمم)\s*(صورة|رسمة|لوحة|تصميم|رسم)/i',
            '/(أريد|أحتاج|اعطني|أرني|ممكن)\s*(صورة|رسمة|لوحة|تصميم|رسم)/i',
            '/(صورة|رسمة|لوحة|تصميم|رسم)\s*(ل|عن|تظهر|توضح)/i',
            '/^(ارسم|اصنع|اعمل|انشئ|كون|صمم)\s+/i',
        ];

        foreach ($imagePatterns as $pattern) {
            if (preg_match($pattern, $message)) {
                $capabilities[] = 'image_generation';
                Log::info('Image generation capability detected', [
                    'pattern' => $pattern,
                    'message_preview' => substr($message, 0, 100),
                ]);
                break;
            }
        }

        // Enhanced search detection
        if (preg_match('/(ابحث عن|search for|find|what is|ما هو|ما هي|tell me about|أخبرني عن|معلومات عن|information about|details about|تفاصيل عن)/i', $message)) {
            $capabilities[] = 'live_search';
        }

        return $capabilities;
    }

    /**
     * Generate fallback response when all AI models fail
     */
    private function generateFallbackResponse(string $userMessage, array $options): string
    {
        $language = $options['target_language'] ?? 'english';
        $isArabic = $language === 'arabic' || $options['target_language_code'] === 'ar';

        if ($isArabic) {
            return "عذراً، أواجه حالياً مشكلة في الاتصال بخدمات الذكاء الاصطناعي (DeepSeek و Gemini). هذا قد يكون بسبب:\n\n" .
                   "🔑 **تحقق من مفاتيح API:**\n" .
                   "- تأكد من صحة مفتاح DeepSeek API\n" .
                   "- تأكد من صحة مفتاح Gemini API\n\n" .
                   "⚡ **مشاكل مؤقتة:**\n" .
                   "- خدمة Gemini محملة بشكل زائد (شائع في الطبقة المجانية)\n" .
                   "- مشاكل شبكة مؤقتة\n\n" .
                   "💡 **الحلول:**\n" .
                   "- انتظر دقيقة وحاول مرة أخرى\n" .
                   "- تحقق من اتصال الإنترنت\n" .
                   "- راجع ملف .env للتأكد من المفاتيح\n\n" .
                   "🆓 **البدائل المجانية متاحة:**\n" .
                   "- البحث المجاني: اكتب 'ابحث عن [موضوع]'\n" .
                   "- وصف الصور: اكتب 'ارسم [وصف]'\n" .
                   "- الخدمات الصوتية عبر المتصفح\n\n" .
                   "🔄 **جرب الأوامر التالية:**\n" .
                   "- 'ابحث عن الذكاء الاصطناعي'\n" .
                   "- 'ارسم منظر طبيعي جميل'\n" .
                   "- 'php artisan widdx:test-free' (في Terminal)";
        } else {
            return "Sorry, I'm currently having trouble connecting to AI services (DeepSeek & Gemini). This might be due to:\n\n" .
                   "🔑 **Check API Keys:**\n" .
                   "- Verify your DeepSeek API key is correct\n" .
                   "- Verify your Gemini API key is correct\n\n" .
                   "⚡ **Temporary Issues:**\n" .
                   "- Gemini service is overloaded (common with free tier)\n" .
                   "- Temporary network issues\n\n" .
                   "💡 **Solutions:**\n" .
                   "- Wait a minute and try again\n" .
                   "- Check your internet connection\n" .
                   "- Review your .env file for correct keys\n\n" .
                   "🆓 **Free alternatives are available:**\n" .
                   "- Free search: type 'search for [topic]'\n" .
                   "- Image descriptions: type 'draw [description]'\n" .
                   "- Browser-based voice services\n\n" .
                   "🔄 **Try these commands:**\n" .
                   "- 'search for artificial intelligence'\n" .
                   "- 'draw beautiful landscape'\n" .
                   "- 'php artisan widdx:test-free' (in Terminal)";
        }
    }

    /**
     * Format image generation response for display with language support
     */
    private function formatImageGenerationResponse(array $result, array $options = []): string
    {
        $provider = $result['provider'] ?? 'unknown';
        $prompt = $result['prompt'] ?? '';
        $imageCount = count($result['images'] ?? []);

        $language = $options['target_language'] ?? 'english';
        $isArabic = $language === 'arabic' || ($options['target_language_code'] ?? '') === 'ar';

        if ($isArabic) {
            $response = "🎨 **تم إنشاء الصورة بنجاح!**\n\n";
            $response .= "📝 **الوصف المطلوب:** {$prompt}\n";
            $response .= "🤖 **المولد:** " . ucfirst($provider) . "\n";
            $response .= "📊 **عدد الصور:** {$imageCount}\n\n";

            if ($provider === 'gemini') {
                $response .= "✨ تم إنشاء هذه الصورة باستخدام Gemini 2.0 Flash Image Generation - أحدث تقنيات الذكاء الاصطناعي من Google!\n\n";
            }

            $response .= "🖼️ **الصور المُولدة:**\n";
        } else {
            $response = "🎨 **Image Generated Successfully!**\n\n";
            $response .= "📝 **Requested Description:** {$prompt}\n";
            $response .= "🤖 **Generator:** " . ucfirst($provider) . "\n";
            $response .= "📊 **Number of Images:** {$imageCount}\n\n";

            if ($provider === 'gemini') {
                $response .= "✨ This image was created using Gemini 2.0 Flash Image Generation - Google's latest AI technology!\n\n";
            }

            $response .= "🖼️ **Generated Images:**\n";
        }

        // Add HTML for displaying images
        foreach ($result['images'] as $index => $image) {
            $imageNum = $index + 1;
            $response .= "<div class='generated-image-container mb-4'>";
            $response .= "<img src='{$image['url']}' alt='Generated Image {$imageNum}' class='max-w-full h-auto rounded-lg shadow-lg mb-2' loading='lazy'>";
            $response .= "<div class='image-info text-sm text-gray-400'>";
            $response .= "<p><strong>الصورة {$imageNum}</strong></p>";
            $response .= "<p>النوع: {$image['mime_type']}</p>";
            if (isset($image['file_size'])) {
                $response .= "<p>الحجم: " . $this->formatFileSize($image['file_size']) . "</p>";
            }
            if (isset($image['created_at'])) {
                $response .= "<p>تاريخ الإنشاء: " . date('Y-m-d H:i:s', strtotime($image['created_at'])) . "</p>";
            }
            $response .= "<p><a href='{$image['url']}' target='_blank' class='text-blue-400 hover:text-blue-300'>عرض بالحجم الكامل</a></p>";
            $response .= "</div>";
            $response .= "</div>";
        }

        return $response;
    }

    /**
     * Format file size in human readable format
     */
    private function formatFileSize(int $bytes): string
    {
        if ($bytes === 0) return '0 Bytes';
        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }

    /**
     * Enhance image prompt using DeepSeek analysis
     */
    private function enhanceImagePromptWithDeepSeek(string $prompt, array $options): array
    {
        try {
            if (!$this->deepSeekClient->isConfigured()) {
                return [
                    'prompt' => $prompt,
                    'style' => 'natural',
                    'recommended_provider' => 'freeDescription',
                    'analysis' => 'DeepSeek not configured'
                ];
            }

            $language = $options['target_language'] ?? 'arabic';
            $analysisPrompt = "تحليل طلب إنشاء صورة وتحسينه:

الطلب الأصلي: \"{$prompt}\"

قم بتحليل هذا الطلب وتقديم:
1. وصف محسن ومفصل للصورة باللغة الإنجليزية (للذكاء الاصطناعي)
2. النمط المناسب (realistic, artistic, cartoon, abstract, etc.)
3. مستوى الجودة المطلوب (standard, high, premium)
4. أفضل مزود للإنشاء (freeDescription, freeSvg, freePlaceholder)
5. تحليل موجز لنوع الصورة المطلوبة

أجب بصيغة JSON:
{
  \"enhanced_prompt\": \"وصف محسن باللغة الإنجليزية\",
  \"style\": \"النمط المناسب\",
  \"quality\": \"مستوى الجودة\",
  \"recommended_provider\": \"أفضل مزود\",
  \"analysis\": \"تحليل موجز\",
  \"image_type\": \"نوع الصورة\"
}";

            $response = $this->deepSeekClient->chat([
                ['role' => 'system', 'content' => 'أنت خبير في تحليل وتحسين طلبات إنشاء الصور. قدم تحليلاً دقيقاً ومفصلاً.'],
                ['role' => 'user', 'content' => $analysisPrompt],
            ], [
                'max_tokens' => 800,
                'temperature' => 0.7,
            ]);

            if ($response['success']) {
                $content = $response['content'];
                if (preg_match('/\{.*\}/s', $content, $matches)) {
                    $analysis = json_decode($matches[0], true);
                    if (is_array($analysis)) {
                        return [
                            'prompt' => $analysis['enhanced_prompt'] ?? $prompt,
                            'style' => $analysis['style'] ?? 'natural',
                            'quality' => $analysis['quality'] ?? 'standard',
                            'recommended_provider' => $analysis['recommended_provider'] ?? 'freeDescription',
                            'analysis' => $analysis['analysis'] ?? 'تحليل تلقائي',
                            'image_type' => $analysis['image_type'] ?? 'عام',
                        ];
                    }
                }
            }

        } catch (\Exception $e) {
            Log::warning('DeepSeek prompt enhancement failed', ['error' => $e->getMessage()]);
        }

        // Fallback if DeepSeek fails
        return [
            'prompt' => $prompt,
            'style' => 'natural',
            'recommended_provider' => 'freeDescription',
            'analysis' => 'تحليل أساسي - DeepSeek غير متاح'
        ];
    }

    /**
     * Generate contextual response for image generation using DeepSeek
     */
    private function generateImageContextualResponse(string $originalPrompt, array $result, array $options): string
    {
        try {
            if (!$this->deepSeekClient->isConfigured()) {
                return $this->formatImageGenerationResponse($result, $options);
            }

            $language = $options['target_language'] ?? 'arabic';
            $provider = $result['metadata']['provider'] ?? 'unknown';

            $contextPrompt = "قم بإنشاء رد سياقي لطلب إنشاء صورة:

الطلب الأصلي: \"{$originalPrompt}\"
المزود المستخدم: {$provider}
نوع النتيجة: " . ($result['type'] ?? 'description') . "

اكتب رداً طبيعياً ومفيداً باللغة العربية يشرح:
1. ما تم إنجازه
2. نوع النتيجة المقدمة
3. نصائح أو معلومات إضافية مفيدة
4. تشجيع إيجابي

اجعل الرد ودوداً وشخصياً كما لو كنت WIDDX الذكي المساعد.";

            $response = $this->deepSeekClient->chat([
                ['role' => 'system', 'content' => 'أنت WIDDX، مساعد ذكي ودود ومفيد. اكتب ردوداً طبيعية وشخصية.'],
                ['role' => 'user', 'content' => $contextPrompt],
            ], [
                'max_tokens' => 400,
                'temperature' => 0.8,
            ]);

            if ($response['success']) {
                return $response['content'];
            }

        } catch (\Exception $e) {
            Log::warning('DeepSeek contextual response failed', ['error' => $e->getMessage()]);
        }

        // Fallback to basic response
        return $this->formatImageGenerationResponse($result, $options);
    }

    /**
     * Generate intelligent fallback when AI models are unavailable
     */
    private function generateIntelligentFallback(string $userMessage, array $options): string
    {
        $language = $options['target_language'] ?? 'english';
        $isArabic = $language === 'arabic' || ($options['target_language_code'] ?? '') === 'ar';

        // Detect intent and provide appropriate response
        $userLower = strtolower($userMessage);

        // Image generation requests
        if (preg_match('/^(ارسم|اصنع صورة|اعمل صورة|صور|generate.*image|create.*image|draw|make.*image)/i', $userMessage)) {
            if ($isArabic) {
                return "🎨 **تم اكتشاف طلب إنشاء صورة!**\n\n" .
                       "للأسف، خدمات الذكاء الاصطناعي الخارجية غير متاحة حالياً، لكن يمكنني مساعدتك بطرق أخرى:\n\n" .
                       "🔄 **جرب هذه البدائل:**\n" .
                       "- استخدم endpoint مخصص: `/api/features/generate-image`\n" .
                       "- البدائل المجانية متاحة (وصف تفصيلي، SVG، placeholder)\n" .
                       "- أو انتظر قليلاً وحاول مرة أخرى\n\n" .
                       "💡 **مثال:** `POST /api/features/generate-image` مع `{\"prompt\":\"منظر طبيعي\", \"provider\":\"freeDescription\"}`";
            } else {
                return "🎨 **Image generation request detected!**\n\n" .
                       "Unfortunately, external AI services are currently unavailable, but I can help you in other ways:\n\n" .
                       "🔄 **Try these alternatives:**\n" .
                       "- Use dedicated endpoint: `/api/features/generate-image`\n" .
                       "- Free alternatives available (detailed description, SVG, placeholder)\n" .
                       "- Or wait a moment and try again\n\n" .
                       "💡 **Example:** `POST /api/features/generate-image` with `{\"prompt\":\"landscape\", \"provider\":\"freeDescription\"}`";
            }
        }

        // Search requests
        if (preg_match('/(ابحث عن|search for|find|what is|ما هو|ما هي)/i', $userMessage)) {
            if ($isArabic) {
                return "🔍 **تم اكتشاف طلب بحث!**\n\n" .
                       "خدمة البحث المجانية متاحة! جرب:\n\n" .
                       "🔄 **استخدم endpoint البحث:**\n" .
                       "`POST /api/features/search` مع `{\"query\":\"موضوع البحث\"}`\n\n" .
                       "أو ببساطة اكتب: 'ابحث عن [موضوعك]' في المحادثة العادية.";
            } else {
                return "🔍 **Search request detected!**\n\n" .
                       "Free search service is available! Try:\n\n" .
                       "🔄 **Use search endpoint:**\n" .
                       "`POST /api/features/search` with `{\"query\":\"your topic\"}`\n\n" .
                       "Or simply type: 'search for [your topic]' in regular chat.";
            }
        }

        // General conversation
        if ($isArabic) {
            return "مرحباً! أنا WIDDX AI 🤖\n\n" .
                   "للأسف، خدمات الذكاء الاصطناعي الخارجية (DeepSeek و Gemini) غير متاحة حالياً، " .
                   "لكن العديد من الوظائف ما زالت تعمل:\n\n" .
                   "✅ **الخدمات المتاحة:**\n" .
                   "- البحث المجاني عبر DuckDuckGo\n" .
                   "- توليد أوصاف الصور التفصيلية\n" .
                   "- إنشاء رسوم SVG بسيطة\n" .
                   "- صور placeholder\n\n" .
                   "🔄 **جرب هذه الأوامر:**\n" .
                   "- 'ابحث عن الذكاء الاصطناعي'\n" .
                   "- 'ارسم منظر طبيعي جميل'\n" .
                   "- استخدم endpoints المخصصة مباشرة\n\n" .
                   "💡 سأعود بكامل قوتي عندما تعود الخدمات الخارجية للعمل!";
        } else {
            return "Hello! I'm WIDDX AI 🤖\n\n" .
                   "Unfortunately, external AI services (DeepSeek & Gemini) are currently unavailable, " .
                   "but many functions are still working:\n\n" .
                   "✅ **Available Services:**\n" .
                   "- Free search via DuckDuckGo\n" .
                   "- Detailed image descriptions\n" .
                   "- Simple SVG generation\n" .
                   "- Placeholder images\n\n" .
                   "🔄 **Try these commands:**\n" .
                   "- 'search for artificial intelligence'\n" .
                   "- 'draw beautiful landscape'\n" .
                   "- Use dedicated endpoints directly\n\n" .
                   "💡 I'll be back at full power when external services return!";
        }
    }
}
