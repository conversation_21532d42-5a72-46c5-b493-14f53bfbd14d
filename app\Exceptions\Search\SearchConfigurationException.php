<?php

namespace App\Exceptions\Search;

class SearchConfigurationException extends SearchException
{
    private string $configurationKey;
    private mixed $configurationValue;

    public function __construct(
        string $message = '',
        int $code = 0,
        ?\Exception $previous = null,
        string $searchQuery = '',
        string $provider = '',
        array $context = [],
        string $configurationKey = '',
        mixed $configurationValue = null
    ) {
        parent::__construct($message, $code, $previous, $searchQuery, $provider, $context);
        $this->configurationKey = $configurationKey;
        $this->configurationValue = $configurationValue;
    }

    /**
     * Get the configuration key that caused the issue
     */
    public function getConfigurationKey(): string
    {
        return $this->configurationKey;
    }

    /**
     * Get the configuration value that caused the issue
     */
    public function getConfigurationValue(): mixed
    {
        return $this->configurationValue;
    }

    /**
     * Create exception for missing configuration
     */
    public static function missingConfiguration(
        string $configKey,
        string $provider = '',
        array $context = []
    ): self {
        return new self(
            message: "Missing required configuration: {$configKey}",
            code: 4001,
            provider: $provider,
            context: $context,
            configurationKey: $configKey
        );
    }

    /**
     * Create exception for invalid configuration
     */
    public static function invalidConfiguration(
        string $configKey,
        mixed $configValue,
        string $expectedType = '',
        string $provider = '',
        array $context = []
    ): self {
        $message = "Invalid configuration for '{$configKey}'";
        if ($expectedType) {
            $message .= ". Expected {$expectedType}";
        }

        return new self(
            message: $message,
            code: 4002,
            provider: $provider,
            context: $context,
            configurationKey: $configKey,
            configurationValue: $configValue
        );
    }

    /**
     * Create exception for service configuration errors
     */
    public static function serviceConfiguration(
        string $serviceName,
        string $issue,
        array $context = []
    ): self {
        return new self(
            message: "Service configuration error for '{$serviceName}': {$issue}",
            code: 4003,
            provider: $serviceName,
            context: $context
        );
    }

    /**
     * Convert exception to array with configuration information
     */
    public function toArray(): array
    {
        $array = parent::toArray();
        $array['configurationKey'] = $this->configurationKey;
        $array['configurationValue'] = $this->configurationValue;
        return $array;
    }
}