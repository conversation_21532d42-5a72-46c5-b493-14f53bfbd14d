<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'WIDDX AI')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind (CDN for now) -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'widdx': {
                            'primary': '#1d4ed8',
                            'primary-hover': '#1e40af',
                            'secondary': '#7c3aed',
                        },
                        'widdx-bg': {
                            'primary': '#0a0a0a',
                            'secondary': '#111111',
                        },
                        'widdx-text': {
                            'primary': '#ffffff',
                            'secondary': '#a1a1aa',
                        },
                    }
                }
            }
        }
    </script>

    @stack('styles')
</head>
<body class="bg-widdx-bg-primary text-widdx-text-primary font-['Inter','sans-serif'] min-h-screen flex flex-col">
    @yield('content')

    @stack('scripts')

    <!-- Global Theme Toggle Logic (shared across pages) -->
    <script>
        (function() {
            const htmlEl = document.documentElement;
            const storedTheme = localStorage.getItem('widdx-theme') || 'dark';
            if (storedTheme === 'dark') {
                htmlEl.classList.add('dark');
            } else {
                htmlEl.classList.remove('dark');
            }
        })();
    </script>
</body>
</html>
