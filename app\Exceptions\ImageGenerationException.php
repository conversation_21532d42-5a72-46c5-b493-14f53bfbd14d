<?php

namespace App\Exceptions;

use Exception;

class ImageGenerationException extends Exception
{
    protected $errorCode;
    protected $arabicMessage;
    
    public function __construct(string $message, string $arabicMessage = '', int $errorCode = 0, Exception $previous = null)
    {
        parent::__construct($message, $errorCode, $previous);
        $this->arabicMessage = $arabicMessage ?: $this->getDefaultArabicMessage($message);
        $this->errorCode = $errorCode;
    }
    
    public function getArabicMessage(): string
    {
        return $this->arabicMessage;
    }
    
    public function getErrorCode(): int
    {
        return $this->errorCode;
    }
    
    private function getDefaultArabicMessage(string $englishMessage): string
    {
        $translations = [
            'Gemini API key not configured' => 'مفتاح Gemini API غير مُعدّ',
            'Empty image data received' => 'تم استلام بيانات صورة فارغة',
            'Failed to save image to storage' => 'فشل في حفظ الصورة في التخزين',
            'Image file was not saved properly' => 'لم يتم حفظ ملف الصورة بشكل صحيح',
            'No images generated by Gemini' => 'لم يتم إنشاء أي صور بواسطة Gemini',
            'Failed to decode base64 image data' => 'فشل في فك تشفير بيانات الصورة',
            'Gemini image generation failed' => 'فشل في توليد الصورة بواسطة Gemini',
            'Invalid image prompt' => 'وصف الصورة غير صالح',
            'Image generation timeout' => 'انتهت مهلة توليد الصورة',
            'Insufficient storage space' => 'مساحة التخزين غير كافية',
            'Invalid image parameters' => 'معاملات الصورة غير صالحة',
        ];
        
        return $translations[$englishMessage] ?? 'حدث خطأ في توليد الصورة';
    }
    
    public static function apiKeyNotConfigured(): self
    {
        return new self(
            'Gemini API key not configured',
            'مفتاح Gemini API غير مُعدّ. يرجى التحقق من إعدادات النظام.',
            1001
        );
    }
    
    public static function emptyImageData(): self
    {
        return new self(
            'Empty image data received',
            'تم استلام بيانات صورة فارغة من الخادم.',
            1002
        );
    }
    
    public static function storageFailed(): self
    {
        return new self(
            'Failed to save image to storage',
            'فشل في حفظ الصورة. يرجى المحاولة مرة أخرى.',
            1003
        );
    }
    
    public static function noImagesGenerated(): self
    {
        return new self(
            'No images generated by Gemini',
            'لم يتم إنشاء أي صور. يرجى تجربة وصف مختلف للصورة.',
            1004
        );
    }
    
    public static function invalidPrompt(): self
    {
        return new self(
            'Invalid image prompt',
            'وصف الصورة غير صالح. يرجى إدخال وصف واضح ومفصل.',
            1005
        );
    }
    
    public static function timeout(): self
    {
        return new self(
            'Image generation timeout',
            'انتهت مهلة توليد الصورة. يرجى المحاولة مرة أخرى.',
            1006
        );
    }
    
    public static function apiError(string $apiMessage): self
    {
        return new self(
            'Gemini API error: ' . $apiMessage,
            'خطأ في خدمة توليد الصور: ' . $apiMessage,
            1007
        );
    }
}
