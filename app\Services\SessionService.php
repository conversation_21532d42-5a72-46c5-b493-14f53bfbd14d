<?php

namespace App\Services;

use App\Models\ChatSession;
use App\Models\Message;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class SessionService
{
    /**
     * الحصول على جلسة موجودة أو إنشاء جلسة جديدة
     *
     * @param string|null $sessionId
     * @param string $personalityType
     * @return ChatSession
     */
    public function getOrCreateSession(string $sessionId = null, string $personalityType = 'neutral'): ChatSession
    {
        // استخدام ذاكرة التخزين المؤقت لتسريع الوصول للجلسة
        $cacheKey = $sessionId ? 'chat_session_' . $sessionId : null;
        
        if ($cacheKey && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        if ($sessionId) {
            try {
                $session = ChatSession::with('user')
                    ->where('session_id', $sessionId)
                    ->firstOrFail();
                
                // تحديث وقت النشاط الأخير
                $session->update(['last_activity' => now()]);
                
                // تخزين مؤقت
                if ($cacheKey) {
                    Cache::put($cacheKey, $session, now()->addHours(1));
                }
                
                return $session;
            } catch (ModelNotFoundException $e) {
                // متابعة لإنشاء جلسة جديدة
            }
        }

        // إنشاء جلسة جديدة
        $userId = Auth::check() ? Auth::id() : null;
        
        $session = ChatSession::create([
            'session_id' => $sessionId ?: (string) Str::uuid(),
            'user_id' => $userId,
            'user_identifier' => $this->getUserIdentifier(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'personality_type' => $personalityType,
            'last_activity' => now(),
            'context_data' => [
                'created_at' => now()->toDateTimeString(),
                'user_platform' => $this->getUserPlatform(),
                'user_browser' => $this->getUserBrowser(),
            ],
        ]);

        // تخزين مؤقت للجلسة الجديدة
        if ($cacheKey) {
            Cache::put($cacheKey, $session, now()->addHours(1));
        }

        return $session;
    }

    /**
     * إضافة رسالة جديدة إلى الجلسة
     *
     * @param ChatSession $session
     * @param string $role
     * @param string $content
     * @param array $metadata
     * @return Message
     */
    public function addMessage(ChatSession $session, string $role, string $content, array $metadata = []): Message
    {
        // تنظيف المحتوى من أي أكواد ضارة
        $cleanedContent = $this->sanitizeContent($content);
        
        $messageData = [
            'chat_session_id' => $session->id,
            'user_id' => Auth::check() ? Auth::id() : null,
            'role' => $role,
            'content' => $cleanedContent,
            'metadata' => $this->sanitizeMetadata($metadata),
            'personality_applied' => $session->personality_type,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'content_length' => mb_strlen($cleanedContent, 'UTF-8'),
            'word_count' => str_word_count(strip_tags($cleanedContent)),
        ];

        // إضافة معلومات اللغة إذا كانت متوفرة في البيانات الوصفية
        if (isset($metadata['detected_language'])) {
            $messageData = array_merge($messageData, [
                'detected_language' => $metadata['detected_language'],
                'detected_language_code' => $metadata['detected_language_code'] ?? null,
                'language_confidence' => $metadata['language_confidence'] ?? null,
                'language_detection_method' => $metadata['language_detection_method'] ?? null,
                'language_override_used' => $metadata['language_override_used'] ?? false,
            ]);
        }

        $message = Message::create($messageData);

        // Update session last activity
        $session->update(['last_activity' => now()]);

        return $message;
    }

    public function updateLanguagePreferences(ChatSession $session, array $languageInfo): void
    {
        // Update session language preferences based on detected language
        $currentLanguageHistory = $session->language_history ?? [];

        // Track language usage
        $language = $languageInfo['language'];
        $languageCode = $languageInfo['language_code'];

        if (!isset($currentLanguageHistory[$language])) {
            $currentLanguageHistory[$language] = [
                'count' => 0,
                'first_used' => now()->toISOString(),
                'confidence_scores' => [],
            ];
        }

        $currentLanguageHistory[$language]['count']++;
        $currentLanguageHistory[$language]['last_used'] = now()->toISOString();
        $currentLanguageHistory[$language]['confidence_scores'][] = $languageInfo['confidence'];

        // Update preferred language if this language is used frequently or with high confidence
        $shouldUpdatePreferred = false;

        if ($languageInfo['method'] === 'override') {
            // User explicitly requested this language
            $shouldUpdatePreferred = true;
        } elseif ($languageInfo['confidence'] > 0.7 && $currentLanguageHistory[$language]['count'] >= 2) {
            // High confidence and used multiple times
            $shouldUpdatePreferred = true;
        } elseif ($currentLanguageHistory[$language]['count'] >= 5) {
            // Used frequently
            $shouldUpdatePreferred = true;
        }

        $updateData = ['language_history' => $currentLanguageHistory];

        if ($shouldUpdatePreferred) {
            $updateData['preferred_language'] = $language;
            $updateData['preferred_language_code'] = $languageCode;
        }

        $session->update($updateData);
    }

    public function getConversationHistory(ChatSession $session, int $limit = 10): array
    {
        return $session->getRecentMessages($limit)
            ->map(function ($message) {
                return [
                    'role' => $message->role,
                    'content' => $message->content,
                    'created_at' => $message->created_at,
                ];
            })
            ->toArray();
    }

    public function updateSessionPersonality(ChatSession $session, string $personalityType): void
    {
        $session->update([
            'personality_type' => $personalityType,
            'last_activity' => now(),
        ]);
    }

    public function updateSessionContext(ChatSession $session, array $contextData): void
    {
        $currentContext = $session->context_data ?? [];
        $mergedContext = array_merge($currentContext, $contextData);

        $session->update([
            'context_data' => $mergedContext,
            'last_activity' => now(),
        ]);
    }

    public function cleanupOldSessions(int $daysOld = 30): int
    {
        $cutoffDate = now()->subDays($daysOld);

        return ChatSession::where('last_activity', '<', $cutoffDate)->delete();
    }

    /**
     * الحصول على معرف فريد للمستخدم بناءً على عنوان IP والمتصفح
     *
     * @return string
     */
    protected function getUserIdentifier(): string
    {
        $ip = Request::ip();
        $userAgent = Request::userAgent() ?: 'unknown';
        
        // إضافة ملح (salt) عشوائي لزيادة الأمان
        $salt = config('app.key');
        
        return hash('sha256', $ip . $userAgent . $salt);
    }
    
    /**
     * تنظيف محتوى الرسالة من أي أكواد ضارة
     *
     * @param string $content
     * @return string
     */
    protected function sanitizeContent(string $content): string
    {
        // إزالة أي أكواد HTML وJavaScript الضارة
        $cleaned = strip_tags($content);
        
        // إزالة محاولات حقن SQL
        $cleaned = preg_replace(['/\b(?:select|insert|update|delete|drop|create|alter)\b/i'], '', $cleaned);
        
        // تقليل المسافات البيضاء المتعددة
        $cleaned = preg_replace('/\s+/', ' ', $cleaned);
        
        return trim($cleaned);
    }
    
    /**
     * تنظيف البيانات الوصفية للرسالة
     *
     * @param array $metadata
     * @return array
     */
    protected function sanitizeMetadata(array $metadata): array
    {
        $sanitized = [];
        
        foreach ($metadata as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = $this->sanitizeMetadata($value);
            } elseif (is_string($value)) {
                $sanitized[$key] = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
            } else {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * الحصول على نظام تشغيل المستخدم
     *
     * @return string
     */
    protected function getUserPlatform(): string
    {
        $userAgent = Request::userAgent() ?: '';
        
        if (preg_match('/windows|win32/i', $userAgent)) {
            return 'Windows';
        } elseif (preg_match('/macintosh|mac os x/i', $userAgent)) {
            return 'Mac OS';
        } elseif (preg_match('/linux/i', $userAgent)) {
            return 'Linux';
        } elseif (preg_match('/android/i', $userAgent)) {
            return 'Android';
        } elseif (preg_match('/(iphone|ipad|ipod)/i', $userAgent)) {
            return 'iOS';
        }
        
        return 'Unknown';
    }
    
    /**
     * الحصول على متصفح المستخدم
     *
     * @return string
     */
    protected function getUserBrowser(): string
    {
        $userAgent = Request::userAgent() ?: '';
        
        if (preg_match('/msie|trident/i', $userAgent)) {
            return 'Internet Explorer';
        } elseif (preg_match('/edg/i', $userAgent)) {
            return 'Microsoft Edge';
        } elseif (preg_match('/chrome/i', $userAgent)) {
            return 'Google Chrome';
        } elseif (preg_match('/firefox/i', $userAgent)) {
            return 'Mozilla Firefox';
        } elseif (preg_match('/safari/i', $userAgent)) {
            return 'Safari';
        } elseif (preg_match('/opera|opr/i', $userAgent)) {
            return 'Opera';
        }
        
        return 'Unknown';
    }
    
    /**
     * حذف الجلسة وبياناتها المرتبطة
     *
     * @param string $sessionId
     * @return bool
     */
    public function deleteSession(string $sessionId): bool
    {
        try {
            $session = ChatSession::where('session_id', $sessionId)->firstOrFail();
            
            // حذف جميع الرسائل المرتبطة بالجلسة
            $session->messages()->delete();
            
            // حذف الجلسة
            $deleted = $session->delete();
            
            // حذف الجلسة من الذاكرة المؤقتة
            Cache::forget('chat_session_' . $sessionId);
            
            return $deleted;
        } catch (ModelNotFoundException $e) {
            return false;
        }
    }

    public function getSessionStats(ChatSession $session): array
    {
        $messageCount = $session->messages()->count();
        $userMessages = $session->messages()->where('role', 'user')->count();
        $widdxMessages = $session->messages()->where('role', 'widdx')->count();

        return [
            'total_messages' => $messageCount,
            'user_messages' => $userMessages,
            'widdx_messages' => $widdxMessages,
            'session_duration' => $session->created_at->diffInMinutes($session->last_activity),
            'personality' => $session->personality_type,
            'created_at' => $session->created_at,
            'last_activity' => $session->last_activity,
        ];
    }
}
