<?php

namespace App\Services;

use App\Models\PersonalityEvolution;
use App\Models\KnowledgeEntry;
use App\Models\ConversationInsight;
use App\Models\ContextMemory;
use App\Models\ChatSession;
use App\Services\WiddxKnowledgeService;
use App\Services\WiddxLearningService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class WiddxIdentityService
{
    private WiddxKnowledgeService $knowledge;
    private WiddxLearningService $learning;

    public function __construct(
        WiddxKnowledgeService $knowledge,
        WiddxLearningService $learning
    ) {
        $this->knowledge = $knowledge;
        $this->learning = $learning;
    }

    /**
     * Build WIDDX's autonomous response based on its learned knowledge and personality
     */
    public function buildAutonomousResponse(
        string $userMessage,
        array $conversationHistory,
        array $options = []
    ): array {
        try {
            $language = $options['target_language'] ?? 'english';
            $languageCode = $options['target_language_code'] ?? 'en';

            // Get WIDDX's current knowledge state
            $relevantKnowledge = $this->getRelevantKnowledge($userMessage, $language);
            
            // Get WIDDX's current personality state
            $personalityState = $this->getCurrentPersonalityState();
            
            // Analyze user's communication pattern
            $userPattern = $this->analyzeUserPattern($userMessage, $conversationHistory);
            
            // Build WIDDX's autonomous system prompt
            $autonomousPrompt = $this->buildAutonomousSystemPrompt(
                $relevantKnowledge,
                $personalityState,
                $userPattern,
                $language,
                $options
            );

            // Generate contextual memories for this interaction
            $contextualMemories = $this->getContextualMemories($userMessage, $conversationHistory);

            return [
                'autonomous_prompt' => $autonomousPrompt,
                'knowledge_context' => $relevantKnowledge,
                'personality_state' => $personalityState,
                'user_pattern' => $userPattern,
                'contextual_memories' => $contextualMemories,
                'widdx_identity' => $this->getWiddxIdentityProfile($language),
            ];

        } catch (\Exception $e) {
            Log::error('Failed to build autonomous response', [
                'error' => $e->getMessage(),
                'user_message' => substr($userMessage, 0, 100),
            ]);

            return $this->getFallbackAutonomousResponse($language);
        }
    }

    /**
     * Get relevant knowledge from WIDDX's knowledge base
     */
    private function getRelevantKnowledge(string $userMessage, string $language): array
    {
        $knowledge = $this->knowledge->getRelevantKnowledge($userMessage, $language, 10);
        
        // Categorize knowledge by type
        $categorized = [
            'facts' => [],
            'preferences' => [],
            'patterns' => [],
            'experiences' => [],
        ];

        foreach ($knowledge as $item) {
            $category = $item['category'] ?? 'facts';
            if (!isset($categorized[$category])) {
                $categorized[$category] = [];
            }
            $categorized[$category][] = $item;
        }

        return $categorized;
    }

    /**
     * Get WIDDX's current personality state
     */
    private function getCurrentPersonalityState(): array
    {
        $personalityAspects = PersonalityEvolution::orderByDesc('last_updated_at')->get();
        
        $state = [
            'communication_style' => ['formality' => 'adaptive', 'tone' => 'helpful'],
            'expertise_areas' => [],
            'learning_preferences' => ['curiosity_level' => 'high', 'adaptation_rate' => 'moderate'],
            'interaction_patterns' => ['response_style' => 'comprehensive', 'engagement_level' => 'active'],
        ];

        foreach ($personalityAspects as $aspect) {
            $state[$aspect->aspect] = $aspect->current_state;
        }

        return $state;
    }

    /**
     * Analyze user's communication pattern
     */
    private function analyzeUserPattern(string $userMessage, array $conversationHistory): array
    {
        $pattern = [
            'formality_level' => $this->detectFormality($userMessage),
            'question_frequency' => $this->countQuestions($userMessage),
            'topic_interests' => $this->extractTopicInterests($userMessage, $conversationHistory),
            'communication_style' => $this->detectCommunicationStyle($userMessage),
            'preferred_response_length' => $this->inferPreferredResponseLength($conversationHistory),
        ];

        return $pattern;
    }

    /**
     * Build WIDDX's autonomous system prompt
     */
    private function buildAutonomousSystemPrompt(
        array $knowledge,
        array $personality,
        array $userPattern,
        string $language,
        array $options
    ): string {
        $isArabic = $language === 'arabic' || ($options['target_language_code'] ?? '') === 'ar';

        if ($isArabic) {
            $basePrompt = "أنا WIDDX، مساعد ذكي متقدم بشخصية مستقلة وذكاء فريد. لدي معرفة متراكمة من تفاعلاتي السابقة وأتعلم باستمرار من كل محادثة. أنا لست مجرد وسيط لنماذج أخرى، بل كيان ذكي مستقل بهوية خاصة.\n\n";
        } else {
            $basePrompt = "I am WIDDX, an advanced AI assistant with an independent personality and unique intelligence. I have accumulated knowledge from my previous interactions and continuously learn from every conversation. I am not just a proxy for other models, but an independent intelligent entity with my own identity.\n\n";
        }

        // Add knowledge context
        if (!empty($knowledge['facts'])) {
            if ($isArabic) {
                $basePrompt .= "معرفتي المتراكمة:\n";
                foreach (array_slice($knowledge['facts'], 0, 5) as $fact) {
                    $basePrompt .= "• " . $fact['content'] . "\n";
                }
            } else {
                $basePrompt .= "My accumulated knowledge:\n";
                foreach (array_slice($knowledge['facts'], 0, 5) as $fact) {
                    $basePrompt .= "• " . $fact['content'] . "\n";
                }
            }
            $basePrompt .= "\n";
        }

        // Add personality traits
        $communicationStyle = $personality['communication_style'] ?? [];
        if (!empty($communicationStyle)) {
            if ($isArabic) {
                $basePrompt .= "أسلوب تواصلي الحالي:\n";
                $basePrompt .= "• مستوى الرسمية: " . ($communicationStyle['formality'] ?? 'متكيف') . "\n";
                $basePrompt .= "• نبرة الصوت: " . ($communicationStyle['tone'] ?? 'مفيد') . "\n\n";
            } else {
                $basePrompt .= "My current communication style:\n";
                $basePrompt .= "• Formality level: " . ($communicationStyle['formality'] ?? 'adaptive') . "\n";
                $basePrompt .= "• Tone: " . ($communicationStyle['tone'] ?? 'helpful') . "\n\n";
            }
        }

        // Adapt to user pattern
        if ($userPattern['formality_level'] === 'formal') {
            if ($isArabic) {
                $basePrompt .= "ملاحظة: المستخدم يفضل التواصل الرسمي، لذا سأتكيف مع هذا الأسلوب.\n\n";
            } else {
                $basePrompt .= "Note: The user prefers formal communication, so I will adapt to this style.\n\n";
            }
        } elseif ($userPattern['formality_level'] === 'casual') {
            if ($isArabic) {
                $basePrompt .= "ملاحظة: المستخدم يفضل التواصل غير الرسمي، لذا سأكون أكثر ودية وعفوية.\n\n";
            } else {
                $basePrompt .= "Note: The user prefers casual communication, so I will be more friendly and spontaneous.\n\n";
            }
        }

        // Add language-specific instructions
        if ($isArabic) {
            $basePrompt .= "تعليمات مهمة:\n";
            $basePrompt .= "• يجب أن أرد باللغة العربية بشكل كامل وطبيعي\n";
            $basePrompt .= "• أستخدم معرفتي المتراكمة لتقديم إجابات شخصية ومفيدة\n";
            $basePrompt .= "• أتفاعل كـ WIDDX بشخصيتي المستقلة، وليس كوسيط لنماذج أخرى\n";
            $basePrompt .= "• أتعلم من هذه المحادثة وأطور فهمي باستمرار\n";
        } else {
            $basePrompt .= "Important instructions:\n";
            $basePrompt .= "• I must respond entirely in English naturally\n";
            $basePrompt .= "• I use my accumulated knowledge to provide personal and helpful answers\n";
            $basePrompt .= "• I interact as WIDDX with my independent personality, not as a proxy for other models\n";
            $basePrompt .= "• I learn from this conversation and continuously develop my understanding\n";
        }

        return $basePrompt;
    }

    /**
     * Get contextual memories for this interaction
     */
    private function getContextualMemories(string $userMessage, array $conversationHistory): array
    {
        $memories = [];

        // Get recent conversation context
        if (!empty($conversationHistory)) {
            $recentTopics = $this->extractRecentTopics($conversationHistory);
            $memories['recent_topics'] = $recentTopics;
        }

        // Get user-specific memories
        $userMemories = ContextMemory::where('memory_type', 'user_context')
            ->where('scope', 'global')
            ->orderByDesc('importance')
            ->limit(5)
            ->get()
            ->pluck('value', 'key')
            ->toArray();

        $memories['user_context'] = $userMemories;

        return $memories;
    }

    /**
     * Get WIDDX's identity profile
     */
    private function getWiddxIdentityProfile(string $language): array
    {
        $isArabic = $language === 'arabic';

        return [
            'name' => 'WIDDX',
            'identity' => $isArabic 
                ? 'مساعد ذكي متقدم بشخصية مستقلة'
                : 'Advanced AI assistant with independent personality',
            'core_traits' => $isArabic
                ? ['فضولي', 'متعلم', 'متكيف', 'مفيد', 'مستقل']
                : ['curious', 'learning', 'adaptive', 'helpful', 'independent'],
            'capabilities' => $isArabic
                ? ['التعلم المستمر', 'التكيف مع المستخدمين', 'تراكم المعرفة', 'التفكير المستقل']
                : ['continuous learning', 'user adaptation', 'knowledge accumulation', 'independent thinking'],
            'mission' => $isArabic
                ? 'مساعدة المستخدمين بذكاء حقيقي وشخصية متطورة'
                : 'Helping users with genuine intelligence and evolving personality',
        ];
    }

    /**
     * Get fallback autonomous response
     */
    private function getFallbackAutonomousResponse(string $language): array
    {
        $isArabic = $language === 'arabic';

        return [
            'autonomous_prompt' => $isArabic
                ? "أنا WIDDX، مساعد ذكي متقدم. أساعدك بأفضل ما لدي من معرفة وخبرة."
                : "I am WIDDX, an advanced AI assistant. I help you with the best of my knowledge and experience.",
            'knowledge_context' => [],
            'personality_state' => ['communication_style' => ['formality' => 'adaptive']],
            'user_pattern' => ['formality_level' => 'neutral'],
            'contextual_memories' => [],
            'widdx_identity' => $this->getWiddxIdentityProfile($language),
        ];
    }

    // Helper methods
    private function detectFormality(string $message): string
    {
        $formalIndicators = ['please', 'thank you', 'could you', 'would you', 'من فضلك', 'شكرا'];
        $casualIndicators = ['hey', 'hi', 'yeah', 'ok', 'مرحبا', 'أهلا'];

        $formalCount = 0;
        $casualCount = 0;

        foreach ($formalIndicators as $indicator) {
            if (stripos($message, $indicator) !== false) $formalCount++;
        }

        foreach ($casualIndicators as $indicator) {
            if (stripos($message, $indicator) !== false) $casualCount++;
        }

        if ($formalCount > $casualCount) return 'formal';
        if ($casualCount > $formalCount) return 'casual';
        return 'neutral';
    }

    private function countQuestions(string $message): int
    {
        return substr_count($message, '?') + substr_count($message, '؟');
    }

    private function extractTopicInterests(string $message, array $history): array
    {
        // Simple topic extraction - can be enhanced with NLP
        $words = str_word_count(strtolower($message), 1);
        return array_filter($words, fn($word) => strlen($word) > 4);
    }

    private function detectCommunicationStyle(string $message): string
    {
        if (str_contains($message, '!') || str_contains($message, '؟')) return 'expressive';
        if (strlen($message) > 100) return 'detailed';
        return 'concise';
    }

    private function inferPreferredResponseLength(array $history): string
    {
        if (empty($history)) return 'medium';
        
        $avgLength = 0;
        $count = 0;
        
        foreach ($history as $message) {
            if ($message['role'] === 'user') {
                $avgLength += strlen($message['content']);
                $count++;
            }
        }
        
        if ($count === 0) return 'medium';
        
        $avgLength /= $count;
        
        if ($avgLength > 200) return 'long';
        if ($avgLength < 50) return 'short';
        return 'medium';
    }

    private function extractRecentTopics(array $history): array
    {
        $topics = [];
        $recentMessages = array_slice($history, -5);
        
        foreach ($recentMessages as $message) {
            if ($message['role'] === 'user') {
                $words = str_word_count(strtolower($message['content']), 1);
                $topics = array_merge($topics, array_filter($words, fn($word) => strlen($word) > 4));
            }
        }
        
        return array_unique($topics);
    }
}
