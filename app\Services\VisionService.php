<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\UploadedFile;

class VisionService
{
    private DeepSeekClient $deepSeek;
    private array $supportedImageTypes;
    private int $maxImageSize;

    public function __construct(DeepSeekClient $deepSeek)
    {
        $this->deepSeek = $deepSeek;
        $this->supportedImageTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'bmp' => 'image/bmp',
        ];
        $this->maxImageSize = 20 * 1024 * 1024; // 20MB as per Grok specs
    }

    /**
     * Analyze uploaded image
     */
    public function analyzeImage(UploadedFile $image, string $prompt = '', array $options = []): array
    {
        try {
            Log::info('Image analysis started', [
                'filename' => $image->getClientOriginalName(),
                'size' => $image->getSize(),
                'type' => $image->getMimeType(),
                'prompt' => $prompt,
            ]);

            // Validate image
            $validation = $this->validateImage($image);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => $validation['error'],
                    'filename' => $image->getClientOriginalName(),
                ];
            }

            // Convert image to base64
            $imageData = $this->prepareImageData($image);

            // Perform analysis
            $analysis = $this->performImageAnalysis($imageData, $prompt, $options);

            // Extract text if requested
            $extractedText = '';
            if ($options['extract_text'] ?? false) {
                $extractedText = $this->extractTextFromImage($imageData);
            }

            // Detect objects if requested
            $detectedObjects = [];
            if ($options['detect_objects'] ?? false) {
                $detectedObjects = $this->detectObjects($imageData);
            }

            $result = [
                'success' => true,
                'filename' => $image->getClientOriginalName(),
                'image_size' => $image->getSize(),
                'image_type' => $image->getClientOriginalExtension(),
                'analysis' => $analysis,
                'extracted_text' => $extractedText,
                'detected_objects' => $detectedObjects,
                'metadata' => [
                    'processed_at' => now()->toISOString(),
                    'image_dimensions' => $this->getImageDimensions($image),
                    'analysis_type' => $options['analysis_type'] ?? 'comprehensive',
                ],
            ];

            Log::info('Image analysis completed', [
                'filename' => $image->getClientOriginalName(),
                'success' => true,
                'has_text' => !empty($extractedText),
                'objects_count' => count($detectedObjects),
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Image analysis error', [
                'filename' => $image->getClientOriginalName(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'filename' => $image->getClientOriginalName(),
            ];
        }
    }

    /**
     * Analyze image from URL
     */
    public function analyzeImageFromUrl(string $imageUrl, string $prompt = '', array $options = []): array
    {
        try {
            Log::info('Image URL analysis started', [
                'url' => $imageUrl,
                'prompt' => $prompt,
            ]);

            // Download image
            $response = Http::timeout(30)->get($imageUrl);
            
            if (!$response->successful()) {
                throw new \Exception('فشل في تحميل الصورة من الرابط');
            }

            // Create temporary file
            $tempFile = tmpfile();
            fwrite($tempFile, $response->body());
            $tempPath = stream_get_meta_data($tempFile)['uri'];

            // Get image info
            $imageInfo = getimagesize($tempPath);
            if (!$imageInfo) {
                throw new \Exception('الملف المحمل ليس صورة صالحة');
            }

            // Convert to base64
            $imageData = [
                'data' => base64_encode($response->body()),
                'mime_type' => $imageInfo['mime'],
            ];

            // Perform analysis
            $analysis = $this->performImageAnalysis($imageData, $prompt, $options);

            fclose($tempFile);

            return [
                'success' => true,
                'image_url' => $imageUrl,
                'analysis' => $analysis,
                'metadata' => [
                    'processed_at' => now()->toISOString(),
                    'image_dimensions' => $imageInfo[0] . 'x' . $imageInfo[1],
                    'mime_type' => $imageInfo['mime'],
                ],
            ];

        } catch (\Exception $e) {
            Log::error('Image URL analysis error', [
                'url' => $imageUrl,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'image_url' => $imageUrl,
            ];
        }
    }

    /**
     * Validate uploaded image
     */
    private function validateImage(UploadedFile $image): array
    {
        // Check file size
        if ($image->getSize() > $this->maxImageSize) {
            return [
                'valid' => false,
                'error' => 'حجم الصورة كبير جداً. الحد الأقصى هو 20 ميجابايت.',
            ];
        }

        // Check file type
        $extension = strtolower($image->getClientOriginalExtension());
        if (!array_key_exists($extension, $this->supportedImageTypes)) {
            return [
                'valid' => false,
                'error' => 'نوع الصورة غير مدعوم. الأنواع المدعومة: ' . implode(', ', array_keys($this->supportedImageTypes)),
            ];
        }

        // Check MIME type
        $mimeType = $image->getMimeType();
        if (!in_array($mimeType, $this->supportedImageTypes)) {
            return [
                'valid' => false,
                'error' => 'نوع الملف لا يتطابق مع امتداد الصورة.',
            ];
        }

        return ['valid' => true];
    }

    /**
     * Prepare image data for analysis
     */
    private function prepareImageData(UploadedFile $image): array
    {
        $imageContent = $image->getContent();
        $base64Data = base64_encode($imageContent);

        return [
            'data' => $base64Data,
            'mime_type' => $image->getMimeType(),
        ];
    }

    /**
     * Perform comprehensive image analysis
     */
    private function performImageAnalysis(array $imageData, string $prompt, array $options): array
    {
        try {
            $analysisType = $options['analysis_type'] ?? 'comprehensive';
            
            // Build analysis prompt
            $systemPrompt = "You are an expert image analyst. Analyze the provided image and provide detailed insights.";
            
            $userPrompt = $prompt ?: "Analyze this image and describe:
1. What you see in the image
2. Objects, people, or scenes present
3. Colors, composition, and visual elements
4. Context and setting
5. Any text visible in the image
6. Overall mood or atmosphere
7. Technical aspects (lighting, quality, etc.)

Provide a comprehensive analysis in Arabic.";

            // Prepare messages with image
            $messages = [
                [
                    'role' => 'system',
                    'content' => $systemPrompt,
                ],
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => $userPrompt,
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => 'data:' . $imageData['mime_type'] . ';base64,' . $imageData['data'],
                            ],
                        ],
                    ],
                ],
            ];

            $response = $this->deepSeek->chat($messages, [
                'max_tokens' => 1000,
                'temperature' => 0.7,
            ]);

            if ($response['success']) {
                return [
                    'description' => $response['content'],
                    'analysis_type' => $analysisType,
                    'confidence' => 0.85,
                ];
            } else {
                throw new \Exception('فشل في تحليل الصورة: ' . ($response['error'] ?? 'خطأ غير معروف'));
            }

        } catch (\Exception $e) {
            Log::error('Image analysis failed', ['error' => $e->getMessage()]);
            
            return [
                'description' => 'فشل في تحليل الصورة. ' . $e->getMessage(),
                'analysis_type' => $analysisType ?? 'comprehensive',
                'confidence' => 0.0,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Extract text from image (OCR)
     */
    private function extractTextFromImage(array $imageData): string
    {
        try {
            $messages = [
                [
                    'role' => 'system',
                    'content' => 'You are an OCR expert. Extract all visible text from the image accurately.',
                ],
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => 'Extract all text visible in this image. If there is no text, respond with "لا يوجد نص مرئي في الصورة".',
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => 'data:' . $imageData['mime_type'] . ';base64,' . $imageData['data'],
                            ],
                        ],
                    ],
                ],
            ];

            $response = $this->deepSeek->chat($messages, [
                'max_tokens' => 500,
                'temperature' => 0.1, // Lower temperature for more accurate text extraction
            ]);

            return $response['success'] ? $response['content'] : 'فشل في استخراج النص من الصورة.';

        } catch (\Exception $e) {
            Log::warning('Text extraction failed', ['error' => $e->getMessage()]);
            return 'فشل في استخراج النص من الصورة.';
        }
    }

    /**
     * Detect objects in image
     */
    private function detectObjects(array $imageData): array
    {
        try {
            $messages = [
                [
                    'role' => 'system',
                    'content' => 'You are an object detection expert. Identify and list all objects visible in the image.',
                ],
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => 'Identify all objects, people, animals, and items visible in this image. Provide the results in JSON format with object names and confidence levels.',
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => 'data:' . $imageData['mime_type'] . ';base64,' . $imageData['data'],
                            ],
                        ],
                    ],
                ],
            ];

            $response = $this->deepSeek->chat($messages, [
                'max_tokens' => 400,
                'temperature' => 0.3,
            ]);

            if ($response['success']) {
                // Try to extract JSON from response
                if (preg_match('/\[.*\]/s', $response['content'], $matches)) {
                    $objects = json_decode($matches[0], true);
                    if (is_array($objects)) {
                        return $objects;
                    }
                }
                
                // Fallback: parse text response
                $lines = explode("\n", $response['content']);
                $objects = [];
                foreach ($lines as $line) {
                    $line = trim($line);
                    if (!empty($line) && !str_starts_with($line, '-')) {
                        $objects[] = [
                            'name' => $line,
                            'confidence' => 0.7,
                        ];
                    }
                }
                return $objects;
            }

            return [];

        } catch (\Exception $e) {
            Log::warning('Object detection failed', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get image dimensions
     */
    private function getImageDimensions(UploadedFile $image): string
    {
        try {
            $tempPath = $image->getRealPath();
            $imageInfo = getimagesize($tempPath);
            
            if ($imageInfo) {
                return $imageInfo[0] . 'x' . $imageInfo[1];
            }
            
            return 'غير معروف';
        } catch (\Exception $e) {
            return 'غير معروف';
        }
    }

    /**
     * Compare two images
     */
    public function compareImages(UploadedFile $image1, UploadedFile $image2, array $options = []): array
    {
        try {
            $imageData1 = $this->prepareImageData($image1);
            $imageData2 = $this->prepareImageData($image2);

            $messages = [
                [
                    'role' => 'system',
                    'content' => 'You are an image comparison expert. Compare the two images and identify similarities and differences.',
                ],
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => 'Compare these two images and describe:
1. Similarities between them
2. Key differences
3. Which elements are the same or different
4. Overall comparison summary

Provide detailed comparison in Arabic.',
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => 'data:' . $imageData1['mime_type'] . ';base64,' . $imageData1['data'],
                            ],
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => 'data:' . $imageData2['mime_type'] . ';base64,' . $imageData2['data'],
                            ],
                        ],
                    ],
                ],
            ];

            $response = $this->deepSeek->chat($messages, [
                'max_tokens' => 800,
                'temperature' => 0.7,
            ]);

            return [
                'success' => true,
                'comparison' => $response['success'] ? $response['content'] : 'فشل في مقارنة الصور.',
                'image1_name' => $image1->getClientOriginalName(),
                'image2_name' => $image2->getClientOriginalName(),
            ];

        } catch (\Exception $e) {
            Log::error('Image comparison failed', ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'comparison' => 'فشل في مقارنة الصور.',
            ];
        }
    }

    /**
     * Get supported image types
     */
    public function getSupportedTypes(): array
    {
        return array_keys($this->supportedImageTypes);
    }

    /**
     * Get vision capabilities
     */
    public function getCapabilities(): array
    {
        return [
            'supported_formats' => array_keys($this->supportedImageTypes),
            'max_file_size' => $this->maxImageSize,
            'features' => [
                'image_description',
                'object_detection',
                'text_extraction',
                'image_comparison',
                'scene_analysis',
                'color_analysis',
            ],
            'analysis_types' => ['quick', 'comprehensive', 'detailed'],
        ];
    }
}
