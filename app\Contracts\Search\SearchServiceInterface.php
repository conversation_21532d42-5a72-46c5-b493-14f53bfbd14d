<?php

namespace App\Contracts\Search;

use App\ValueObjects\Search\SearchResult;
use App\ValueObjects\Search\SearchOptions;

interface SearchServiceInterface
{
    /**
     * Perform a search with the given query and options
     */
    public function search(string $query, SearchOptions $options = null): SearchResult;

    /**
     * Get the capabilities of this search service
     */
    public function getCapabilities(): array;

    /**
     * Check if the search service is available
     */
    public function isAvailable(): bool;

    /**
     * Get the service name/identifier
     */
    public function getName(): string;
}