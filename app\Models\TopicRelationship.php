<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TopicRelationship extends Model
{
    protected $fillable = [
        'topic_a',
        'topic_b',
        'relationship_type',
        'strength',
        'context',
    ];

    protected $casts = [
        'context' => 'array',
        'strength' => 'float',
    ];

    /**
     * Strengthen the relationship
     */
    public function strengthen(float $amount = 0.1): void
    {
        $newStrength = min(1.0, $this->strength + $amount);
        $this->update(['strength' => $newStrength]);
    }

    /**
     * Weaken the relationship
     */
    public function weaken(float $amount = 0.1): void
    {
        $newStrength = max(0.0, $this->strength - $amount);
        $this->update(['strength' => $newStrength]);
    }

    /**
     * Get related topics for a given topic
     */
    public static function getRelatedTopics(string $topic, float $minStrength = 0.5): array
    {
        $relationships = static::where(function ($query) use ($topic) {
            $query->where('topic_a', $topic)
                  ->orWhere('topic_b', $topic);
        })
        ->where('strength', '>=', $minStrength)
        ->orderByDesc('strength')
        ->get();

        $relatedTopics = [];
        foreach ($relationships as $relationship) {
            $relatedTopic = $relationship->topic_a === $topic 
                ? $relationship->topic_b 
                : $relationship->topic_a;
            
            $relatedTopics[] = [
                'topic' => $relatedTopic,
                'relationship_type' => $relationship->relationship_type,
                'strength' => $relationship->strength,
            ];
        }

        return $relatedTopics;
    }

    /**
     * Create or update a relationship
     */
    public static function createOrUpdateRelationship(
        string $topicA,
        string $topicB,
        string $relationshipType,
        float $strength = 0.5,
        array $context = []
    ): self {
        // Ensure consistent ordering (alphabetical)
        if ($topicA > $topicB) {
            [$topicA, $topicB] = [$topicB, $topicA];
        }

        return static::updateOrCreate([
            'topic_a' => $topicA,
            'topic_b' => $topicB,
            'relationship_type' => $relationshipType,
        ], [
            'strength' => $strength,
            'context' => $context,
        ]);
    }

    /**
     * Scope for strong relationships
     */
    public function scopeStrong($query, float $threshold = 0.7)
    {
        return $query->where('strength', '>=', $threshold);
    }

    /**
     * Scope for specific relationship type
     */
    public function scopeType($query, string $type)
    {
        return $query->where('relationship_type', $type);
    }
}
