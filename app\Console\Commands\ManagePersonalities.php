<?php

namespace App\Console\Commands;

use App\Models\Personality;
use Illuminate\Console\Command;

class ManagePersonalities extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'widdx:personalities {action : list, create, update, delete, toggle} {name? : Personality name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage WIDDX AI personalities';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');
        $name = $this->argument('name');

        switch ($action) {
            case 'list':
                return $this->listPersonalities();
            case 'create':
                return $this->createPersonality();
            case 'update':
                return $this->updatePersonality($name);
            case 'delete':
                return $this->deletePersonality($name);
            case 'toggle':
                return $this->togglePersonality($name);
            default:
                $this->error("Invalid action. Use: list, create, update, delete, or toggle");
                return self::FAILURE;
        }
    }

    private function listPersonalities()
    {
        $personalities = Personality::orderBy('sort_order')->orderBy('name')->get();

        if ($personalities->isEmpty()) {
            $this->info('No personalities found.');
            return self::SUCCESS;
        }

        $this->table(
            ['Name', 'Display Name', 'Active', 'Sort Order', 'Description'],
            $personalities->map(function ($personality) {
                return [
                    $personality->name,
                    $personality->display_name,
                    $personality->is_active ? 'Yes' : 'No',
                    $personality->sort_order,
                    substr($personality->description, 0, 50) . '...',
                ];
            })
        );

        return self::SUCCESS;
    }

    private function createPersonality()
    {
        $name = $this->ask('Personality name (lowercase, no spaces)');
        $displayName = $this->ask('Display name');
        $description = $this->ask('Description');
        $systemPrompt = $this->ask('System prompt');
        $sortOrder = $this->ask('Sort order', '0');

        if (Personality::where('name', $name)->exists()) {
            $this->error("Personality '{$name}' already exists.");
            return self::FAILURE;
        }

        Personality::create([
            'name' => $name,
            'display_name' => $displayName,
            'description' => $description,
            'system_prompt' => $systemPrompt,
            'sort_order' => (int) $sortOrder,
            'is_active' => true,
        ]);

        $this->info("Personality '{$name}' created successfully.");
        return self::SUCCESS;
    }

    private function updatePersonality($name)
    {
        if (!$name) {
            $this->error('Personality name is required for update.');
            return self::FAILURE;
        }

        $personality = Personality::where('name', $name)->first();
        if (!$personality) {
            $this->error("Personality '{$name}' not found.");
            return self::FAILURE;
        }

        $this->info("Updating personality: {$personality->display_name}");

        $displayName = $this->ask('Display name', $personality->display_name);
        $description = $this->ask('Description', $personality->description);
        $systemPrompt = $this->ask('System prompt', $personality->system_prompt);
        $sortOrder = $this->ask('Sort order', $personality->sort_order);

        $personality->update([
            'display_name' => $displayName,
            'description' => $description,
            'system_prompt' => $systemPrompt,
            'sort_order' => (int) $sortOrder,
        ]);

        $this->info("Personality '{$name}' updated successfully.");
        return self::SUCCESS;
    }

    private function deletePersonality($name)
    {
        if (!$name) {
            $this->error('Personality name is required for deletion.');
            return self::FAILURE;
        }

        $personality = Personality::where('name', $name)->first();
        if (!$personality) {
            $this->error("Personality '{$name}' not found.");
            return self::FAILURE;
        }

        if (!$this->confirm("Are you sure you want to delete personality '{$personality->display_name}'?")) {
            $this->info('Deletion cancelled.');
            return self::SUCCESS;
        }

        $personality->delete();
        $this->info("Personality '{$name}' deleted successfully.");
        return self::SUCCESS;
    }

    private function togglePersonality($name)
    {
        if (!$name) {
            $this->error('Personality name is required for toggle.');
            return self::FAILURE;
        }

        $personality = Personality::where('name', $name)->first();
        if (!$personality) {
            $this->error("Personality '{$name}' not found.");
            return self::FAILURE;
        }

        $personality->update(['is_active' => !$personality->is_active]);

        $status = $personality->is_active ? 'activated' : 'deactivated';
        $this->info("Personality '{$name}' {$status} successfully.");

        return self::SUCCESS;
    }
}
