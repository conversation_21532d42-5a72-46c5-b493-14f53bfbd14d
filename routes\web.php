<?php

use App\Http\Controllers\WebController;
use Illuminate\Support\Facades\Route;

// Main route - Production UI
Route::get('/', [WebController::class, 'index']);

// Alternative UI routes for testing
Route::get('/vite-ui', function () {
    $personalities = app(\App\Services\PersonalityModifierService::class)->getAvailablePersonalities();
    return view('chat-new', compact('personalities'));
});

Route::get('/production-ui', function () {
    $personalities = app(\App\Services\PersonalityModifierService::class)->getAvailablePersonalities();
    return view('chat-production', compact('personalities'));
});
