// WIDDX AI Sidebar Controller - Modern Navigation System

class WiddxSidebar {
    constructor() {
        this.sidebar = document.getElementById('widdx-sidebar');
        this.overlay = document.getElementById('sidebar-overlay');
        this.toggleBtn = document.getElementById('sidebar-toggle');
        this.newChatBtn = document.getElementById('new-chat-btn');
        this.chatHistory = document.getElementById('chat-history');

        this.isCollapsed = localStorage.getItem('widdx-sidebar-collapsed') === 'true';
        this.isMobile = window.innerWidth < 1024;
        this.chatSessions = this.loadChatSessions();

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupKeyboardShortcuts();
        this.renderChatHistory();
        this.updateSidebarState();
        this.setupQuickActions();

        // Handle window resize
        window.addEventListener('resize', this.debounce(() => {
            this.isMobile = window.innerWidth < 1024;
            this.updateSidebarState();
        }, 250));
    }

    setupEventListeners() {
        // Toggle sidebar
        this.toggleBtn?.addEventListener('click', () => {
            this.toggleSidebar();
        });

        // Overlay click to close on mobile
        this.overlay?.addEventListener('click', () => {
            if (this.isMobile) {
                this.closeSidebar();
            }
        });

        // New chat button
        this.newChatBtn?.addEventListener('click', () => {
            this.createNewChat();
        });

        // Chat history clicks
        this.chatHistory?.addEventListener('click', (e) => {
            const chatItem = e.target.closest('.widdx-chat-item');
            if (chatItem) {
                this.selectChat(chatItem.dataset.chatId);
            }
        });

        // Escape key to close sidebar on mobile
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isMobile && !this.isCollapsed) {
                this.closeSidebar();
            }
        });
    }

    setupKeyboardShortcuts() {
        // Register shortcuts with the global UI system
        if (window.widdxUI) {
            window.widdxUI.registerShortcut('ctrl+b', () => this.toggleSidebar());
            window.widdxUI.registerShortcut('ctrl+n', () => this.createNewChat());
            window.widdxUI.registerShortcut('ctrl+shift+h', () => this.showChatHistory());
        }
    }

    setupQuickActions() {
        const quickActions = document.querySelectorAll('.widdx-quick-action');
        quickActions.forEach(action => {
            action.addEventListener('click', () => {
                const actionType = action.dataset.action;
                this.handleQuickAction(actionType);
            });
        });
    }

    handleQuickAction(actionType) {
        // Emit custom events for the main chat system to handle
        const event = new CustomEvent('widdx-quick-action', {
            detail: { action: actionType }
        });
        document.dispatchEvent(event);

        // Close sidebar on mobile after action
        if (this.isMobile) {
            this.closeSidebar();
        }

        // Show notification
        if (window.widdxUI) {
            const actionNames = {
                'search': 'البحث المباشر',
                'image-gen': 'توليد الصور',
                'voice': 'التحكم الصوتي',
                'think-mode': 'وضع التفكير',
                'settings': 'الإعدادات',
                'help': 'المساعدة'
            };

            window.widdxUI.showNotification(
                `تم تفعيل ${actionNames[actionType]}`,
                'info',
                3000
            );
        }
    }

    toggleSidebar() {
        if (this.isMobile) {
            this.isCollapsed ? this.openSidebar() : this.closeSidebar();
        } else {
            this.isCollapsed = !this.isCollapsed;
            this.updateSidebarState();
            this.saveSidebarState();
        }
    }

    openSidebar() {
        this.isCollapsed = false;
        this.updateSidebarState();

        if (this.isMobile) {
            this.overlay?.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    closeSidebar() {
        if (this.isMobile) {
            this.isCollapsed = true;
            this.updateSidebarState();
            this.overlay?.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    updateSidebarState() {
        if (!this.sidebar) return;

        if (this.isCollapsed) {
            this.sidebar.classList.add('collapsed');
            if (this.isMobile) {
                this.sidebar.classList.remove('open');
            }
        } else {
            this.sidebar.classList.remove('collapsed');
            if (this.isMobile) {
                this.sidebar.classList.add('open');
            }
        }

        // Update toggle button icon
        this.updateToggleIcon();
    }

    updateToggleIcon() {
        const icon = this.toggleBtn?.querySelector('svg');
        if (!icon) return;

        if (this.isCollapsed) {
            icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>';
        } else {
            icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>';
        }
    }

    createNewChat() {
        const newChatId = this.generateChatId();
        const newChat = {
            id: newChatId,
            title: 'New Chat',
            timestamp: new Date().toISOString(),
            messages: []
        };

        this.chatSessions.unshift(newChat);
        this.saveChatSessions();
        this.renderChatHistory();
        this.selectChat(newChatId);

        // Emit event for main chat system
        const event = new CustomEvent('widdx-new-chat', {
            detail: { chatId: newChatId }
        });
        document.dispatchEvent(event);
    }

    selectChat(chatId) {
        // Update active state
        const chatItems = this.chatHistory?.querySelectorAll('.widdx-chat-item');
        chatItems?.forEach(item => {
            item.classList.toggle('active', item.dataset.chatId === chatId);
        });

        // Emit event for main chat system
        const event = new CustomEvent('widdx-select-chat', {
            detail: { chatId }
        });
        document.dispatchEvent(event);
    }

    renderChatHistory() {
        if (!this.chatHistory) return;

        const historyHTML = this.chatSessions.map(chat => `
            <div class="widdx-chat-item group" data-chat-id="${chat.id}">
                <div class="flex items-center space-x-3 p-3 mx-2 rounded-lg hover:bg-widdx-bg-hover cursor-pointer transition-colors">
                    <div class="w-2 h-2 bg-widdx-text-tertiary rounded-full flex-shrink-0"></div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm text-widdx-text-primary truncate sidebar-text">${chat.title}</p>
                        <p class="text-xs text-widdx-text-tertiary sidebar-text">${this.formatTimestamp(chat.timestamp)}</p>
                    </div>
                    <div class="opacity-0 group-hover:opacity-100 transition-opacity">
                        <button class="widdx-chat-options p-1 hover:bg-widdx-bg-elevated rounded"
                                onclick="event.stopPropagation(); widdxSidebar.showChatOptions('${chat.id}')">
                            <svg class="w-4 h-4 text-widdx-text-secondary" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        this.chatHistory.innerHTML = historyHTML;
    }

    showChatOptions(chatId) {
        // Create context menu for chat options
        const options = [
            { label: 'Rename', action: () => this.renameChat(chatId) },
            { label: 'Export', action: () => this.exportChat(chatId) },
            { label: 'Delete', action: () => this.deleteChat(chatId), danger: true }
        ];

        // This would integrate with a context menu system
        console.log('Chat options for:', chatId, options);
    }

    renameChat(chatId) {
        const chat = this.chatSessions.find(c => c.id === chatId);
        if (!chat) return;

        const newTitle = prompt('New chat name:', chat.title);
        if (newTitle && newTitle.trim()) {
            chat.title = newTitle.trim();
            this.saveChatSessions();
            this.renderChatHistory();
        }
    }

    deleteChat(chatId) {
        if (confirm('Are you sure you want to delete this chat?')) {
            this.chatSessions = this.chatSessions.filter(c => c.id !== chatId);
            this.saveChatSessions();
            this.renderChatHistory();
        }
    }

    exportChat(chatId) {
        const chat = this.chatSessions.find(c => c.id === chatId);
        if (!chat) return;

        const dataStr = JSON.stringify(chat, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `widdx-chat-${chat.title}-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        URL.revokeObjectURL(url);
    }

    // Utility methods
    generateChatId() {
        return 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return 'Now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        if (diffDays < 7) return `${diffDays}d ago`;

        return date.toLocaleDateString('en-US');
    }

    loadChatSessions() {
        try {
            const saved = localStorage.getItem('widdx-chat-sessions');
            return saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.error('Error loading chat sessions:', error);
            return [];
        }
    }

    saveChatSessions() {
        try {
            localStorage.setItem('widdx-chat-sessions', JSON.stringify(this.chatSessions));
        } catch (error) {
            console.error('Error saving chat sessions:', error);
        }
    }

    saveSidebarState() {
        localStorage.setItem('widdx-sidebar-collapsed', this.isCollapsed.toString());
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize sidebar when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.widdxSidebar = new WiddxSidebar();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WiddxSidebar;
}
