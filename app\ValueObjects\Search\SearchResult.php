<?php

namespace App\ValueObjects\Search;

class SearchResult
{
    public function __construct(
        public readonly bool $success,
        public readonly string $query,
        public readonly array $results,
        public readonly string $provider,
        public readonly ?string $error = null,
        public readonly array $metadata = [],
        public readonly float $responseTime = 0.0,
        public readonly int $totalResults = 0,
        public readonly bool $fromCache = false,
        public readonly ?string $nextPageToken = null,
        public readonly array $providerResults = []
    ) {}

    /**
     * Convert to array for API responses
     */
    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'query' => $this->query,
            'results' => array_map(
                fn($result) => $result instanceof SearchResultItem ? $result->toArray() : $result,
                $this->results
            ),
            'provider' => $this->provider,
            'error' => $this->error,
            'metadata' => $this->metadata,
            'responseTime' => $this->responseTime,
            'totalResults' => $this->totalResults,
            'fromCache' => $this->fromCache,
            'nextPageToken' => $this->nextPageToken,
            'providerResults' => array_map(
                fn($result) => $result instanceof ProviderResult ? $result->toArray() : $result,
                $this->providerResults
            ),
        ];
    }

    /**
     * Create a successful search result
     */
    public static function success(
        string $query,
        array $results,
        string $provider,
        array $metadata = [],
        float $responseTime = 0.0,
        int $totalResults = 0,
        bool $fromCache = false,
        ?string $nextPageToken = null,
        array $providerResults = []
    ): self {
        return new self(
            success: true,
            query: $query,
            results: $results,
            provider: $provider,
            error: null,
            metadata: $metadata,
            responseTime: $responseTime,
            totalResults: $totalResults,
            fromCache: $fromCache,
            nextPageToken: $nextPageToken,
            providerResults: $providerResults
        );
    }

    /**
     * Create a failed search result
     */
    public static function failure(
        string $query,
        string $error,
        string $provider,
        array $metadata = [],
        float $responseTime = 0.0,
        array $providerResults = []
    ): self {
        return new self(
            success: false,
            query: $query,
            results: [],
            provider: $provider,
            error: $error,
            metadata: $metadata,
            responseTime: $responseTime,
            totalResults: 0,
            fromCache: false,
            nextPageToken: null,
            providerResults: $providerResults
        );
    }

    /**
     * Get the search result items as SearchResultItem objects
     */
    public function getResultItems(): array
    {
        return array_map(
            fn($result) => $result instanceof SearchResultItem 
                ? $result 
                : SearchResultItem::fromArray($result),
            $this->results
        );
    }

    /**
     * Check if the result has any valid results
     */
    public function hasResults(): bool
    {
        return $this->success && !empty($this->results);
    }

    /**
     * Get combined metadata from all provider results
     */
    public function getCombinedMetadata(): array
    {
        $combined = $this->metadata;
        
        foreach ($this->providerResults as $providerResult) {
            if ($providerResult instanceof ProviderResult) {
                $combined = array_merge($combined, $providerResult->metadata);
            }
        }
        
        return $combined;
    }
}