<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class HuggingFaceImageService
{
    private string $apiKey;
    private string $model;
    private int $timeout;

    public function __construct()
    {
        $this->apiKey = config('services.huggingface.api_key');
        $this->model = config('services.huggingface.model', 'stabilityai/stable-diffusion-xl-base-1.0');
        $this->timeout = (int) config('services.huggingface.timeout', 120);
    }

    /**
     * Generate an image from a text prompt using Hugging Face inference API.
     *
     * @param string $prompt
     * @return array{success: bool, path?: string, error?: string}
     */
    public function generateImage(string $prompt): array
    {
        if (empty($this->apiKey)) {
            return [
                'success' => false,
                'error' => 'Hugging Face API key is not configured.'
            ];
        }

        $url = "https://api-inference.huggingface.co/models/{$this->model}";

        try {
            $response = Http::withToken($this->apiKey)
                ->timeout($this->timeout)
                ->withHeaders([
                    'Accept' => 'application/json'
                ])
                ->post($url, [
                    'inputs' => $prompt,
                ]);

            if ($response->failed()) {
                return [
                    'success' => false,
                    'error' => 'API request failed: ' . $response->body(),
                ];
            }

            $json = $response->json();
            if (!isset($json[0]['generated_image']) && !isset($json[0]['blob'])) {
                return [
                    'success' => false,
                    'error' => 'Unexpected API response.'
                ];
            }

            // HF may return base64 string in 'generated_image' or 'blob'
            $base64 = $json[0]['generated_image'] ?? $json[0]['blob'];
            $imageData = base64_decode($base64);
            if ($imageData === false) {
                return [
                    'success' => false,
                    'error' => 'Failed to decode image data.'
                ];
            }

            $fileName = 'images/' . uniqid('hf_', true) . '.png';
            Storage::disk('public')->put($fileName, $imageData);

            return [
                'success' => true,
                'path' => Storage::url($fileName),
            ];
        } catch (\Exception $e) {
            Log::error('HuggingFace image generation error', ['error' => $e->getMessage()]);
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
}
