{{-- WIDDX AI Chat Area Component - Enhanced Layout --}}
<main class="widdx-chat-area">
    {{-- Cha<PERSON> Header --}}
    <header class="widdx-chat-header">
        <div class="widdx-container">
            <div class="flex items-center justify-between py-4">
                {{-- Mobile Sidebar Toggle --}}
                <button id="mobile-sidebar-toggle" class="widdx-btn widdx-btn-ghost p-2 lg:hidden">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>

                {{-- Chat Info --}}
                <div class="flex items-center space-x-3">
                    <div class="widdx-gradient-primary w-8 h-8 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-sm">W</span>
                    </div>
                    <div>
                        <h1 class="text-lg font-semibold text-widdx-text-primary">WIDDX AI</h1>
                        <p class="text-xs text-widdx-text-tertiary" id="chat-status">Online - Ready to help</p>
                    </div>
                </div>

                {{-- Chat Actions --}}
                <div class="flex items-center space-x-2">
                    {{-- Personality Selector --}}
                    <div class="relative">
                        <button id="personality-btn" class="widdx-btn widdx-btn-secondary text-sm" data-dropdown="personality-dropdown">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <span id="current-personality">Neutral</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="personality-dropdown" class="widdx-dropdown hidden">
                            <div class="py-1">
                                <button class="widdx-dropdown-item" data-personality="neutral">Neutral</button>
                                <button class="widdx-dropdown-item" data-personality="friendly">Friendly</button>
                                <button class="widdx-dropdown-item" data-personality="professional">Professional</button>
                                <button class="widdx-dropdown-item" data-personality="creative">Creative</button>
                                <button class="widdx-dropdown-item" data-personality="analytical">Analytical</button>
                            </div>
                        </div>
                    </div>

                    {{-- Features Toggle --}}
                    <button id="features-toggle" class="widdx-btn widdx-btn-primary text-sm" data-tooltip="Advanced Features">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <span class="hidden sm:inline">Features</span>
                    </button>

                    {{-- More Options --}}
                    <button id="chat-options" class="widdx-btn widdx-btn-ghost p-2" data-tooltip="More Options">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </header>

    {{-- Advanced Features Panel --}}
    <div id="features-panel" class="widdx-features-panel hidden">
        <div class="widdx-container">
            <div class="py-4">
                <div class="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-8 gap-3">
                    <button class="widdx-feature-btn" data-feature="search">
                        <div class="widdx-feature-icon bg-blue-600">🔍</div>
                        <span class="widdx-feature-label">Search</span>
                    </button>
                    <button class="widdx-feature-btn" data-feature="deep-search">
                        <div class="widdx-feature-icon bg-purple-600">🔬</div>
                        <span class="widdx-feature-label">Deep Search</span>
                    </button>
                    <button class="widdx-feature-btn" data-feature="image-gen">
                        <div class="widdx-feature-icon bg-green-600">🎨</div>
                        <span class="widdx-feature-label">Image Gen</span>
                    </button>
                    <button class="widdx-feature-btn" data-feature="think-mode">
                        <div class="widdx-feature-icon bg-yellow-600">🧠</div>
                        <span class="widdx-feature-label">Think Mode</span>
                    </button>
                    <button class="widdx-feature-btn" data-feature="voice">
                        <div class="widdx-feature-icon bg-red-600">🎤</div>
                        <span class="widdx-feature-label">Voice</span>
                    </button>
                    <button class="widdx-feature-btn" data-feature="document">
                        <div class="widdx-feature-icon bg-indigo-600">📄</div>
                        <span class="widdx-feature-label">Documents</span>
                    </button>
                    <button class="widdx-feature-btn" data-feature="vision">
                        <div class="widdx-feature-icon bg-pink-600">👁️</div>
                        <span class="widdx-feature-label">Vision</span>
                    </button>
                    <button class="widdx-feature-btn" data-feature="trends">
                        <div class="widdx-feature-icon bg-teal-600">📈</div>
                        <span class="widdx-feature-label">Trends</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    {{-- Messages Container --}}
    <div class="widdx-messages-container">
        <div class="widdx-container">
            <div id="messages-list" class="widdx-messages-list">
                {{-- Welcome Message --}}
                <div class="widdx-welcome-message">
                    <div class="text-center py-12">
                        <div class="widdx-gradient-primary w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-white font-bold text-2xl">W</span>
                        </div>
                        <h2 class="text-2xl font-bold text-widdx-text-primary mb-2">Welcome to WIDDX AI</h2>
                        <p class="text-widdx-text-secondary mb-6 max-w-md mx-auto">
                            I'm your advanced intelligent assistant. I can help you with search, image generation, analysis, and many other tasks.
                        </p>
                        <div class="flex flex-wrap justify-center gap-2">
                            <button class="widdx-suggestion-btn" data-suggestion="What are your capabilities?">
                                What are your capabilities?
                            </button>
                            <button class="widdx-suggestion-btn" data-suggestion="Search for latest news">
                                Search for latest news
                            </button>
                            <button class="widdx-suggestion-btn" data-suggestion="Generate a beautiful image">
                                Generate a beautiful image
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Typing Indicator --}}
            <div id="typing-indicator" class="widdx-typing-indicator hidden">
                <div class="flex items-start space-x-3 px-4 py-4">
                    <div class="widdx-gradient-primary w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0">
                        <span class="text-white font-bold text-sm">W</span>
                    </div>
                    <div class="bg-widdx-bg-elevated border border-widdx-border-primary rounded-2xl px-4 py-3">
                        <div class="flex space-x-1">
                            <div class="widdx-typing-dot"></div>
                            <div class="widdx-typing-dot"></div>
                            <div class="widdx-typing-dot"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Scroll to Bottom Button --}}
    <button id="scroll-to-bottom" class="widdx-scroll-btn hidden" data-tooltip="Scroll to Bottom">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
    </button>
</main>

<style>
.widdx-chat-area {
    @apply flex flex-col h-full bg-widdx-bg-primary;
}

.widdx-chat-header {
    @apply bg-widdx-bg-secondary border-b border-widdx-border-primary flex-shrink-0;
}

.widdx-features-panel {
    @apply bg-widdx-bg-tertiary border-b border-widdx-border-primary;
}

.widdx-feature-btn {
    @apply flex flex-col items-center justify-center p-3 rounded-xl hover:bg-widdx-bg-hover transition-all duration-200 group;
}

.widdx-feature-btn:hover {
    @apply transform scale-105;
}

.widdx-feature-btn.active {
    @apply bg-widdx-bg-hover ring-2 ring-widdx-primary ring-opacity-50;
}

.widdx-feature-icon {
    @apply w-10 h-10 rounded-lg flex items-center justify-center text-lg mb-2 transition-transform duration-200;
}

.widdx-feature-btn:hover .widdx-feature-icon {
    @apply transform scale-110;
}

.widdx-feature-label {
    @apply text-xs text-widdx-text-secondary group-hover:text-widdx-text-primary transition-colors;
}

.widdx-messages-container {
    @apply flex-1 overflow-y-auto scrollbar-thin;
}

.widdx-messages-list {
    @apply min-h-full py-4;
}

.widdx-welcome-message {
    @apply animate-fade-in;
}

.widdx-suggestion-btn {
    @apply px-4 py-2 bg-widdx-bg-elevated border border-widdx-border-primary rounded-full text-sm text-widdx-text-secondary hover:text-widdx-text-primary hover:bg-widdx-bg-hover transition-all duration-200;
}

.widdx-suggestion-btn:hover {
    @apply transform translateY(-1px) shadow-widdx-md;
}

.widdx-dropdown {
    @apply absolute top-full right-0 mt-2 w-48 bg-widdx-bg-elevated border border-widdx-border-primary rounded-lg shadow-widdx-lg z-50;
}

.widdx-dropdown-item {
    @apply block w-full text-left px-4 py-2 text-sm text-widdx-text-secondary hover:text-widdx-text-primary hover:bg-widdx-bg-hover transition-colors;
}

.widdx-scroll-btn {
    @apply fixed bottom-24 right-6 w-12 h-12 bg-widdx-primary hover:bg-widdx-primary-hover text-white rounded-full shadow-widdx-lg flex items-center justify-center transition-all duration-200 z-40;
}

.widdx-scroll-btn:hover {
    @apply transform scale-110;
}

/* Responsive */
@media (max-width: 1024px) {
    .widdx-chat-area {
        @apply ml-0;
    }
}

@media (max-width: 768px) {
    .widdx-feature-btn {
        @apply p-2;
    }

    .widdx-feature-icon {
        @apply w-8 h-8 text-base;
    }

    .widdx-feature-label {
        @apply text-xs;
    }

    .widdx-scroll-btn {
        @apply bottom-20 right-4 w-10 h-10;
    }
}
</style>
