<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ConversationInsight extends Model
{
    protected $fillable = [
        'chat_session_id',
        'insight_type',
        'insight_content',
        'supporting_data',
        'strength',
        'language',
    ];

    protected $casts = [
        'supporting_data' => 'array',
        'strength' => 'float',
    ];

    /**
     * Get the chat session that owns this insight
     */
    public function chatSession(): BelongsTo
    {
        return $this->belongsTo(ChatSession::class);
    }

    /**
     * Strengthen this insight
     */
    public function strengthen(float $amount = 0.1): void
    {
        $newStrength = min(1.0, $this->strength + $amount);
        $this->update(['strength' => $newStrength]);
    }

    /**
     * Weaken this insight
     */
    public function weaken(float $amount = 0.1): void
    {
        $newStrength = max(0.0, $this->strength - $amount);
        $this->update(['strength' => $newStrength]);
    }

    /**
     * Scope for strong insights
     */
    public function scopeStrong($query, float $threshold = 0.7)
    {
        return $query->where('strength', '>=', $threshold);
    }

    /**
     * Scope for specific insight type
     */
    public function scopeType($query, string $type)
    {
        return $query->where('insight_type', $type);
    }

    /**
     * Scope for specific language
     */
    public function scopeLanguage($query, string $language)
    {
        return $query->where('language', $language);
    }
}
