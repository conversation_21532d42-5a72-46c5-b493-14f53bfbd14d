# WIDDX AI - Image Generation Feature

## Overview

WIDDX AI now supports advanced image generation using Google's Gemini 2.0 Flash Image Generation model. This feature allows users to create high-quality images from text descriptions in both Arabic and English.

## Features

- **Text-to-Image Generation**: Convert detailed text descriptions into high-quality images
- **Multiple Styles**: Support for various artistic styles (natural, vivid, photographic, artistic, digital-art)
- **Flexible Sizing**: Multiple image dimensions (square, landscape, portrait)
- **Quality Options**: Standard and HD quality settings
- **Multilingual Support**: Works with both Arabic and English prompts
- **Smart Storage Management**: Automatic cleanup and optimization
- **Error Handling**: Comprehensive error messages in both languages

## Usage

### Via Chat Interface

Users can generate images by typing natural language commands:

**Arabic Commands:**
- `ارسم منظر طبيعي جميل`
- `اصنع صورة لقطة جميلة`
- `أريد صورة لغروب الشمس`

**English Commands:**
- `generate image of a beautiful landscape`
- `create image of a sunset`
- `draw a mountain scene`

### Via Advanced Modal

Click the "Image Generation" button in the quick actions to access advanced options:

1. **Image Description**: Enter detailed description
2. **Style**: Choose from available styles
3. **Quality**: Select standard or HD
4. **Size**: Pick dimensions (1024x1024, 1792x1024, 1024x1792)

### Via API

```bash
POST /api/features/generate-image
Content-Type: application/json

{
    "prompt": "A beautiful sunset over mountains",
    "style": "natural",
    "quality": "standard",
    "size": "1024x1024",
    "provider": "gemini"
}
```

## Configuration

### Environment Variables

Add to your `.env` file:

```env
GEMINI_API_KEY=your_gemini_api_key_here
```

### Service Configuration

The service is configured in `config/services.php`:

```php
'gemini' => [
    'api_key' => env('GEMINI_API_KEY'),
    'base_url' => 'https://generativelanguage.googleapis.com',
],
```

## Storage Management

### Automatic Cleanup

- Images older than 30 days are automatically cleaned up
- Storage limit: 1GB
- Automatic cleanup triggers at 90% usage

### Manual Management

**View Storage Statistics:**
```bash
php artisan widdx:image-stats
```

**Clean Up Old Images:**
```bash
php artisan widdx:cleanup-images
php artisan widdx:cleanup-images --force
```

## Error Handling

The system provides comprehensive error handling with messages in both Arabic and English:

### Common Error Codes

- `1001`: API key not configured
- `1002`: Empty image data received
- `1003`: Storage failed
- `1004`: No images generated
- `1005`: Invalid prompt
- `1006`: Generation timeout
- `1007`: API error
- `1008`: Prompt too long
- `1009`: Invalid image size
- `1010`: Invalid image style
- `1011`: Invalid image quality
- `1012`: Invalid image count

### Error Response Format

```json
{
    "success": false,
    "error": "English error message",
    "arabic_error": "رسالة الخطأ بالعربية",
    "error_code": 1001,
    "provider": "gemini"
}
```

## Technical Details

### Architecture

- **ImageGenerationService**: Main service for image generation
- **ImageStorageService**: Handles storage and cleanup
- **ImageGenerationException**: Custom exception handling
- **AdvancedFeaturesController**: API endpoints

### File Structure

```
app/
├── Services/
│   ├── ImageGenerationService.php
│   └── ImageStorageService.php
├── Exceptions/
│   └── ImageGenerationException.php
├── Http/Controllers/
│   └── AdvancedFeaturesController.php
└── Console/Commands/
    ├── CleanupGeneratedImages.php
    └── ImageStorageStats.php

storage/app/public/
└── generated_images/
    └── [generated image files]

tests/
├── Unit/
│   ├── ImageGenerationServiceTest.php
│   ├── ImageStorageServiceTest.php
│   └── ImageGenerationExceptionTest.php
└── Feature/
    ├── ImageGenerationApiTest.php
    └── ImageCommandsTest.php
```

### Performance Considerations

- **Timeout**: 120 seconds for image generation
- **File Naming**: Timestamped with unique identifiers
- **Storage Optimization**: Automatic cleanup and compression
- **Error Recovery**: Graceful handling of API failures

## Testing

Run the complete test suite:

```bash
# All image generation tests
php artisan test --filter=ImageGeneration

# Unit tests only
php artisan test tests/Unit/ImageGenerationServiceTest.php
php artisan test tests/Unit/ImageStorageServiceTest.php

# Feature tests only
php artisan test tests/Feature/ImageGenerationApiTest.php

# Test specific features
php artisan widdx:test-features --feature=image
```

## Troubleshooting

### Common Issues

1. **API Key Not Working**
   - Verify `GEMINI_API_KEY` in `.env`
   - Check API key permissions
   - Ensure billing is enabled

2. **Storage Issues**
   - Check disk space
   - Verify permissions on `storage/app/public`
   - Run cleanup: `php artisan widdx:cleanup-images`

3. **Generation Failures**
   - Check prompt length (max 1000 characters)
   - Verify internet connection
   - Check Gemini API status

### Debug Mode

Enable detailed logging by setting `LOG_LEVEL=debug` in `.env`.

## Best Practices

### Prompt Writing

- Be specific and descriptive
- Include style preferences
- Mention lighting, colors, composition
- Avoid copyrighted content references

### Performance

- Use standard quality for faster generation
- Clean up old images regularly
- Monitor storage usage
- Implement rate limiting for production

## Future Enhancements

- Image editing and modification
- Batch image generation
- Custom style training
- Integration with external storage (S3, etc.)
- Image compression and optimization
- User galleries and favorites

## Support

For issues and questions:
- Check the error codes and messages
- Review the logs in `storage/logs/laravel.log`
- Run diagnostic commands
- Contact support with error codes and details
