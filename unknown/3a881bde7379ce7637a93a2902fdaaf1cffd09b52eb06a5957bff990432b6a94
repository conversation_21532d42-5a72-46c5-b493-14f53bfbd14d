<?php

namespace App\Console\Commands;

use App\Services\ImageStorageService;
use Illuminate\Console\Command;

class CleanupGeneratedImages extends Command
{
    protected $signature = 'widdx:cleanup-images {--force : Force cleanup without confirmation}';
    protected $description = 'Clean up old generated images to free storage space';

    private ImageStorageService $storageService;

    public function __construct(ImageStorageService $storageService)
    {
        parent::__construct();
        $this->storageService = $storageService;
    }

    public function handle(): int
    {
        $this->info('🧹 WIDDX AI Image Cleanup');
        $this->info('========================');

        // Get current storage stats
        $stats = $this->storageService->getStorageStats();
        
        $this->info("📊 Current Storage Statistics:");
        $this->info("   Total Files: {$stats['total_files']}");
        $this->info("   Total Size: {$stats['total_size_formatted']}");
        $this->info("   Usage: {$stats['usage_percentage']}%");
        $this->newLine();

        if ($stats['total_files'] === 0) {
            $this->info('✅ No images found. Nothing to clean up.');
            return 0;
        }

        // Ask for confirmation unless forced
        if (!$this->option('force')) {
            if (!$this->confirm('Do you want to proceed with cleanup? This will delete images older than 30 days.')) {
                $this->info('❌ Cleanup cancelled.');
                return 0;
            }
        }

        // Perform cleanup
        $this->info('🔄 Starting cleanup...');
        $result = $this->storageService->cleanupOldImages();

        if ($result['deleted_files'] > 0) {
            $this->info("✅ Cleanup completed successfully!");
            $this->info("   Files deleted: {$result['deleted_files']}");
            $this->info("   Space freed: " . $this->formatFileSize($result['freed_space']));
            
            // Show updated stats
            $newStats = $this->storageService->getStorageStats();
            $this->newLine();
            $this->info("📊 Updated Storage Statistics:");
            $this->info("   Total Files: {$newStats['total_files']}");
            $this->info("   Total Size: {$newStats['total_size_formatted']}");
            $this->info("   Usage: {$newStats['usage_percentage']}%");
        } else {
            $this->info('ℹ️  No old files found to delete.');
        }

        return 0;
    }

    private function formatFileSize(int $bytes): string
    {
        if ($bytes === 0) return '0 Bytes';
        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }
}
