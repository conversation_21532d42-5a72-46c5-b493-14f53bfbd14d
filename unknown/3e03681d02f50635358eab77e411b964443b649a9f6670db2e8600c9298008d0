<?php

namespace App\Console\Commands;

use App\Services\ImageStorageService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class ImageStorageStats extends Command
{
    protected $signature = 'widdx:image-stats';
    protected $description = 'Display image storage statistics and usage information';

    private ImageStorageService $storageService;

    public function __construct(ImageStorageService $storageService)
    {
        parent::__construct();
        $this->storageService = $storageService;
    }

    public function handle(): int
    {
        $this->info('📊 WIDDX AI Image Storage Statistics');
        $this->info('===================================');

        $stats = $this->storageService->getStorageStats();

        // Basic statistics
        $this->info("📁 Storage Overview:");
        $this->info("   Directory: storage/app/public/generated_images");
        $this->info("   Total Files: {$stats['total_files']}");
        $this->info("   Total Size: {$stats['total_size_formatted']}");
        $this->info("   Average File Size: " . $this->formatFileSize($stats['average_file_size']));
        $this->newLine();

        // Storage limits
        $this->info("💾 Storage Limits:");
        $this->info("   Storage Limit: {$stats['storage_limit_formatted']}");
        $this->info("   Current Usage: {$stats['usage_percentage']}%");
        
        // Progress bar for usage
        $this->displayUsageBar($stats['usage_percentage']);
        $this->newLine();

        // Recent files
        $this->displayRecentFiles();

        // Recommendations
        $this->displayRecommendations($stats);

        return 0;
    }

    private function displayUsageBar(float $percentage): void
    {
        $barLength = 50;
        $filledLength = (int) round(($percentage / 100) * $barLength);
        $bar = str_repeat('█', $filledLength) . str_repeat('░', $barLength - $filledLength);
        
        $color = 'green';
        if ($percentage > 80) {
            $color = 'red';
        } elseif ($percentage > 60) {
            $color = 'yellow';
        }
        
        $this->line("   Usage: <fg={$color}>[{$bar}] {$percentage}%</>");
    }

    private function displayRecentFiles(): void
    {
        try {
            $files = Storage::disk('public')->files('generated_images');
            
            if (empty($files)) {
                $this->info("📄 Recent Files: None");
                return;
            }

            // Sort by modification time (newest first)
            usort($files, function ($a, $b) {
                return Storage::disk('public')->lastModified($b) - Storage::disk('public')->lastModified($a);
            });

            $this->info("📄 Recent Files (last 5):");
            
            $recentFiles = array_slice($files, 0, 5);
            foreach ($recentFiles as $file) {
                $size = Storage::disk('public')->size($file);
                $modified = date('Y-m-d H:i:s', Storage::disk('public')->lastModified($file));
                $filename = basename($file);
                
                $this->info("   {$filename} - {$this->formatFileSize($size)} - {$modified}");
            }
            
        } catch (\Exception $e) {
            $this->error("   Error reading files: " . $e->getMessage());
        }
        
        $this->newLine();
    }

    private function displayRecommendations(array $stats): void
    {
        $this->info("💡 Recommendations:");
        
        if ($stats['usage_percentage'] > 90) {
            $this->warn("   ⚠️  Storage is almost full! Run 'php artisan widdx:cleanup-images' immediately.");
        } elseif ($stats['usage_percentage'] > 70) {
            $this->warn("   ⚠️  Storage usage is high. Consider running cleanup soon.");
        } elseif ($stats['total_files'] > 100) {
            $this->info("   ℹ️  You have many generated images. Regular cleanup is recommended.");
        } else {
            $this->info("   ✅ Storage usage is healthy.");
        }
        
        if ($stats['total_files'] > 0) {
            $this->info("   📅 Images older than 30 days are automatically cleaned up.");
            $this->info("   🧹 Run 'php artisan widdx:cleanup-images' to clean up manually.");
        }
    }

    private function formatFileSize(int $bytes): string
    {
        if ($bytes === 0) return '0 Bytes';
        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }
}
