# WIDDX AI - Developer Guide for Image Generation

## Quick Start

### 1. Setup

```bash
# Install dependencies
composer install

# Set up environment
cp .env.example .env

# Add Gemini API key
echo "GEMINI_API_KEY=your_api_key_here" >> .env

# Run migrations
php artisan migrate

# Test the feature
php artisan widdx:test-features --feature=image
```

### 2. Basic Usage

```php
use App\Services\ImageGenerationService;

$service = app(ImageGenerationService::class);

$result = $service->generateImage('A beautiful sunset', [
    'style' => 'natural',
    'quality' => 'standard',
    'size' => '1024x1024'
]);

if ($result['success']) {
    foreach ($result['images'] as $image) {
        echo "Image URL: " . $image['url'] . "\n";
    }
}
```

## Service Classes

### ImageGenerationService

Main service for generating images using Gemini API.

**Key Methods:**
- `generateImage(string $prompt, array $options = []): array`
- `getAvailableOptions(): array`
- `getStorageStats(): array`
- `cleanupOldImages(): array`

**Options:**
```php
$options = [
    'provider' => 'gemini',        // Currently only gemini
    'style' => 'natural',          // natural, vivid, photographic, artistic, digital-art
    'quality' => 'standard',       // standard, hd
    'size' => '1024x1024',        // 1024x1024, 1792x1024, 1024x1792
    'count' => 1,                 // 1-4 images
    'temperature' => 0.8          // 0.0-1.0 creativity
];
```

### ImageStorageService

Handles image storage, optimization, and cleanup.

**Key Methods:**
- `saveImage(string $imageData, string $provider, array $metadata = []): string`
- `getStorageStats(): array`
- `cleanupOldImages(): array`

**Configuration:**
```php
private const MAX_STORAGE_SIZE = 1024 * 1024 * 1024; // 1GB
private const CLEANUP_DAYS = 30; // Delete after 30 days
private const IMAGES_DIRECTORY = 'generated_images';
```

## Exception Handling

### ImageGenerationException

Custom exception class with Arabic translations.

**Static Factory Methods:**
```php
ImageGenerationException::apiKeyNotConfigured()
ImageGenerationException::emptyImageData()
ImageGenerationException::storageFailed()
ImageGenerationException::noImagesGenerated()
ImageGenerationException::invalidPrompt()
ImageGenerationException::timeout()
ImageGenerationException::apiError($message)
```

**Usage:**
```php
try {
    $result = $service->generateImage($prompt);
} catch (ImageGenerationException $e) {
    $errorCode = $e->getErrorCode();
    $arabicMessage = $e->getArabicMessage();
    $englishMessage = $e->getMessage();
    
    // Handle specific error codes
    switch ($errorCode) {
        case 1001:
            // API key not configured
            break;
        case 1004:
            // No images generated
            break;
        // ... other cases
    }
}
```

## API Endpoints

### POST /api/features/generate-image

Generate images via HTTP API.

**Request:**
```json
{
    "prompt": "A beautiful landscape with mountains",
    "style": "natural",
    "quality": "standard",
    "size": "1024x1024",
    "provider": "gemini"
}
```

**Response (Success):**
```json
{
    "success": true,
    "provider": "gemini",
    "prompt": "A beautiful landscape with mountains",
    "images": [
        {
            "url": "http://localhost/storage/generated_images/gemini_2024-01-01_12-00-00_abc123.png",
            "local_path": "generated_images/gemini_2024-01-01_12-00-00_abc123.png",
            "mime_type": "image/png",
            "file_size": 1024000,
            "created_at": "2024-01-01T12:00:00.000000Z"
        }
    ]
}
```

**Response (Error):**
```json
{
    "success": false,
    "error": "Invalid image prompt",
    "arabic_error": "وصف الصورة غير صالح",
    "error_code": 1005,
    "provider": "gemini",
    "images": []
}
```

## Console Commands

### widdx:image-stats

Display storage statistics and usage information.

```bash
php artisan widdx:image-stats
```

### widdx:cleanup-images

Clean up old generated images.

```bash
# Interactive cleanup
php artisan widdx:cleanup-images

# Force cleanup without confirmation
php artisan widdx:cleanup-images --force
```

### widdx:test-features

Test image generation functionality.

```bash
# Test all features
php artisan widdx:test-features

# Test only image generation
php artisan widdx:test-features --feature=image
```

## Frontend Integration

### JavaScript API

```javascript
// Generate image via modal
window.widdxChat.showImageGenerationModal();

// Generate image programmatically
async function generateImage(prompt, options = {}) {
    const response = await fetch('/api/features/generate-image', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            prompt: prompt,
            ...options
        })
    });
    
    return await response.json();
}
```

### Chat Integration

The system automatically detects image generation requests in chat:

**Supported Patterns:**
- Arabic: `ارسم`, `اصنع صورة`, `أريد صورة`
- English: `generate image`, `create image`, `draw`

## Testing

### Unit Tests

```bash
# Test image generation service
php artisan test tests/Unit/ImageGenerationServiceTest.php

# Test storage service
php artisan test tests/Unit/ImageStorageServiceTest.php

# Test exceptions
php artisan test tests/Unit/ImageGenerationExceptionTest.php
```

### Feature Tests

```bash
# Test API endpoints
php artisan test tests/Feature/ImageGenerationApiTest.php

# Test console commands
php artisan test tests/Feature/ImageCommandsTest.php
```

### Mock Testing

```php
// Mock the storage service
$mockStorage = Mockery::mock(ImageStorageService::class);
$mockStorage->shouldReceive('saveImage')
    ->once()
    ->andReturn('path/to/image.png');

$this->app->instance(ImageStorageService::class, $mockStorage);
```

## Configuration

### Environment Variables

```env
# Required
GEMINI_API_KEY=your_gemini_api_key

# Optional
GEMINI_BASE_URL=https://generativelanguage.googleapis.com
IMAGE_GENERATION_TIMEOUT=120
IMAGE_STORAGE_MAX_SIZE=**********
IMAGE_CLEANUP_DAYS=30
```

### Service Provider Registration

Add to `config/app.php` if needed:

```php
'providers' => [
    // ... other providers
    App\Providers\ImageGenerationServiceProvider::class,
],
```

## Deployment

### Production Checklist

- [ ] Set `GEMINI_API_KEY` in production environment
- [ ] Configure storage permissions
- [ ] Set up scheduled cleanup: `php artisan schedule:run`
- [ ] Monitor storage usage
- [ ] Set up error logging and monitoring
- [ ] Configure rate limiting
- [ ] Test API endpoints
- [ ] Verify image URLs are accessible

### Scheduled Tasks

Add to `app/Console/Kernel.php`:

```php
protected function schedule(Schedule $schedule)
{
    // Clean up old images daily
    $schedule->command('widdx:cleanup-images --force')
             ->daily()
             ->at('02:00');
}
```

## Monitoring

### Key Metrics

- Image generation success rate
- Average generation time
- Storage usage
- Error rates by type
- API response times

### Logging

```php
// Custom logging
Log::channel('image_generation')->info('Image generated', [
    'prompt' => $prompt,
    'provider' => 'gemini',
    'file_size' => $fileSize,
    'generation_time' => $generationTime
]);
```

## Security Considerations

- Validate and sanitize all prompts
- Implement rate limiting
- Monitor for inappropriate content
- Secure API keys
- Validate file types and sizes
- Implement user permissions
- Log all generation requests

## Performance Optimization

- Use queues for batch processing
- Implement caching for repeated prompts
- Optimize image storage and delivery
- Monitor API rate limits
- Use CDN for image delivery
- Implement progressive loading
