{{-- WIDDX AI Error Message Component --}}
@props(['type' => 'error', 'message' => ''])

@if($message)
    <div class="widdx-alert widdx-alert-{{ $type }}" role="alert" x-data="{ show: true }" x-show="show" x-init="setTimeout(() => { show = false }, 5000)" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100 transform translate-y-0" x-transition:leave-end="opacity-0 transform -translate-y-2">
        <div class="widdx-alert-content">
            <div class="widdx-alert-icon">
                @if($type === 'error')
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
                    </svg>
                @elseif($type === 'success')
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                    </svg>
                @elseif($type === 'warning')
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                    </svg>
                @else
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd" />
                    </svg>
                @endif
            </div>
            <div class="widdx-alert-message">
                {{ $message }}
            </div>
            <button type="button" class="widdx-alert-close" @click="show = false" aria-label="Close">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
                </svg>
            </button>
        </div>
    </div>

    <style>
    .widdx-alert {
        position: relative;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        border: 1px solid transparent;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        animation: slideIn 0.3s ease-out forwards;
    }

    .widdx-alert-content {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .widdx-alert-icon {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
        width: 1.25rem;
        height: 1.25rem;
    }

    .widdx-alert-message {
        flex: 1;
        font-size: 0.875rem;
        line-height: 1.25rem;
    }

    .widdx-alert-close {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 1.5rem;
        height: 1.5rem;
        margin-left: 0.5rem;
        color: inherit;
        background: transparent;
        border: none;
        border-radius: 0.25rem;
        cursor: pointer;
        opacity: 0.7;
        transition: opacity 0.2s ease-in-out;
    }

    .widdx-alert-close:hover {
        opacity: 1;
        background-color: rgba(0, 0, 0, 0.05);
    }

    .widdx-alert-close:focus {
        outline: 2px solid rgba(99, 102, 241, 0.5);
        outline-offset: 2px;
    }

    /* Alert Types */
    .widdx-alert-error {
        color: #991b1b;
        background-color: #fee2e2;
        border-color: #fecaca;
    }

    .widdx-alert-success {
        color: #065f46;
        background-color: #d1fae5;
        border-color: #a7f3d0;
    }

    .widdx-alert-warning {
        color: #92400e;
        background-color: #fef3c7;
        border-color: #fde68a;
    }

    .widdx-alert-info {
        color: #1e40af;
        background-color: #dbeafe;
        border-color: #bfdbfe;
    }

    /* Animations */
    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-0.5rem);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideOut {
        to {
            opacity: 0;
            transform: translateY(-0.5rem);
        }
    }

    /* Alpine.js transitions */
    [x-cloak] { display: none !important; }
    </style>
@endif
