<?php

namespace App\Policies;

use App\Models\ChatSession;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ChatSessionPolicy
{
    use HandlesAuthorization;

    /**
     * تحديد ما إذا كان المستخدم يمكنه عرض الجلسة
     */
    public function view(User $user, ChatSession $chatSession): bool
    {
        // يمكن للمستخدم رؤية الجلسة إذا كان هو مالكها
        return $user->id === $chatSession->user_id;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه تحديث الجلسة
     */
    public function update(User $user, ChatSession $chatSession): bool
    {
        // يمكن للمستخدم تحديث الجلسة إذا كان هو مالكها
        return $user->id === $chatSession->user_id;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه حذف الجلسة
     */
    public function delete(User $user, ChatSession $chatSession): bool
    {
        // يمكن للمستخدم حذف الجلسة إذا كان هو مالكها
        return $user->id === $chatSession->user_id;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه استعادة الجلسة المحذوفة
     */
    public function restore(User $user, ChatSession $chatSession): bool
    {
        // يمكن للمستخدم استعادة الجلسة إذا كان هو مالكها
        return $user->id === $chatSession->user_id;
    }

    /**
     * تحديد ما إذا كان المستخدم يمكنه حذف الجلسة نهائيًا
     */
    public function forceDelete(User $user, ChatSession $chatSession): bool
    {
        // يمكن للمستخدم حذف الجلسة نهائيًا إذا كان هو مالكها
        return $user->id === $chatSession->user_id;
    }
}
