// WIDDX AI Advanced Input Area Controller

class WiddxInputArea {
    constructor() {
        // Input elements
        this.messageInput = document.getElementById('message-input');
        this.sendButton = document.getElementById('send-button');
        this.charCount = document.getElementById('char-count');
        this.fileInput = document.getElementById('file-input');
        this.imageInput = document.getElementById('image-input');
        this.filePreviewArea = document.getElementById('file-preview-area');
        this.filePreviewList = document.getElementById('file-preview-list');
        this.voiceRecording = document.getElementById('voice-recording');
        this.quickCommands = document.getElementById('quick-commands');
        this.inputSuggestions = document.getElementById('input-suggestions');
        this.notificationContainer = document.getElementById('notification-container');
        
        // State
        this.attachedFiles = [];
        this.isRecording = false;
        this.mediaRecorder = null;
        this.suggestions = [];
        this.currentSuggestionIndex = -1;
        this.notificationTimeouts = new Map();
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupAutoResize();
        this.setupDragAndDrop();
        this.setupKeyboardShortcuts();
        this.setupVoiceRecognition();
        this.loadSuggestions();
    }

    setupEventListeners() {
        // Text input events
        this.messageInput?.addEventListener('input', () => {
            this.handleInputChange();
        });

        this.messageInput?.addEventListener('keydown', (e) => {
            this.handleKeyDown(e);
        });

        this.messageInput?.addEventListener('focus', () => {
            this.showQuickCommands();
        });

        this.messageInput?.addEventListener('blur', () => {
            setTimeout(() => this.hideQuickCommands(), 150);
        });

        // Send button
        this.sendButton?.addEventListener('click', () => {
            this.sendMessage();
        });

        // File upload buttons
        document.getElementById('file-upload-btn')?.addEventListener('click', () => {
            this.fileInput?.click();
        });

        document.getElementById('image-upload-btn')?.addEventListener('click', () => {
            this.imageInput?.click();
        });

        // Voice input button
        document.getElementById('voice-input-btn')?.addEventListener('click', () => {
            this.toggleVoiceRecording();
        });

        // Camera button
        document.getElementById('camera-btn')?.addEventListener('click', () => {
            this.openCamera();
        });

        // File inputs
        this.fileInput?.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files);
        });

        this.imageInput?.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files);
        });

        // Clear files button
        document.getElementById('clear-files')?.addEventListener('click', () => {
            this.clearFiles();
        });

        // Quick commands
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('widdx-quick-cmd')) {
                this.insertCommand(e.target.dataset.command);
            }
        });

        // Stop recording
        document.getElementById('stop-recording')?.addEventListener('click', () => {
            this.stopVoiceRecording();
        });
    }

    setupAutoResize() {
        if (!this.messageInput) return;

        const autoResize = () => {
            this.messageInput.style.height = 'auto';
            this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
        };

        this.messageInput.addEventListener('input', autoResize);
        autoResize(); // Initial resize
    }

    setupDragAndDrop() {
        const inputContainer = document.querySelector('.widdx-input-container');
        if (!inputContainer) return;

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            inputContainer.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            inputContainer.addEventListener(eventName, () => {
                inputContainer.classList.add('drag-over');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            inputContainer.addEventListener(eventName, () => {
                inputContainer.classList.remove('drag-over');
            }, false);
        });

        inputContainer.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            this.handleFileSelect(files);
        }, false);
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+Enter to send
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                this.sendMessage();
            }
            
            // Escape to clear input
            if (e.key === 'Escape' && this.messageInput === document.activeElement) {
                this.clearInput();
            }
            
            // Arrow keys for suggestions navigation
            if (this.suggestions.length > 0 && !this.inputSuggestions.classList.contains('hidden')) {
                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    this.navigateSuggestions(1);
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    this.navigateSuggestions(-1);
                } else if (e.key === 'Tab' && this.currentSuggestionIndex >= 0) {
                    e.preventDefault();
                    this.applySuggestion(this.suggestions[this.currentSuggestionIndex]);
                }
            }
        });
    }

    setupVoiceRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.speechRecognition = new SpeechRecognition();
            this.speechRecognition.continuous = true;
            this.speechRecognition.interimResults = true;
            this.speechRecognition.lang = 'ar-SA';

            this.speechRecognition.onresult = (event) => {
                let finalTranscript = '';
                for (let i = event.resultIndex; i < event.results.length; i++) {
                    if (event.results[i].isFinal) {
                        finalTranscript += event.results[i][0].transcript;
                    }
                }
                if (finalTranscript) {
                    this.messageInput.value += finalTranscript;
                    this.handleInputChange();
                }
            };

            this.speechRecognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                this.stopVoiceRecording();
            };
        }
    }

    loadSuggestions() {
        // Load common suggestions from localStorage or default set
        this.commonSuggestions = [
            'ابحث عن',
            'ولد صورة',
            'اشرح لي',
            'ما هو',
            'كيف يمكنني',
            'أريد أن أعرف',
            'ساعدني في',
            'قم بتحليل'
        ];
    }

    handleInputChange() {
        const value = this.messageInput?.value || '';
        const length = value.length;

        // Update character counter
        if (this.charCount) {
            this.charCount.textContent = length;
            const counter = this.charCount.parentElement;
            counter.classList.toggle('warning', length > 8000);
            counter.classList.toggle('error', length > 9500);
        }

        // Update send button state
        if (this.sendButton) {
            this.sendButton.disabled = length === 0 && this.attachedFiles.length === 0;
        }

        // Show suggestions
        this.showSuggestions(value);

        // Auto-save draft
        this.saveDraft(value);
    }

    handleKeyDown(e) {
        // Handle Enter key
        if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey) {
            e.preventDefault();
            if (this.currentSuggestionIndex >= 0) {
                this.applySuggestion(this.suggestions[this.currentSuggestionIndex]);
            } else {
                this.sendMessage();
            }
        }

        // Handle Tab for command completion
        if (e.key === 'Tab' && this.messageInput.value.startsWith('/')) {
            e.preventDefault();
            this.completeCommand();
        }
    }

    showSuggestions(value) {
        if (!value || value.length < 2) {
            this.hideSuggestions();
            return;
        }

        const filtered = this.commonSuggestions.filter(suggestion =>
            suggestion.includes(value) || value.includes(suggestion.substring(0, 3))
        );

        if (filtered.length === 0) {
            this.hideSuggestions();
            return;
        }

        this.suggestions = filtered.slice(0, 5);
        this.renderSuggestions();
        this.inputSuggestions?.classList.remove('hidden');
    }

    renderSuggestions() {
        const suggestionsList = this.inputSuggestions?.querySelector('.widdx-suggestions-list');
        if (!suggestionsList) return;

        suggestionsList.innerHTML = this.suggestions.map((suggestion, index) => `
            <div class="widdx-suggestion-item ${index === this.currentSuggestionIndex ? 'active' : ''}" 
                 data-index="${index}">
                ${suggestion}
            </div>
        `).join('');

        // Add click handlers
        suggestionsList.querySelectorAll('.widdx-suggestion-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                this.applySuggestion(this.suggestions[index]);
            });
        });
    }

    navigateSuggestions(direction) {
        this.currentSuggestionIndex = Math.max(-1, 
            Math.min(this.suggestions.length - 1, this.currentSuggestionIndex + direction)
        );
        this.renderSuggestions();
    }

    applySuggestion(suggestion) {
        if (this.messageInput) {
            this.messageInput.value = suggestion;
            this.messageInput.focus();
            this.handleInputChange();
        }
        this.hideSuggestions();
    }

    hideSuggestions() {
        this.inputSuggestions?.classList.add('hidden');
        this.currentSuggestionIndex = -1;
        this.suggestions = [];
    }

    showQuickCommands() {
        this.quickCommands?.classList.remove('hidden');
    }

    hideQuickCommands() {
        this.quickCommands?.classList.add('hidden');
    }

    insertCommand(command) {
        if (this.messageInput) {
            this.messageInput.value = command + ' ';
            this.messageInput.focus();
            this.handleInputChange();
        }
    }

    completeCommand() {
        const value = this.messageInput?.value || '';
        const commands = ['/search', '/image', '/think', '/help', '/analyze', '/translate'];
        const match = commands.find(cmd => cmd.startsWith(value));
        
        if (match && this.messageInput) {
            this.messageInput.value = match + ' ';
            this.handleInputChange();
        }
    }

    handleFileSelect(files) {
        Array.from(files).forEach(file => {
            if (this.attachedFiles.length < 10) { // Limit to 10 files
                this.attachedFiles.push(file);
            }
        });
        
        this.renderFilePreview();
        this.handleInputChange(); // Update send button state
    }

    renderFilePreview() {
        if (this.attachedFiles.length === 0) {
            this.filePreviewArea?.classList.add('hidden');
            return;
        }

        this.filePreviewArea?.classList.remove('hidden');
        
        if (this.filePreviewList) {
            this.filePreviewList.innerHTML = this.attachedFiles.map((file, index) => `
                <div class="widdx-file-preview-item">
                    <div class="file-icon">${this.getFileIcon(file)}</div>
                    <div class="file-info">
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${this.formatFileSize(file.size)}</div>
                    </div>
                    <button class="widdx-btn widdx-btn-ghost p-1" onclick="widdxInputArea.removeFile(${index})">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `).join('');
        }
    }

    removeFile(index) {
        this.attachedFiles.splice(index, 1);
        this.renderFilePreview();
        this.handleInputChange();
    }

    clearFiles() {
        this.attachedFiles = [];
        this.renderFilePreview();
        this.handleInputChange();
    }

    getFileIcon(file) {
        const type = file.type.split('/')[0];
        const icons = {
            'image': '🖼️',
            'video': '🎥',
            'audio': '🎵',
            'text': '📄',
            'application': '📎'
        };
        return icons[type] || '📎';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    toggleVoiceRecording() {
        if (this.isRecording) {
            this.stopVoiceRecording();
        } else {
            this.startVoiceRecording();
        }
    }

    startVoiceRecording() {
        if (!this.speechRecognition) {
            this.showNotification('error', 'Voice recording is not supported in this browser');
            return;
        }

        this.isRecording = true;
        this.voiceRecording?.classList.remove('hidden');
        this.speechRecognition.start();
        
        // Update voice button state
        const voiceBtn = document.getElementById('voice-input-btn');
        voiceBtn?.classList.add('active');
    }
    
    stopVoiceRecording() {
        if (!this.speechRecognition) return;
        
        this.isRecording = false;
        this.speechRecognition.stop();
        this.voiceRecording?.classList.add('hidden');
        
        // Update voice button state
        const voiceBtn = document.getElementById('voice-input-btn');
        voiceBtn?.classList.remove('active');
    }
    
    // Show a notification to the user
    showNotification(type, message, duration = 5000) {
        // Create notification element
        const templateId = `${type}-message-template`;
        const template = document.getElementById(templateId);
        
        if (!template) {
            console.error(`Template not found: ${templateId}`);
            return null;
        }
        
        // Clone the template and set the message
        const notification = template.content.cloneNode(true);
        const messageElement = notification.querySelector('.widdx-alert');
        messageElement.querySelector('span').textContent = message;
        
        // Add to container and show with animation
        const container = this.notificationContainer;
        container.appendChild(notification);
        
        // Auto-remove after duration
        const timeoutId = setTimeout(() => {
            messageElement.classList.add('widdx-alert-exit');
            messageElement.addEventListener('animationend', () => {
                messageElement.remove();
                this.notificationTimeouts.delete(timeoutId);
            }, { once: true });
        }, duration);
        
        // Store timeout ID for potential early removal
        this.notificationTimeouts.set(timeoutId, messageElement);
        
        // Click to dismiss
        messageElement.addEventListener('click', (e) => {
            if (e.target.closest('.widdx-alert-close')) {
                clearTimeout(timeoutId);
                messageElement.classList.add('widdx-alert-exit');
                messageElement.addEventListener('animationend', () => {
                    messageElement.remove();
                    this.notificationTimeouts.delete(timeoutId);
                }, { once: true });
            }
        });
        
        return messageElement;
    }
    
    // Clear all notifications
    clearAllNotifications() {
        this.notificationTimeouts.forEach((element, timeoutId) => {
            clearTimeout(timeoutId);
            element.remove();
        });
        this.notificationTimeouts.clear();
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
}

// Initialize input area when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.widdxInputArea = new WiddxInputArea();
    
    // Load draft on page load
    window.widdxInputArea.loadDraft();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WiddxInputArea;
}
