# WIDDX AI - Image Generation Examples

## Basic Examples

### Simple Text-to-Image

**Arabic:**
```
ارسم منظر طبيعي جميل مع جبال وبحيرة
```

**English:**
```
Generate image of a beautiful landscape with mountains and a lake
```

**Result:** High-quality landscape image with natural lighting and composition.

### Detailed Descriptions

**Arabic:**
```
اصنع صورة لغروب الشمس فوق المحيط مع ألوان برتقالية وزهرية في السماء وأمواج هادئة
```

**English:**
```
Create image of a sunset over the ocean with orange and pink colors in the sky and calm waves
```

**Result:** Detailed sunset scene with specified color palette and mood.

## Style Examples

### Natural Style
```
Prompt: "A forest path in autumn"
Style: natural
Quality: standard
Size: 1024x1024
```
**Best for:** Realistic, natural-looking images with authentic lighting and colors.

### Vivid Style
```
Prompt: "A colorful butterfly garden"
Style: vivid
Quality: hd
Size: 1792x1024
```
**Best for:** Bright, saturated colors with enhanced contrast and vibrancy.

### Photographic Style
```
Prompt: "Portrait of an elderly man reading a book"
Style: photographic
Quality: hd
Size: 1024x1792
```
**Best for:** Professional photography look with realistic lighting and depth.

### Artistic Style
```
Prompt: "Abstract representation of music and sound"
Style: artistic
Quality: standard
Size: 1024x1024
```
**Best for:** Creative, artistic interpretations with unique visual elements.

### Digital Art Style
```
Prompt: "Futuristic city with flying cars"
Style: digital-art
Quality: hd
Size: 1792x1024
```
**Best for:** Modern, digital artwork with clean lines and contemporary aesthetics.

## Advanced Prompting Techniques

### Composition and Framing

**Good:**
```
"Close-up portrait of a cat, shallow depth of field, soft lighting, centered composition"
```

**Better:**
```
"Professional close-up portrait of a Persian cat with blue eyes, shallow depth of field, golden hour lighting, rule of thirds composition, blurred background"
```

### Lighting Specifications

```
"A cozy reading nook with warm ambient lighting, soft shadows, and natural window light"
```

```
"Dramatic black and white portrait with strong side lighting and deep shadows"
```

### Color and Mood

```
"Serene beach scene with pastel colors, soft blues and pinks, peaceful morning atmosphere"
```

```
"Vibrant street art mural with bold primary colors, high contrast, energetic mood"
```

## API Usage Examples

### Basic API Call

```javascript
const response = await fetch('/api/features/generate-image', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    },
    body: JSON.stringify({
        prompt: "A peaceful garden with blooming flowers",
        style: "natural",
        quality: "standard",
        size: "1024x1024"
    })
});

const result = await response.json();
if (result.success) {
    console.log('Generated image URL:', result.images[0].url);
}
```

### Advanced API Call with Error Handling

```javascript
async function generateImageWithRetry(prompt, options = {}, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const response = await fetch('/api/features/generate-image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    prompt: prompt,
                    style: options.style || 'natural',
                    quality: options.quality || 'standard',
                    size: options.size || '1024x1024',
                    provider: 'gemini'
                })
            });

            const result = await response.json();
            
            if (result.success) {
                return result;
            } else {
                console.error(`Attempt ${attempt} failed:`, result.arabic_error || result.error);
                if (attempt === maxRetries) {
                    throw new Error(result.arabic_error || result.error);
                }
            }
        } catch (error) {
            console.error(`Attempt ${attempt} error:`, error.message);
            if (attempt === maxRetries) {
                throw error;
            }
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
    }
}

// Usage
try {
    const result = await generateImageWithRetry("Beautiful mountain landscape", {
        style: "photographic",
        quality: "hd",
        size: "1792x1024"
    });
    console.log('Success:', result.images[0].url);
} catch (error) {
    console.error('Failed to generate image:', error.message);
}
```

## PHP Service Examples

### Basic Service Usage

```php
use App\Services\ImageGenerationService;

class ImageController extends Controller
{
    private ImageGenerationService $imageService;

    public function __construct(ImageGenerationService $imageService)
    {
        $this->imageService = $imageService;
    }

    public function generateUserImage(Request $request)
    {
        $validated = $request->validate([
            'prompt' => 'required|string|max:1000',
            'style' => 'in:natural,vivid,photographic,artistic,digital-art',
            'quality' => 'in:standard,hd',
            'size' => 'in:1024x1024,1792x1024,1024x1792'
        ]);

        try {
            $result = $this->imageService->generateImage(
                $validated['prompt'],
                $validated
            );

            if ($result['success']) {
                // Save to user's gallery
                $this->saveToUserGallery(auth()->id(), $result['images']);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Image generated successfully',
                    'images' => $result['images']
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => $result['error']
            ], 400);

        } catch (ImageGenerationException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getArabicMessage(),
                'error_code' => $e->getErrorCode()
            ], 400);
        }
    }
}
```

### Batch Image Generation

```php
public function generateBatchImages(array $prompts, array $options = [])
{
    $results = [];
    $errors = [];

    foreach ($prompts as $index => $prompt) {
        try {
            $result = $this->imageService->generateImage($prompt, $options);
            if ($result['success']) {
                $results[] = $result;
            } else {
                $errors[] = [
                    'index' => $index,
                    'prompt' => $prompt,
                    'error' => $result['error']
                ];
            }
        } catch (Exception $e) {
            $errors[] = [
                'index' => $index,
                'prompt' => $prompt,
                'error' => $e->getMessage()
            ];
        }

        // Add delay to respect rate limits
        sleep(1);
    }

    return [
        'successful' => count($results),
        'failed' => count($errors),
        'results' => $results,
        'errors' => $errors
    ];
}
```

## Chat Integration Examples

### Natural Language Processing

The system automatically detects these patterns:

**Arabic Patterns:**
- `ارسم لي صورة لـ...`
- `اصنع صورة تظهر...`
- `أريد صورة عن...`
- `اعطني صورة لـ...`

**English Patterns:**
- `generate image of...`
- `create image showing...`
- `draw a picture of...`
- `make image with...`

### Chat Response Examples

**User Input:**
```
ارسم منظر لغروب الشمس فوق البحر
```

**System Response:**
```
🎨 تم إنشاء الصورة بنجاح!

📝 الوصف المطلوب: منظر لغروب الشمس فوق البحر
🤖 المولد: Gemini
📊 عدد الصور: 1

✨ تم إنشاء هذه الصورة باستخدام Gemini 2.0 Flash Image Generation - أحدث تقنيات الذكاء الاصطناعي من Google!

🖼️ الصور المُولدة:

[Generated Image Display]
النوع: image/png
الحجم: 876.01 KB
تاريخ الإنشاء: 2024-01-01 12:00:00
عرض بالحجم الكامل
```

## Error Handling Examples

### Common Error Scenarios

**API Key Missing:**
```json
{
    "success": false,
    "error": "Gemini API key not configured",
    "arabic_error": "مفتاح Gemini API غير مُعدّ. يرجى التحقق من إعدادات النظام.",
    "error_code": 1001
}
```

**Invalid Prompt:**
```json
{
    "success": false,
    "error": "Invalid image prompt",
    "arabic_error": "وصف الصورة غير صالح. يرجى إدخال وصف واضح ومفصل.",
    "error_code": 1005
}
```

**Storage Full:**
```json
{
    "success": false,
    "error": "Storage limit exceeded",
    "arabic_error": "تم تجاوز حد التخزين. يرجى المحاولة لاحقاً.",
    "error_code": 1003
}
```

### Error Recovery Strategies

```javascript
function handleImageGenerationError(error) {
    switch (error.error_code) {
        case 1001:
            // API key issue - contact admin
            showAdminAlert('API configuration needed');
            break;
        case 1005:
            // Invalid prompt - help user improve
            showPromptHelp();
            break;
        case 1006:
            // Timeout - suggest retry
            showRetryOption();
            break;
        default:
            // Generic error handling
            showGenericError(error.arabic_error || error.error);
    }
}
```

## Best Practices

### Prompt Writing Tips

1. **Be Specific:**
   - ❌ "A nice picture"
   - ✅ "A serene mountain lake at sunrise with mist and reflection"

2. **Include Style Details:**
   - ❌ "A portrait"
   - ✅ "Professional headshot portrait with soft lighting and neutral background"

3. **Specify Composition:**
   - ❌ "A flower"
   - ✅ "Close-up macro shot of a red rose with water droplets, shallow depth of field"

4. **Add Mood and Atmosphere:**
   - ❌ "A city"
   - ✅ "Bustling city street at night with neon lights and rain reflections, cyberpunk atmosphere"

### Performance Tips

1. **Use appropriate quality settings:**
   - Standard for quick previews
   - HD for final images

2. **Choose optimal sizes:**
   - Square (1024x1024) for social media
   - Landscape (1792x1024) for banners
   - Portrait (1024x1792) for mobile screens

3. **Monitor storage usage:**
   ```bash
   php artisan widdx:image-stats
   ```

4. **Regular cleanup:**
   ```bash
   php artisan widdx:cleanup-images --force
   ```

## Integration Examples

### WordPress Plugin Integration

```php
function widdx_generate_featured_image($post_id, $prompt) {
    $response = wp_remote_post('https://your-widdx-domain.com/api/features/generate-image', [
        'body' => json_encode([
            'prompt' => $prompt,
            'style' => 'photographic',
            'quality' => 'hd',
            'size' => '1792x1024'
        ]),
        'headers' => [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . get_option('widdx_api_token')
        ]
    ]);

    if (!is_wp_error($response)) {
        $data = json_decode(wp_remote_retrieve_body($response), true);
        if ($data['success']) {
            // Download and set as featured image
            $image_url = $data['images'][0]['url'];
            $image_id = media_sideload_image($image_url, $post_id, $prompt, 'id');
            set_post_thumbnail($post_id, $image_id);
        }
    }
}
```

### Discord Bot Integration

```javascript
const { SlashCommandBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('generate-image')
        .setDescription('Generate an image from text')
        .addStringOption(option =>
            option.setName('prompt')
                .setDescription('Describe the image you want')
                .setRequired(true)
        ),
    async execute(interaction) {
        await interaction.deferReply();

        try {
            const response = await fetch('https://your-widdx-domain.com/api/features/generate-image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${process.env.WIDDX_API_TOKEN}`
                },
                body: JSON.stringify({
                    prompt: interaction.options.getString('prompt'),
                    style: 'digital-art',
                    quality: 'standard',
                    size: '1024x1024'
                })
            });

            const data = await response.json();
            
            if (data.success) {
                await interaction.editReply({
                    content: `Generated image for: "${data.prompt}"`,
                    files: [data.images[0].url]
                });
            } else {
                await interaction.editReply(`Error: ${data.error}`);
            }
        } catch (error) {
            await interaction.editReply('Failed to generate image');
        }
    }
};
```
