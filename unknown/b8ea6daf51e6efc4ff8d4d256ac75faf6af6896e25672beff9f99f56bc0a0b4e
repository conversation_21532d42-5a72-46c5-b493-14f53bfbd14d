<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $e
     * @return \Symfony\Component\HttpFoundation\Response
     *
     * @throws \Throwable
     */
    public function render($request, Throwable $e)
    {
        // معالجة أخطاء التحقق من الصحة
        if ($e instanceof ValidationException) {
            return $this->convertValidationExceptionToResponse($e, $request);
        }

        // معالجة أخطاء المصادقة
        if ($e instanceof \Illuminate\Auth\AuthenticationException) {
            return $this->unauthenticated($request, $e);
        }

        // معالجة أخطاء عدم الصلاحية
        if ($e instanceof \Illuminate\Auth\Access\AuthorizationException) {
            return $this->errorResponse('غير مصرح لك بتنفيذ هذا الإجراء', 403);
        }

        // معالجة أخطاء عدم العثور على المورد
        if ($e instanceof \Illuminate\Database\Eloquent\ModelNotFoundException) {
            $modelName = strtolower(class_basename($e->getModel()));
            return $this->errorResponse(
                sprintf('لا يوجد %s بالمعرف المحدد', $modelName),
                404
            );
        }

        // معالجة أخطاء CSRF
        if ($e instanceof \Illuminate\Session\TokenMismatchException) {
            return redirect()->back()->withInput($request->input());
        }

        // إذا كان الطلب يريد استجابة JSON
        if ($this->isFrontend($request)) {
            return $this->errorResponse($e->getMessage(), 500);
        }

        return parent::render($request, $e);
    }

    /**
     * تحويل استثناءات التحقق من الصحة إلى استجابة JSON
     */
    protected function convertValidationExceptionToResponse(ValidationException $e, $request)
    {
        $errors = $e->validator->errors()->getMessages();
        
        if ($this->isFrontend($request)) {
            return $request->ajax() 
                ? response()->json($errors, 422)
                : redirect()->back()->withInput($request->input())->withErrors($errors);
        }

        return $this->errorResponse($errors, 422);
    }

    /**
     * إنشاء استجابة خطأ موحدة
     */
    private function errorResponse($message, $code): JsonResponse
    {
        return response()->json([
            'success' => false,
            'error' => $message,
            'code' => $code
        ], $code);
    }

    /**
     * التحقق مما إذا كان الطلب من واجهة المستخدم
     */
    private function isFrontend($request): bool
    {
        return $request->acceptsHtml() && collect($request->route()->middleware())->contains('web');
    }
}
