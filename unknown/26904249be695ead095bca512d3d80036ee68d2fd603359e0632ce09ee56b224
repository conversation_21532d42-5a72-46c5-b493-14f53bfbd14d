{{-- WIDDX AI Advanced Input Area Component --}}
<footer class="widdx-input-area">
    {{-- Notification Container --}}
    <div id="notification-container" class="widdx-notification-container">
        <!-- Notifications will be inserted here dynamically -->
    </div>
    <div class="widdx-container">
        {{-- File Upload Preview --}}
        <div id="file-preview-area" class="widdx-file-preview hidden">
            <div class="flex items-center space-x-2 p-3 bg-widdx-bg-elevated rounded-lg mb-3">
                <div class="flex-1">
                    <div id="file-preview-list" class="flex flex-wrap gap-2">
                        {{-- File previews will be added here dynamically --}}
                    </div>
                </div>
                <button id="clear-files" class="widdx-btn widdx-btn-ghost p-1" data-tooltip="Clear Files">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        {{-- Main Input Container --}}
        <div class="widdx-input-container">
            {{-- Input Actions Bar --}}
            <div class="widdx-input-actions">
                {{-- File Upload --}}
                <button id="file-upload-btn" class="widdx-input-action" data-tooltip="Upload File">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                    </svg>
                </button>

                {{-- Image Upload --}}
                <button id="image-upload-btn" class="widdx-input-action" data-tooltip="Upload Image">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </button>

                {{-- Voice Input --}}
                <button id="voice-input-btn" class="widdx-input-action" data-tooltip="Voice Input">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                    </svg>
                </button>

                {{-- Camera Capture --}}
                <button id="camera-btn" class="widdx-input-action" data-tooltip="Capture Image">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </button>
            </div>

            {{-- Text Input Area --}}
            <div class="widdx-input-wrapper">
                <div class="widdx-input-field">
                    <textarea
                        id="message-input"
                        placeholder="Type your message here... (Ctrl+Enter to send)"
                        class="widdx-textarea"
                        rows="1"
                        maxlength="10000"
                    ></textarea>

                    {{-- Input Suggestions --}}
                    <div id="input-suggestions" class="widdx-input-suggestions hidden">
                        <div class="widdx-suggestions-list">
                            {{-- Suggestions will be populated dynamically --}}
                        </div>
                    </div>
                </div>

                {{-- Character Counter --}}
                <div class="widdx-char-counter">
                    <span id="char-count">0</span><span class="text-widdx-text-muted">/10000</span>
                </div>
            </div>

            {{-- Send Button --}}
            <div class="widdx-send-area">
                <button
                    id="send-button"
                    class="widdx-send-btn"
                    disabled
                    data-tooltip="Send (Ctrl+Enter)"
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </div>
        </div>

        {{-- Quick Commands --}}
        <div id="quick-commands" class="widdx-quick-commands hidden">
            <div class="flex items-center space-x-2 text-xs text-widdx-text-tertiary">
                <span>Quick commands:</span>
                <button class="widdx-quick-cmd" data-command="/search">Search</button>
                <button class="widdx-quick-cmd" data-command="/image">Generate Image</button>
                <button class="widdx-quick-cmd" data-command="/think">Think Mode</button>
                <button class="widdx-quick-cmd" data-command="/help">Help</button>
            </div>
        </div>

        {{-- Voice Recording Indicator --}}
        <div id="voice-recording" class="widdx-voice-recording hidden">
            <div class="flex items-center justify-center space-x-3 p-4 bg-widdx-bg-elevated rounded-lg">
                <div class="widdx-recording-indicator">
                    <div class="widdx-pulse-dot"></div>
                </div>
                <span class="text-widdx-text-primary">Recording...</span>
                <button id="stop-recording" class="widdx-btn widdx-btn-secondary">
                    Stop
                </button>
            </div>
        </div>
    </div>

    {{-- Hidden File Inputs --}}
    <input type="file" id="file-input" class="hidden" multiple accept="*/*">
    <input type="file" id="image-input" class="hidden" multiple accept="image/*">
</footer>

{{-- Error Message Template --}}
<template id="error-message-template">
    <x-error-message type="error" message="" />
</template>

{{-- Success Message Template --}}
<template id="success-message-template">
    <x-error-message type="success" message="" />
</template>

<style>
/* Notification Container */
.widdx-notification-container {
    position: fixed;
    bottom: 6rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    width: 100%;
    max-width: 32rem;
    padding: 0 1rem;
    pointer-events: none;
}

.widdx-input-area {
    background-color: var(--widdx-bg-secondary);
    border-top: 1px solid var(--widdx-border-primary);
    padding: 1rem;
    flex-shrink: 0;
}

.widdx-file-preview {
    animation: slideDown 0.3s ease-out;
}

.widdx-file-preview-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: var(--widdx-bg-primary);
    border-radius: 0.5rem;
    padding: 0.5rem;
    border: 1px solid var(--widdx-border-primary);
}

.widdx-file-preview-item .file-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 0.25rem;
    background-color: var(--widdx-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    font-weight: 700;
}

.widdx-file-preview-item .file-info {
    flex: 1;
    min-width: 0;
}

.widdx-file-preview-item .file-name {
    font-size: 0.875rem;
    color: var(--widdx-text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.widdx-file-preview-item .file-size {
    font-size: 0.75rem;
    color: var(--widdx-text-tertiary);
}

.widdx-input-container {
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
    background-color: var(--widdx-bg-elevated);
    border: 1px solid var(--widdx-border-primary);
    border-radius: 1rem;
    padding: 0.75rem;
}

.widdx-input-actions {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.widdx-input-action {
    padding: 0.5rem;
    color: var(--widdx-text-secondary);
    border-radius: 0.5rem;
    transition: all 200ms ease-in-out;
}

.widdx-input-action:hover {
    color: var(--widdx-text-primary);
    background-color: var(--widdx-bg-hover);
    transform: scale(1.1);
}

.widdx-input-action.active {
    color: var(--widdx-primary);
    background-color: var(--widdx-bg-hover);
}

.widdx-input-wrapper {
    flex: 1;
    position: relative;
}

.widdx-input-field {
    position: relative;
}

.widdx-textarea {
    width: 100%;
    background: transparent;
    border: none;
    outline: none;
    color: var(--widdx-text-primary);
    resize: none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.5;
}

.widdx-textarea::placeholder {
    color: var(--widdx-text-muted);
}

.widdx-char-counter {
    position: absolute;
    bottom: 0.25rem;
    right: 0.5rem;
    font-size: 0.75rem;
    color: var(--widdx-text-muted);
}

.widdx-char-counter.warning {
    color: var(--widdx-status-warning);
}

.widdx-char-counter.error {
    color: var(--widdx-status-error);
}

.widdx-send-area {
    display: flex;
    align-items: center;
}

.widdx-send-btn {
    width: 2.5rem;
    height: 2.5rem;
    background-color: var(--widdx-primary);
    color: white;
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 200ms ease-in-out;
}

.widdx-send-btn:hover:not(:disabled) {
    background-color: var(--widdx-primary-hover);
}

.widdx-send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.widdx-send-btn:not(:disabled):hover {
    transform: scale(1.1);
    box-shadow: 0 0 10px 0 rgba(var(--widdx-primary-rgb), 0.5);
}

.widdx-send-btn:not(:disabled):active {
    transform: scale(0.95);
}

.widdx-input-suggestions {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    margin-bottom: 0.5rem;
    background-color: var(--widdx-bg-elevated);
    border: 1px solid var(--widdx-border-primary);
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    z-index: 50;
}

.widdx-suggestions-list {
    max-height: 10rem;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--widdx-border-primary) transparent;
}

.widdx-suggestions-list::-webkit-scrollbar {
    width: 6px;
}

.widdx-suggestions-list::-webkit-scrollbar-track {
    background: transparent;
}

.widdx-suggestions-list::-webkit-scrollbar-thumb {
    background-color: var(--widdx-border-primary);
    border-radius: 3px;
}

.widdx-suggestion-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: var(--widdx-text-secondary);
    cursor: pointer;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.widdx-suggestion-item.active {
    background-color: var(--widdx-bg-hover);
    color: var(--widdx-text-primary);
}

.widdx-quick-commands {
    margin-top: 0.5rem;
    animation: fadeIn 0.3s ease-in-out;
}

.widdx-quick-cmd {
    padding: 0.25rem 0.5rem;
    background-color: var(--widdx-bg-elevated);
    border-radius: 0.25rem;
    color: var(--widdx-text-secondary);
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.widdx-quick-cmd:hover {
    background-color: var(--widdx-bg-hover);
    color: var(--widdx-text-primary);
}

.widdx-voice-recording {
    animation: slideUp 0.3s ease-out;
}

.widdx-recording-indicator {
    position: relative;
    width: 1rem;
    height: 1rem;
}

.widdx-pulse-dot {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: var(--widdx-status-error);
    border-radius: 9999px;
    animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.widdx-pulse-dot::after {
    content: '';
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    bottom: 0.25rem;
    left: 0.25rem;
    background-color: var(--widdx-status-error);
    border-radius: 9999px;
}

/* Auto-resize textarea */
.widdx-textarea:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

/* Drag and drop styles */
.widdx-input-container.drag-over {
    border-color: var(--widdx-primary);
    background-color: var(--widdx-bg-hover);
}

.widdx-drag-overlay {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(var(--widdx-primary-rgb), 0.1);
    border: 2px dashed var(--widdx-primary);
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.widdx-drag-overlay-content {
    text-align: center;
    color: var(--widdx-primary);
}

/* Responsive */
@media (max-width: 768px) {
    .widdx-input-container {
        padding: 0.5rem;
        gap: 0.5rem;
    }

    .widdx-input-actions {
        gap: 0.125rem;
    }

    .widdx-input-action {
        padding: 0.375rem;
    }

    .widdx-send-btn {
        width: 2rem;
        height: 2rem;
    }

    .widdx-quick-commands {
        font-size: 0.75rem;
        line-height: 1rem;
    }
}

/* Animations */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
