<div id="image-gen-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-gray-900 rounded-lg w-full max-w-2xl p-6 relative">
        <button id="close-image-modal" class="absolute top-4 right-4 text-gray-400 hover:text-white">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
        
        <h3 class="text-xl font-semibold text-white mb-4">Generate Image</h3>
        
        <div class="mb-4">
            <textarea id="image-prompt" class="w-full bg-gray-800 text-white rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows="3" placeholder="Describe the image you want to generate..."></textarea>
        </div>
        
        <div id="image-preview" class="mb-4 bg-gray-800 rounded-lg p-4 flex items-center justify-center hidden">
            <img id="generated-image" src="" alt="Generated Image" class="max-h-64 max-w-full rounded">
        </div>
        
        <div id="image-loading" class="hidden mb-4 text-center">
            <div class="flex justify-center mb-2">
                <div class="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
            <p class="text-gray-300">Generating your image...</p>
        </div>
        
        <div class="flex justify-end space-x-3">
            <button id="cancel-image" class="px-4 py-2 text-gray-300 hover:text-white">
                Cancel
            </button>
            <button id="generate-image" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50" disabled>
                Generate
            </button>
        </div>
    </div>
</div>
