{{-- WIDDX AI Chat Message Component - Enhanced Design --}}
@props(['type' => 'user', 'message' => '', 'timestamp' => null, 'avatar' => null, 'status' => 'sent'])

<div class="widdx-message widdx-message-{{ $type }} animate-slide-up" data-message-type="{{ $type }}">
    <div class="widdx-message-container">
        {{-- Avatar --}}
        <div class="widdx-message-avatar">
            @if($type === 'user')
                <div class="widdx-avatar widdx-avatar-user">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            @else
                <div class="widdx-avatar widdx-avatar-ai">
                    <div class="widdx-gradient-primary rounded-full w-8 h-8 flex items-center justify-center">
                        <span class="text-white font-bold text-sm">W</span>
                    </div>
                </div>
            @endif
        </div>

        {{-- Message Content --}}
        <div class="widdx-message-content">
            {{-- Message Header --}}
            <div class="widdx-message-header">
                <span class="widdx-message-sender">
                    {{ $type === 'user' ? 'You' : 'WIDDX AI' }}
                </span>
                @if($timestamp)
                    <span class="widdx-message-time">{{ $timestamp }}</span>
                @endif
                @if($type === 'user' && $status)
                    <span class="widdx-message-status widdx-message-status-{{ $status }}">
                        @if($status === 'sending')
                            <svg class="w-3 h-3 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        @elseif($status === 'sent')
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        @elseif($status === 'error')
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        @endif
                    </span>
                @endif
            </div>

            {{-- Message Body --}}
            <div class="widdx-message-body">
                <div class="widdx-message-text">
                    {!! $message !!}
                </div>
            </div>

            {{-- Message Actions --}}
            <div class="widdx-message-actions">
                @if($type === 'ai')
                    <button class="widdx-message-action" data-action="copy" data-tooltip="Copy Message">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </button>
                    <button class="widdx-message-action" data-action="regenerate" data-tooltip="Regenerate">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                    </button>
                    <button class="widdx-message-action" data-action="like" data-tooltip="Like">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                        </svg>
                    </button>
                    <button class="widdx-message-action" data-action="dislike" data-tooltip="Dislike">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018c.163 0 .326.02.485.06L17 4m-7 10v5a2 2 0 002 2h.095c.5 0 .905-.405.905-.905 0-.714.211-1.412.608-2.006L17 13V4m-7 10h2m5-10h2a2 2 0 012 2v6a2 2 0 01-2 2h-2.5"></path>
                        </svg>
                    </button>
                @else
                    <button class="widdx-message-action" data-action="edit" data-tooltip="Edit Message">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                    </button>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
.widdx-message {
    @apply mb-6 opacity-0;
    animation: slideUp 0.3s ease-out forwards;
}

.widdx-message-container {
    @apply flex items-start space-x-3 max-w-4xl mx-auto px-4;
}

.widdx-message-user .widdx-message-container {
    @apply flex-row-reverse space-x-reverse;
}

.widdx-message-avatar {
    @apply flex-shrink-0;
}

.widdx-avatar {
    @apply w-8 h-8 rounded-full flex items-center justify-center;
}

.widdx-avatar-user {
    @apply bg-widdx-bg-elevated text-widdx-text-secondary border border-widdx-border-primary;
}

.widdx-avatar-ai {
    @apply relative;
}

.widdx-message-content {
    @apply flex-1 min-w-0;
}

.widdx-message-user .widdx-message-content {
    @apply text-right;
}

.widdx-message-header {
    @apply flex items-center space-x-2 mb-2 text-xs text-widdx-text-tertiary;
}

.widdx-message-user .widdx-message-header {
    @apply justify-end space-x-reverse;
}

.widdx-message-sender {
    @apply font-medium text-widdx-text-secondary;
}

.widdx-message-time {
    @apply text-widdx-text-muted;
}

.widdx-message-status {
    @apply flex items-center;
}

.widdx-message-status-sending {
    @apply text-widdx-text-tertiary;
}

.widdx-message-status-sent {
    @apply text-widdx-status-success;
}

.widdx-message-status-error {
    @apply text-widdx-status-error;
}

.widdx-message-body {
    @apply relative;
}

.widdx-message-text {
    @apply bg-widdx-bg-elevated border border-widdx-border-primary rounded-2xl px-4 py-3 text-widdx-text-primary leading-relaxed;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.widdx-message-user .widdx-message-text {
    @apply bg-widdx-primary text-white border-widdx-primary;
}

.widdx-message-text pre {
    @apply bg-widdx-bg-primary rounded-lg p-3 mt-2 overflow-x-auto text-sm font-mono;
}

.widdx-message-text code {
    @apply bg-widdx-bg-primary px-1 py-0.5 rounded text-sm font-mono;
}

.widdx-message-text blockquote {
    @apply border-l-4 border-widdx-border-secondary pl-4 italic text-widdx-text-secondary;
}

.widdx-message-text ul, .widdx-message-text ol {
    @apply ml-4 mt-2;
}

.widdx-message-text li {
    @apply mb-1;
}

.widdx-message-text a {
    @apply text-widdx-primary hover:text-widdx-primary-hover underline;
}

.widdx-message-actions {
    @apply flex items-center space-x-1 mt-2 opacity-0 transition-opacity duration-200;
}

.widdx-message:hover .widdx-message-actions {
    @apply opacity-100;
}

.widdx-message-user .widdx-message-actions {
    @apply justify-end space-x-reverse;
}

.widdx-message-action {
    @apply p-1.5 rounded-lg text-widdx-text-tertiary hover:text-widdx-text-primary hover:bg-widdx-bg-hover transition-all duration-200;
}

.widdx-message-action:hover {
    @apply transform scale-110;
}

.widdx-message-action.active {
    @apply text-widdx-primary bg-widdx-bg-hover;
}

/* Typing indicator */
.widdx-typing-indicator {
    @apply flex items-center space-x-2 text-widdx-text-tertiary;
}

.widdx-typing-dot {
    @apply w-2 h-2 bg-widdx-text-tertiary rounded-full animate-pulse;
}

.widdx-typing-dot:nth-child(1) { animation-delay: 0s; }
.widdx-typing-dot:nth-child(2) { animation-delay: 0.2s; }
.widdx-typing-dot:nth-child(3) { animation-delay: 0.4s; }

/* Animations */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .widdx-message-container {
        @apply px-2;
    }

    .widdx-message-text {
        @apply px-3 py-2 text-sm;
    }

    .widdx-message-actions {
        @apply space-x-0.5;
    }

    .widdx-message-action {
        @apply p-1;
    }
}
</style>
